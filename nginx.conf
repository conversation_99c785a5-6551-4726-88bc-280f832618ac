worker_processes 1;

events {
  worker_connections 1024;
}

http {
  include /etc/nginx/mime.types;
  default_type application/octet-stream;
  server_tokens off;
  client_max_body_size 20m;
  client_body_buffer_size 20m;
  keepalive_timeout 65;
  sendfile on;
  tcp_nodelay on;
  ssl_prefer_server_ciphers on;
  ssl_session_cache shared:SSL:2m;
  gzip on;
  gzip_static on;
  gzip_types text/plain application/json application/javascript application/x-javascript text/css application/xml text/javascript;
  gzip_proxied any;
  gzip_vary on;
  gzip_comp_level 6;
  gzip_buffers 16 8k;
  gzip_http_version 1.0;
  server {
    listen 80;
    # 服务器名称
    server_name  localhost;

    location / {
        root /usr/share/nginx/html/home;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }
    location ^~/admin {
      index index.html index.htm;
      alias /usr/share/nginx/html/admin/;
      try_files $uri $uri/ /admin/index.html;
      absolute_redirect off;
    }
    location @rewrites {
       rewrite ^/(admin)/(.+)$ /$1/index.html last;
    }

    location ^~ /api {
      proxy_pass http://localhost:8000/api;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 定义 404 页面
    error_page  404              /404.html;
    location = /404.html {
        root   /usr/share/nginx/html;
    }

    # 定义 50x 页面
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
  }
}
