from enum import Enum

from fastapi_tortoise_crud import BaseModel
from tortoise import fields


class SeasonEnum(Enum):
    Spring = '春期'
    Autumn = '秋期'


class Semester(BaseModel):
    year = fields.SmallIntField(max_length=4, null=True, description='年份：2024')
    season = fields.CharEnumField(SeasonEnum, null=True, index=True, description='春期、秋期')
    semester = fields.CharField(12, null=True, unique=True, index=True, description='2024春期')
    is_default = fields.BooleanField(default=False, description='是否是默认学期,只能有一个')
    categories = fields.ReverseRelation['Category']
    tikus = fields.ReverseRelation['Tiku']

    class Meta:
        table = 'exam-semester'
        table_description = '学期管理'
        unique_together = (('year', 'season'),)


class Category(BaseModel):
    semester = fields.ForeignKeyField('models.Semester', related_name='categories', null=True, description='学期')
    tikus = fields.ReverseRelation['Tiku']
    grade = fields.CharField(32, null=True, description='年级：一年级')
    subject = fields.CharField(32, null=True, description='学科：语文')
    category = fields.CharField(128, null=True, description='题目分类')
    title = fields.CharField(256, null=True, description='标题,semester-grade-subject-category')

    class Meta:
        table = 'exam-category'
        table_description = '分类管理'
        unique_together = (('semester', 'grade', 'subject', 'category'),)


class Tiku(BaseModel):
    # title = fields.CharField(256, description='标题')
    content = fields.TextField(null=True, description='题目内容（html）', )
    abstract = fields.TextField(null=True, description='内容摘要（纯文本）')
    category = fields.ForeignKeyField('models.Category', null=True, related_name='tikus', description='分类')
    cdn_imgs = fields.TextField(null=True, description='cdn图片{id,url},多个以,分割')

    class Meta:
        table = 'exam-tiku'
        table_description = '题库管理'
        unique_together = ('content',)
