[tool]
[tool.pdm]
distribution = false
[[tool.pdm.source]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
#url = "https://pypi.org/simple"
verify_ssl = true
name = "tsinghua"
[tool.pdm.scripts]
start = "uvicorn run:app --host 0.0.0.0 --port 8000"

[project]
name = "api"
version = "0.1.0"
description = "Default template for PDM package"
authors = [
    { name = "moxiaoying", email = "<EMAIL>" },
]
dependencies = [
    "fastapi-tortoise-crud>=0.2.9",
    "uvicorn>=0.30.6",
    "loguru>=0.7.2",
    "bcrypt>=4.0.1",
    "aiofiles>=22.1.0",
    "tortoise-orm==0.21.5",
    "authlib>=1.3.0",
    "passlib>=1.7.4",
    "pydantic-settings>=2.4.0",
    "authx>=1.3.0",
    "itsdangerous>=2.2.0",
    "httpx>=0.28.1",
    "python-multipart>=0.0.20",
    "fastapi-cache2>=0.2.2",
    "redis>=5.2.1",
]
requires-python = ">=3.10"
license = { text = "MIT" }
readme = "README.md"

[dependency-groups]
dev = [
    "pyperclip>=1.9.0",
]
