"""
日志文件配置 参考链接
https://github.com/Delgan/loguru
"""

import os
from datetime import datetime
from loguru import logger

from settings import settings

# 定位到log日志文件
log_path = os.path.join(settings.BASE_PATH, 'logs')

if not os.path.exists(log_path):
    os.mkdir(log_path)

log_path_info = os.path.join(log_path, f'info.log')
log_path_warning = os.path.join(log_path,
                                f'warning.log')
log_path_error = os.path.join(log_path,
                              f'error.log')

# 日志简单配置 文件区分不同级别的日志
logger.add(log_path_info,
           rotation="50 MB",
           encoding='utf-8',
           enqueue=True,
           level='INFO',
           compression='zip',
           retention=5)

logger.add(log_path_warning,
           rotation="50 MB",
           encoding='utf-8',
           enqueue=True,
           level='WARNING',
           compression='zip',
           retention=5)

logger.add(log_path_error,
           rotation="50 MB",
           encoding='utf-8',
           enqueue=True,
           level='ERROR',
           compression='zip',
           retention=5)


def log2str(text):
    return f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-4]}\t{text}"


__all__ = ["logger", "log2str"]
