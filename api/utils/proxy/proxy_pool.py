from random import choice
import time
import json
from datetime import datetime
from loguru import logger
import requests


class IPPool:
    """
    proxy: {
        '**********': {
              "ip": "**********",
              "port": 4220,
              "expire_time": "2021-08-03 21:13:55",
              "outip": "**********"
            }
    }
    """

    def __init__(self):
        self.ip_dict = self.list_to_dict()
        # 记录上次ip使用时间
        self.ip_history = {}
        # 记录ip错误次数
        self.ip_error = {}
        # 最大错误次数
        self.error_max = 5
        # 单个ip间隔多少秒后才能继续提取
        self.time_interval = 5
        # self.ip_list = list(self.ip_dict.keys())

    def get_ip_for_api(self):
        """
        通过芝麻代理api获取数据
        :return:
        """
        url = 'http://webapi.http.zhimacangku.com/getip?neek=cb3d44c2&num=20&type=2&time=4&pro=500000&city=500300&yys=0&port=1&pack=0&ts=1&ys=0&cs=0&lb=1&sb=&pb=4&mr=1&regions='
        ip_list = requests.get(url).json()['data']
        self.ip_dict = {proxy['ip']: proxy for proxy in ip_list}
        self.write_json()

    def record_ip_error(self, ip):
        """
        记录ip错误次数：达到error_max，则去掉当前ip
        :param ip:
        :return:
        """
        error_num = self.ip_error.get(ip, 0) + 1
        if error_num > self.error_max:
            # 去掉当前ip
            logger.error(f'去除无用ip:{ip}')
            self.ip_dict.pop(ip)
            self.write_json()
            pass
        self.ip_error[ip] = error_num

    def check_ip(self, _proxy):
        """
        检测ip是否可用
        :param _proxy:
        :return:
        """
        last_time = self.ip_history.get(_proxy['ip'], 0)
        now_time = int(time.time())
        expire_time = _proxy.get('expire_time', 0)
        invalid_time = datetime.strptime(expire_time, '%Y-%m-%d %H:%M:%S').timestamp()
        if invalid_time - now_time <= 0:
            logger.error(f"ip:{_proxy['ip']}已失效")
            self.ip_dict.pop(_proxy['ip'])
            self.write_json()
            return False
        if now_time - last_time > 5:
            return True
        return False

    def random_ip(self):
        """
        从代理池中随机获取一个ip
        :return:
        """
        while True:
            ip_list = list(self.ip_dict.keys())
            if not ip_list:
                logger.error('IP池已无可用IP，正在重新提取IP')
                self.get_ip_for_api()
                ip_list = list(self.ip_dict.keys())
            proxy_key = choice(ip_list)
            _proxy = self.ip_dict.get(proxy_key)
            if self.check_ip(_proxy):
                break
        self.ip_history[_proxy['ip']] = int(time.time())

        return _proxy

    def generate_proxy(self) -> dict:
        """
        生成代理
        :return:
        """
        ip = self.random_ip()
        proxies = {
            'http': f"http://{ip['ip']}:{ip['port']}",
            'https': f"http://{ip['ip']}:{ip['port']}"
        }
        return proxies

    def list_to_dict(self) -> dict:
        ip_list = self.read_json()
        return {proxy['ip']: proxy for proxy in ip_list}

    def read_json(self):
        try:
            with open('ip.json', 'r') as f:
                content = json.loads(f.read())
            return content['data']
        except FileNotFoundError:
            return []

    def write_json(self):
        with open('ip.json', 'w', encoding='utf-8') as f:
            data = {
                "msg": "0",
                "data": list(self.ip_dict.values())
            }
            content = json.dumps(data)
            f.write(content)


if __name__ == '__main__':
    proxy = IPPool()
    proxy.get_ip_for_api()
    pass
