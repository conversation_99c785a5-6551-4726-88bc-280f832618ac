from tortoise import Tortoise
from fastapi import FastAPI, Depends

from auth.enums import MenuType
from settings import settings
from auth import auth, DependPermission
from auth.models import <PERSON>u, User, Api
from utils.error import ignore_errors


@ignore_errors
async def init_data(app: FastAPI):
    # 初始化数据
    await Tortoise.init(
        config=settings.DATABASE_CONFIG
    )
    # 创建表
    await Tortoise.generate_schemas()
    await init_superuser()
    await init_menus()
    await init_apis(app)
    await Tortoise.close_connections()


async def init_superuser():
    # 如果没有数据，创建一个超级管理员
    if not await User.all().exists():
        print('用户数据为空，正在创建超级管理员...')
        await User.create_one(
            {"username": "admin", "password": auth.get_password_hash("admin123"), "nickname": "超级管理员",
             "status": 1,
             'is_super': True})


async def init_menus():
    menus = await Menu.exists()
    if not menus:
        print('菜单数据为空，正在初始化菜单...')
        parent_menu = await Menu.create(
            menu_type=MenuType.CATALOG,
            name="system",
            title="系统管理",
            path="/system",
            order=1,
            parent_id=0,
            icon="carbon:gui-management",
            hidden=False,
            component="Layout",
            keepalive=False,
            redirect="/system/user",
        )
        children_menu = [
            Menu(
                menu_type=MenuType.MENU,
                name="systemUser",
                title="用户管理",
                path="/system/user",
                order=1,
                parent_id=parent_menu.id,
                icon="material-symbols:person-outline-rounded",
                hidden=False,
                component="/system/user",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="systemRole",
                title="角色管理",
                path="/system/role",
                order=2,
                parent_id=parent_menu.id,
                icon="carbon:user-role",
                hidden=False,
                component="/system/role",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="systemMenu",
                title="菜单管理",
                path="/system/menu",
                order=3,
                parent_id=parent_menu.id,
                icon="material-symbols:list-alt-outline",
                hidden=False,
                component="/system/menu",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="systemApi",
                title="API管理",
                path="/system/api",
                order=4,
                parent_id=parent_menu.id,
                icon="ant-design:api-outlined",
                hidden=False,
                component="/system/api",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="systemLog",
                title="日志管理",
                path="/system/log",
                order=6,
                parent_id=parent_menu.id,
                icon="ph:clipboard-text-bold",
                hidden=False,
                component="/system/log",
                keepalive=False,
            )
        ]
        parent_exam_menu = await Menu.create(
            menu_type=MenuType.CATALOG,
            name="exam",
            title="考试管理",
            path="/exam",
            order=0,
            parent_id=0,
            icon="healthicons:i-exam-multiple-choice-negative",
            hidden=False,
            component="Layout",
            keepalive=False,
            redirect="/exam/tiku",
        )
        children_exam_menu = [
            Menu(
                menu_type=MenuType.MENU,
                name="examTiku",
                title="题库管理",
                path="/exam/tiku",
                order=1,
                parent_id=parent_exam_menu.id,
                icon="material-symbols:person-outline-rounded",
                hidden=False,
                component="/exam/tiku",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="examSemester",
                title="学期管理",
                path="/exam/semester",
                order=1,
                parent_id=parent_exam_menu.id,
                icon="noto:calendar",
                hidden=False,
                component="/exam/semester",
                keepalive=False,
            ),Menu(
                menu_type=MenuType.MENU,
                name="examCategory",
                title="分类管理",
                path="/exam/category",
                order=1,
                parent_id=parent_exam_menu.id,
                icon="ic:baseline-category",
                hidden=False,
                component="/exam/category",
                keepalive=False,
            ),
        ]
        await Menu.bulk_create(children_menu)
        await Menu.bulk_create(children_exam_menu)


async def init_apis(app: FastAPI):
    # apis = await Api.exists()
    # if not apis:
    #     print('Api数据为空，正在初始化API...')
    await Api.refresh_api(app.routes, DependPermission)
    # 初始化开启日志
    app.state.log_paths = await Api.get_path_method_list(log_status=True)
