/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ConfigForm: typeof import('./../src/components/config-form.vue')['default']
    ElBacktop: typeof import('element-plus/es')['ElBacktop']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElImageViewer: typeof import('element-plus/es')['ElImageViewer']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    Question: typeof import('./../src/components/question.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    UnoCSSIconButton: typeof import('./../src/components/Icon/UnoCSSIconButton.vue')['default']
  }
}
