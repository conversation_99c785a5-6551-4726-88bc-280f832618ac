{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "node", "paths": {"~/*": ["src/*"]}, "resolveJsonModule": true, "types": ["vitest", "vite/client", "unplugin-icons/types/vue", "@intlify/unplugin-vue-i18n/messages"], "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "exclude": ["dist", "node_modules"]}