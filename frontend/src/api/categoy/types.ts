/**
 * CategorySchemaFilters
 */
export interface CategorySchemaFilters {
  /**
   * Category，题目分类
   */
  category?: string;
  /**
   * Grade，年级：一年级
   */
  grade?: string;
  semester?: number;
  /**
   * Subject，学科：语文
   */
  subject?: string;

  [property: string]: any;
}

/**
 * CategorySchemaHomeList
 */
export interface CategorySchemaHomeList {
  /**
   * Category，题目分类
   */
  category?: string;
  /**
   * Create Time，创建时间
   */
  create_time?: null;
  /**
   * Grade，年级：一年级
   */
  grade?: string;
  /**
   * Id，主键
   */
  id?: number;
  /**
   * Subject，学科：语文
   */
  subject?: string;
  /**
   * Update Time，更新时间
   */
  update_time?: null;
  [property: string]: any;
}
