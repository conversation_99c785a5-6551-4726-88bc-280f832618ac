/**
 * CategorySchemaFilters
 */
export interface TikuSchemaFilters {
  /**
   * Category，题目分类
   */
  category?: string;
  /**
   * Grade，年级：一年级
   */
  grade?: string;
  semester?: null;
  /**
   * Subject，学科：语文
   */
  subject?: string;

  [property: string]: any;
}

/**
 * BaseApiOut_TikuSchemaRead_
 */
export interface BaseApiOutTikuSchemaRead {
  /**
   * Code
   */
  code?: number;
  data?: null | TikuSchemaRead;
  /**
   * Message
   */
  message?: string;

  [property: string]: any;
}

/**
 * TikuSchemaRead
 */
export interface TikuSchemaRead {
  /**
   * Abstract，内容摘要（纯文本）
   */
  abstract?: string;
  /**
   * 分类
   */
  category: Category;
  /**
   * Content，题目内容（html）
   */
  content?: string;
  /**
   * Create Time，创建时间
   */
  create_time?: null;
  /**
   * Id，主键
   */
  id?: number;
  /**
   * Status，状态:True=启用,False=禁用
   */
  status?: boolean | null;
  /**
   * Update Time，更新时间
   */
  update_time?: null;

  [property: string]: any;
}

/**
 * 分类
 *
 * CategorySchemaRead
 */
export interface Category {
  /**
   * Category，题目分类
   */
  category?: string;
  /**
   * Create Time，创建时间
   */
  create_time?: null;
  /**
   * Grade，年级：一年级
   */
  grade?: string;
  /**
   * Id，主键
   */
  id?: number;
  /**
   * 学期
   */
  semester: Semester;
  /**
   * Status，状态:True=启用,False=禁用
   */
  status?: boolean | null;
  /**
   * Subject，学科：语文
   */
  subject?: string;
  /**
   * Title，标题,semester-grade-subject-category
   */
  title?: string;
  /**
   * Update Time，更新时间
   */
  update_time?: null;

  [property: string]: any;
}

/**
 * 学期
 *
 * SemesterSchemaRelation
 */
export interface Semester {
  /**
   * Id，主键
   */
  id?: number;
  /**
   * Semester，2024春期
   */
  semester?: null;

  [property: string]: any;
}
