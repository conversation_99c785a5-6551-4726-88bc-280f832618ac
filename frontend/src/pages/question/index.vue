<script setup lang="ts">
import MQuestion from "~/components/question.vue";

defineOptions({
  name: "Home",
});
const tikuStore = useTikuStore();
tikuStore.randQuestion();

// 监听题目是否发生变化
// watch(
//   () => [tikuStore.config.category],
//   ([category]) => {
//     // console.log("监听改变：", category);
//     category && tikuStore.randQuestion();
//     // 不放在randQuestion改变字体，防止刷新时字体又被设置为默认值
//   },
// );
</script>

<template>
  <m-question />
</template>

<style scoped></style>
