<script setup lang="ts">
import MConfigForm from "~/components/config-form.vue";

defineOptions({
  name: "MConfig",
});
const dialog = ref(false);
const router = useRouter();
const { width } = useWindowSize();

function handleClose() {
  dialog.value = false;
}

function handleClick() {
  dialog.value = true;
}

function resetConfig() {
  dialog.value = false;
  router.push({ name: "Home" }).then(() => {
    ElMessage.success("配置已重置");
  });
}
</script>

<template>
  <UnoCSSIconButton
    icon="i-tabler:settings"
    @click="handleClick"
  ></UnoCSSIconButton>
  <el-drawer
    ref="drawerRef"
    v-model="dialog"
    title="配置"
    :append-to-body="true"
    :before-close="handleClose"
    direction="rtl"
    :size="width <= 800 ? '60%' : '30%'"
  >
    <template #default>
      <div>
        <m-config-form
          @submit-config="handleClose"
          @reset-config="resetConfig"
        />
      </div>
    </template>
    <template #footer></template>
  </el-drawer>
</template>

<style scoped></style>
