<script setup lang="ts">
import { useRouter } from "vue-router";
import { FormInstance } from "element-plus";

defineOptions({
  name: "MConfigForm",
});

const emits = defineEmits(["resetConfig", "submitConfig"]);
const tikuStore = useTikuStore();
const router = useRouter();
const ruleFormRef = ref<FormInstance>();
const rules = reactive({
  semester: [{ required: true, message: "请选择学期", trigger: "blur" }],
  grade: [{ required: true, message: "请选择年级", trigger: "blur" }],
  subject: [{ required: true, message: "请选择学科", trigger: "blur" }],
  category: [{ required: true, message: "请选择分类", trigger: "blur" }],
});
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const data = await tikuStore.getQuestionList();
      if (data.length === 0) {
        ElMessage.error("没有题目,请检查配置");
        return new Error("题目为空");
      }
      ElMessage.success("提交成功");
      tikuStore.randQuestion();
      // debugger;
      await router.push({ name: "Question" });
      emits("submitConfig");
    } else {
      ElMessage.error("提交失败,请检查配置");
    }
  });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  tikuStore.resetConfig();
  tikuStore.resetQuestion();
  emits("resetConfig");
  // router.push({ name: "Home" });
  // ElMessage("aaa");
};
// 防止有人瞎搞
watch(
  () => [
    tikuStore.config.grade,
    tikuStore.config.subject,
    tikuStore.config.semester,
  ],
  async () => {
    if (
      tikuStore.config.grade &&
      tikuStore.config.semester &&
      tikuStore.config.subject
    ) {
      await tikuStore.getCategoryList({
        grade: tikuStore.config.grade,
        subject: tikuStore.config.subject,
        semester: tikuStore.config.semester,
      });
      tikuStore.config.category = "";
    }
  },
  {
    immediate: true,
  },
);
</script>

<template>
  <div class="m-auto min-w-30">
    <el-form
      size="default"
      ref="ruleFormRef"
      :rules="rules"
      :model="tikuStore.config"
    >
      <el-form-item label="学期" prop="semester">
        <el-select v-model="tikuStore.config.semester" placeholder="请选择学期">
          <el-option
            :label="semester.semester"
            :value="semester.id"
            :key="semester.id"
            v-for="semester in tikuStore.semesterList"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年级" prop="grade">
        <el-select v-model="tikuStore.config.grade" placeholder="请选择年级">
          <el-option label="一年级" value="一年级" />
          <el-option label="二年级" value="二年级" />
        </el-select>
      </el-form-item>
      <el-form-item label="学科" prop="subject">
        <el-select v-model="tikuStore.config.subject" placeholder="请选择学科">
          <el-option label="语文" value="语文" />
          <el-option label="数学" value="数学" />
        </el-select>
      </el-form-item>
      <el-form-item label="分类" prop="category">
        <el-select v-model="tikuStore.config.category" placeholder="请选择分类">
          <el-option
            v-for="category in tikuStore.categoryList"
            :label="category.category"
            :value="category.category"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm(ruleFormRef)">
          确定
        </el-button>
        <el-button type="danger" @click="resetForm(ruleFormRef)"
          >重置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss"></style>
