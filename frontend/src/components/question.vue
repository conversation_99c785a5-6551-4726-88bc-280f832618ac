<script setup lang="ts">
defineOptions({
  name: "MQuestion",
});
const tikuStore = useTikuStore();
const imgPreviewList = ref([]);
const isShowPreview = ref(false);

function showPreview({ target }: PointerEvent) {
  // debugger;
  // 如果是图片
  if (target?.nodeName === "IMG") {
    imgPreviewList.value = [target?.src];
    isShowPreview.value = true;
  }
}

function closePreview() {
  isShowPreview.value = false;
  imgPreviewList.value = [];
}
</script>

<template>
  <div class="flex flex-col">
    <h1
      class="divider font-700 cursor-pointer"
      text="center 4xl secondary"
      p="t-6 b-1"
      m="b-8"
      md="text-6xl pb-4 mb-15"
    >
      {{ tikuStore.currentQuestion?.category?.category }}
    </h1>
    <div
      class="px-4"
      md="px-8"
      @click="showPreview"
      v-html="tikuStore.currentQuestion?.content"
    ></div>
    <el-image-viewer
      v-if="isShowPreview"
      @close="closePreview"
      :zoom-rate="1.2"
      :url-list="imgPreviewList"
    />
  </div>
</template>

<style scoped lang="scss"></style>
