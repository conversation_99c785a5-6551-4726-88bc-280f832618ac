import { defineStore } from "pinia";
import { TikuSchemaFilters, TikuSchemaRead } from "~/api/tiku/types";
import { getTikuList } from "~/api/tiku";
import { SemesterSchemaHomeList } from "~/api/semester/types";
import {
  CategorySchemaFilters,
  CategorySchemaHomeList,
} from "~/api/categoy/types";
import { getCategoryList } from "~/api/categoy";
import { getSemesterList } from "~/api/semester";
import { merge } from "lodash-es";

export const useTikuStore = defineStore("category", {
  state: () => {
    return {
      config: {
        semester: null as number | null,
        grade: "一年级",
        subject: "语文",
        category: "",
      },
      currentQuestion: {} as TikuSchemaRead,
      questionList: [] as TikuSchemaRead[],
      semesterList: [] as SemesterSchemaHomeList[],
      categoryList: [] as CategorySchemaHomeList[],
    };
  },

  actions: {
    async getQuestionList(
      filters: TikuSchemaFilters = {},
    ): Promise<TikuSchemaRead[]> {
      const params = merge(this.config, filters);
      this.questionList = await getTikuList(params);
      return this.questionList;
    },
    async getCategoryList(
      filters: CategorySchemaFilters = {},
    ): Promise<CategorySchemaHomeList[]> {
      this.categoryList = await getCategoryList(filters);
      return this.categoryList;
    },
    async getSemesterList(): Promise<SemesterSchemaHomeList[]> {
      this.semesterList = await getSemesterList();
      // 过滤出is_default为true的唯一一个元素
      // this.semesterList = this.semesterList.filter(item => item.is_default)[0].id;
      const item = this.semesterList.filter((item) => item.is_default);
      // @ts-ignore
      this.config.semester = item?.length > 0 ? item[0].id : null;

      return this.semesterList;
    },
    randQuestion(): TikuSchemaRead {
      // 如果只有一个元素，就直接返回
      if (this.questionList.length === 1) {
        this.currentQuestion = this.questionList[0];
        return this.currentQuestion;
      }
      // 过滤掉当前题目
      const items = this.questionList.filter(
        (item) => item.id !== this.currentQuestion?.id,
      );
      const randomIndex = Math.floor(Math.random() * items.length);
      this.currentQuestion = items[randomIndex];
      return this.currentQuestion;
    },
    resetConfig() {
      this.config = merge(this.config, {
        grade: "一年级",
        subject: "语文",
        category: "",
      });
    },
    resetQuestion() {
      this.questionList = [];
      this.currentQuestion = {} as TikuSchemaRead;
      this.categoryList = [];
    },
  },
  persist: false,
});
