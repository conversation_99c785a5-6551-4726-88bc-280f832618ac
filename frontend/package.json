{"name": "exam", "type": "module", "version": "0.1.0", "description": "红旗小学无纸化测试", "keywords": ["BootVue", "boot-vue", "vite", "unocss", "fast", "boot"], "scripts": {"dev": "vite", "build": "vite build", "typecheck": "vue-tsc --noEmit", "preview": "vite preview"}, "dependencies": {"@kirklin/logger": "^0.0.2", "@kirklin/reset-css": "^0.0.3", "@vueuse/core": "^10.7.1", "axios": "1.7.4", "daisyui": "^3.9.4", "element-plus": "^2.4.4", "iso-639-1": "^3.1.0", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "vue": "^3.5.13", "vue-i18n": "^11.0.1", "vue-router": "^4.2.5"}, "devDependencies": {"@iconify-json/mdi": "^1.1.63", "@iconify-json/tabler": "^1.1.102", "@intlify/unplugin-vue-i18n": "^2.0.0", "@types/node": "^20.10.6", "@types/nprogress": "^0.2.3", "@unocss/eslint-config": "^0.58.2", "@vitejs/plugin-vue": "^5.0.2", "prettier": "^3.1.1", "typescript": "^5.3.3", "unocss": "^0.58.2", "unocss-preset-chinese": "^0.3.0", "unocss-preset-daisy": "^7.0.0", "unocss-preset-ease": "^0.0.3", "unplugin-auto-import": "^0.17.3", "unplugin-icons": "^0.18.1", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.10", "vite-plugin-pwa": "^0.17.4", "vite-plugin-vue-devtools": "^7.0.2", "vitest": "^1.1.0", "vue-tsc": "^1.8.27"}}