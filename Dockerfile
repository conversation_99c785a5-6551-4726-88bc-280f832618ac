# 构建前端页面的阶段
FROM node:20.2.0-alpine3.18 AS home-frontend-builder
WORKDIR /app
COPY frontend/package*.json /app/
RUN npm config set registry https://registry.npmmirror.com && npm install -g pnpm && pnpm install
COPY frontend /app
RUN pnpm build

# 阶段二：构建前端页面 - Admin
FROM node:20.2.0-alpine3.18 AS admin-frontend-builder
WORKDIR /app
COPY admin /app
RUN npm config set registry https://registry.npmmirror.com && npm install -g pnpm && pnpm install && pnpm build

# 使用Alpine作为基础镜像并指定Python 3.12版本
FROM python:3.12.6-alpine
# 设置工作目录
WORKDIR /app
# 更新软件包列表并安装依赖
RUN apk update && apk add --no-cache nginx redis && pip install --no-cache-dir pdm supervisor && rm -rf /var/cache/apk/*

COPY api/pdm.lock .
RUN pdm sync --prod

# 复制项目文件到容器中
COPY api /app
# 复制第一个前端项目的静态文件到Nginx目录
COPY --from=home-frontend-builder /app/dist /usr/share/nginx/html/home
# 复制第二个前端项目的静态文件到Nginx目录
COPY --from=admin-frontend-builder /app/dist /usr/share/nginx/html/admin

# 复制Nginx配置文件
COPY ./nginx.conf /etc/nginx/nginx.conf

# 复制supervisor配置文件
COPY ./supervisord.conf /etc/supervisord.conf

# 暴露Nginx端口
EXPOSE 80

# 启动supervisor
CMD ["supervisord", "-c", "/etc/supervisord.conf"]
