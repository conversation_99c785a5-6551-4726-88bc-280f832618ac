/** 白屏阶段会执行的 CSS 加载动画 */

#app-loading {
  position: relative;
  top: 45vh;
  margin: 0 auto;
  color: #409eff;
  font-size: 12px;
}

#app-loading,
#app-loading::before,
#app-loading::after {
  width: 2em;
  height: 2em;
  border-radius: 50%;
  animation: 2s ease-in-out infinite app-loading-animation;
}

#app-loading::before,
#app-loading::after {
  content: "";
  position: absolute;
}

#app-loading::before {
  left: -4em;
  animation-delay: -0.2s;
}

#app-loading::after {
  left: 4em;
  animation-delay: 0.2s;
}

@keyframes app-loading-animation {
  0%,
  80%,
  100% {
    box-shadow: 0 2em 0 -2em;
  }
  40% {
    box-shadow: 0 2em 0 0;
  }
}
