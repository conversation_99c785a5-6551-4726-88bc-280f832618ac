lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@element-plus/icons-vue':
        specifier: 2.3.1
        version: 2.3.1(vue@3.4.21(typescript@5.4.3))
      '@fast-crud/fast-crud':
        specifier: ^1.21.2
        version: 1.21.2(vue@3.4.21(typescript@5.4.3))
      '@fast-crud/fast-extends':
        specifier: ^1.21.2
        version: 1.21.2(aws-crt@1.21.2)(vue@3.4.21(typescript@5.4.3))
      '@fast-crud/ui-element':
        specifier: ^1.21.2
        version: 1.21.2
      '@fast-crud/ui-interface':
        specifier: ^1.21.2
        version: 1.21.2
      '@tiptap/core':
        specifier: ^2.11.5
        version: 2.11.5(@tiptap/pm@2.11.5)
      '@tiptap/starter-kit':
        specifier: ^2.11.5
        version: 2.11.5
      '@tiptap/vue-3':
        specifier: ^2.11.5
        version: 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)(vue@3.4.21(typescript@5.4.3))
      '@vueuse/core':
        specifier: ^10.9.0
        version: 10.9.0(vue@3.4.21(typescript@5.4.3))
      '@wangeditor-next/editor-for-vue':
        specifier: ^5.1.14
        version: 5.1.14(@wangeditor-next/editor@5.6.27)(vue@3.4.21(typescript@5.4.3))
      add:
        specifier: ^2.0.6
        version: 2.0.6
      axios:
        specifier: 1.6.8
        version: 1.6.8
      date-fns:
        specifier: ^4.1.0
        version: 4.1.0
      date-fns-tz:
        specifier: ^3.2.0
        version: 3.2.0(date-fns@4.1.0)
      dayjs:
        specifier: 1.11.10
        version: 1.11.10
      element-plus:
        specifier: 2.6.2
        version: 2.6.2(vue@3.4.21(typescript@5.4.3))
      js-cookie:
        specifier: 3.0.5
        version: 3.0.5
      lodash-es:
        specifier: 4.17.21
        version: 4.17.21
      mitt:
        specifier: 3.0.1
        version: 3.0.1
      normalize.css:
        specifier: 8.0.1
        version: 8.0.1
      nprogress:
        specifier: 0.2.0
        version: 0.2.0
      path-browserify:
        specifier: 1.0.1
        version: 1.0.1
      path-to-regexp:
        specifier: 6.2.1
        version: 6.2.1
      pinia:
        specifier: 2.1.7
        version: 2.1.7(typescript@5.4.3)(vue@3.4.21(typescript@5.4.3))
      pinyin-pro:
        specifier: ^3.26.0
        version: 3.26.0
      screenfull:
        specifier: 6.0.2
        version: 6.0.2
      vue:
        specifier: 3.4.21
        version: 3.4.21(typescript@5.4.3)
      vue-router:
        specifier: 4.3.0
        version: 4.3.0(vue@3.4.21(typescript@5.4.3))
    devDependencies:
      '@iconify/vue':
        specifier: ^4.1.2
        version: 4.1.2(vue@3.4.21(typescript@5.4.3))
      '@types/js-cookie':
        specifier: 3.0.6
        version: 3.0.6
      '@types/lodash-es':
        specifier: 4.17.12
        version: 4.17.12
      '@types/node':
        specifier: 20.11.30
        version: 20.11.30
      '@types/nprogress':
        specifier: 0.2.3
        version: 0.2.3
      '@types/path-browserify':
        specifier: 1.0.2
        version: 1.0.2
      '@typescript-eslint/eslint-plugin':
        specifier: 7.3.1
        version: 7.3.1(@typescript-eslint/parser@7.3.1(eslint@8.57.0)(typescript@5.4.3))(eslint@8.57.0)(typescript@5.4.3)
      '@typescript-eslint/parser':
        specifier: 7.3.1
        version: 7.3.1(eslint@8.57.0)(typescript@5.4.3)
      '@vitejs/plugin-vue':
        specifier: 5.0.4
        version: 5.0.4(vite@5.3.4(@types/node@20.11.30)(sass@1.72.0))(vue@3.4.21(typescript@5.4.3))
      '@vitejs/plugin-vue-jsx':
        specifier: 3.1.0
        version: 3.1.0(vite@5.3.4(@types/node@20.11.30)(sass@1.72.0))(vue@3.4.21(typescript@5.4.3))
      '@vue/test-utils':
        specifier: 2.4.5
        version: 2.4.5
      jsdom:
        specifier: 24.0.0
        version: 24.0.0
      lint-staged:
        specifier: 15.2.2
        version: 15.2.2
      prettier:
        specifier: 3.2.5
        version: 3.2.5
      sass:
        specifier: 1.72.0
        version: 1.72.0
      typescript:
        specifier: 5.4.3
        version: 5.4.3
      unocss:
        specifier: 0.58.6
        version: 0.58.6(postcss@5.2.18)(rollup@4.13.0)(vite@5.3.4(@types/node@20.11.30)(sass@1.72.0))
      vite:
        specifier: 5.3.4
        version: 5.3.4(@types/node@20.11.30)(sass@1.72.0)
      vite-plugin-svg-icons:
        specifier: 2.0.1
        version: 2.0.1(vite@5.3.4(@types/node@20.11.30)(sass@1.72.0))
      vite-svg-loader:
        specifier: 5.1.0
        version: 5.1.0(vue@3.4.21(typescript@5.4.3))
      vitest:
        specifier: 1.4.0
        version: 1.4.0(@types/node@20.11.30)(jsdom@24.0.0)(sass@1.72.0)
      vue-tsc:
        specifier: 2.0.7
        version: 2.0.7(typescript@5.4.3)

packages:

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@antfu/install-pkg@0.1.1':
    resolution: {integrity: sha512-LyB/8+bSfa0DFGC06zpCEfs89/XoWZwws5ygEa5D+Xsm3OfI+aXQ86VgVG7Acyef+rSZ5HE7J8rrxzrQeM3PjQ==}

  '@antfu/utils@0.7.7':
    resolution: {integrity: sha512-gFPqTG7otEJ8uP6wrhDv6mqwGWYZKNvAcCq6u9hOj0c+IKCEsY4L1oC9trPq2SaWIzAfHvqfBDxF591JkMf+kg==}

  '@aws-crypto/crc32@3.0.0':
    resolution: {integrity: sha512-IzSgsrxUcsrejQbPVilIKy16kAT52EwB6zSaI+M3xxIhKh5+aldEyvI+z6erM7TCLB2BJsFrtHjp6/4/sr+3dA==}

  '@aws-crypto/crc32c@3.0.0':
    resolution: {integrity: sha512-ENNPPManmnVJ4BTXlOjAgD7URidbAznURqD0KvfREyc4o20DPYdEldU1f5cQ7Jbj0CJJSPaMIk/9ZshdB3210w==}

  '@aws-crypto/ie11-detection@3.0.0':
    resolution: {integrity: sha512-341lBBkiY1DfDNKai/wXM3aujNBkXR7tq1URPQDL9wi3AUbI80NR74uF1TXHMm7po1AcnFk8iu2S2IeU/+/A+Q==}

  '@aws-crypto/sha1-browser@3.0.0':
    resolution: {integrity: sha512-NJth5c997GLHs6nOYTzFKTbYdMNA6/1XlKVgnZoaZcQ7z7UJlOgj2JdbHE8tiYLS3fzXNCguct77SPGat2raSw==}

  '@aws-crypto/sha256-browser@3.0.0':
    resolution: {integrity: sha512-8VLmW2B+gjFbU5uMeqtQM6Nj0/F1bro80xQXCW6CQBWgosFWXTx77aeOF5CAIAmbOK64SdMBJdNr6J41yP5mvQ==}

  '@aws-crypto/sha256-js@3.0.0':
    resolution: {integrity: sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==}

  '@aws-crypto/supports-web-crypto@3.0.0':
    resolution: {integrity: sha512-06hBdMwUAb2WFTuGG73LSC0wfPu93xWwo5vL2et9eymgmu3Id5vFAHBbajVWiGhPO37qcsdCap/FqXvJGJWPIg==}

  '@aws-crypto/util@3.0.0':
    resolution: {integrity: sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==}

  '@aws-sdk/client-s3@3.592.0':
    resolution: {integrity: sha512-abn1XYk9HW2nXIvyD6ldwrNcF5/7a2p06OSWEr7zVTo954kArg8N0yTsy83ezznEHZfaZpdZn/DLDl2GxrE1Xw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/client-sso-oidc@3.592.0':
    resolution: {integrity: sha512-11Zvm8nm0s/UF3XCjzFRpQU+8FFVW5rcr3BHfnH6xAe5JEoN6bJN/n+wOfnElnjek+90hh+Qc7s141AMrCjiiw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/client-sso@3.592.0':
    resolution: {integrity: sha512-w+SuW47jQqvOC7fonyjFjsOh3yjqJ+VpWdVrmrl0E/KryBE7ho/Wn991Buf/EiHHeJikoWgHsAIPkBH29+ntdA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/client-sts@3.592.0':
    resolution: {integrity: sha512-KUrOdszZfcrlpKr4dpdkGibZ/qq3Lnfu1rjv1U+V1QJQ9OuMo9J3sDWpWV9tigNqY0aGllarWH5cJbz9868W/w==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/core@3.592.0':
    resolution: {integrity: sha512-gLPMXR/HXDP+9gXAt58t7gaMTvRts9i6Q7NMISpkGF54wehskl5WGrbdtHJFylrlJ5BQo3XVY6i661o+EuR1wg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-env@3.587.0':
    resolution: {integrity: sha512-Hyg/5KFECIk2k5o8wnVEiniV86yVkhn5kzITUydmNGCkXdBFHMHRx6hleQ1bqwJHbBskyu8nbYamzcwymmGwmw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-http@3.587.0':
    resolution: {integrity: sha512-Su1SRWVRCuR1e32oxX3C1V4c5hpPN20WYcRfdcr2wXwHqSvys5DrnmuCC+JoEnS/zt3adUJhPliTqpfKgSdMrA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-ini@3.592.0':
    resolution: {integrity: sha512-3kG6ngCIOPbLJZZ3RV+NsU7HVK6vX1+1DrPJKj9fVlPYn7IXsk8NAaUT5885yC7+jKizjv0cWLrLKvAJV5gfUA==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sts': ^3.592.0

  '@aws-sdk/credential-provider-node@3.592.0':
    resolution: {integrity: sha512-BguihBGTrEjVBQ07hm+ZsO29eNJaxwBwUZMftgGAm2XcMIEClNPfm5hydxu2BmA4ouIJQJ6nG8pNYghEumM+Aw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-process@3.587.0':
    resolution: {integrity: sha512-V4xT3iCqkF8uL6QC4gqBJg/2asd/damswP1h9HCfqTllmPWzImS+8WD3VjgTLw5b0KbTy+ZdUhKc0wDnyzkzxg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-sso@3.592.0':
    resolution: {integrity: sha512-fYFzAdDHKHvhtufPPtrLdSv8lO6GuW3em6n3erM5uFdpGytNpjXvr3XGokIsuXcNkETAY/Xihg+G9ksNE8WJxQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-web-identity@3.587.0':
    resolution: {integrity: sha512-XqIx/I2PG7kyuw3WjAP9wKlxy8IvFJwB8asOFT1xPFoVfZYKIogjG9oLP5YiRtfvDkWIztHmg5MlVv3HdJDGRw==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sts': ^3.587.0

  '@aws-sdk/middleware-bucket-endpoint@3.587.0':
    resolution: {integrity: sha512-HkFXLPl8pr6BH/Q0JpOESqEKL0ZK3sk7aSZ1S6GE4RXET7H5R94THULXqQFZzD48gZcyFooO/yNKZTqrZFaWKg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-expect-continue@3.577.0':
    resolution: {integrity: sha512-6dPp8Tv4F0of4un5IAyG6q++GrRrNQQ4P2NAMB1W0VO4JoEu1C8GievbbDLi88TFIFmtKpnHB0ODCzwnoe8JsA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-flexible-checksums@3.587.0':
    resolution: {integrity: sha512-URMwp/budDvKhIvZ4a6zIBfFTun/iDlPWXqsGKYjEtHt8jz27OSjCZtDtIeqW4WTBdKL8KZgQcl+DdaE5M1qiQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-host-header@3.577.0':
    resolution: {integrity: sha512-9ca5MJz455CODIVXs0/sWmJm7t3QO4EUa1zf8pE8grLpzf0J94bz/skDWm37Pli13T3WaAQBHCTiH2gUVfCsWg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-location-constraint@3.577.0':
    resolution: {integrity: sha512-DKPTD2D2s+t2QUo/IXYtVa/6Un8GZ+phSTBkyBNx2kfZz4Kwavhl/JJzSqTV3GfCXkVdFu7CrjoX7BZ6qWeTUA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-logger@3.577.0':
    resolution: {integrity: sha512-aPFGpGjTZcJYk+24bg7jT4XdIp42mFXSuPt49lw5KygefLyJM/sB0bKKqPYYivW0rcuZ9brQ58eZUNthrzYAvg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-recursion-detection@3.577.0':
    resolution: {integrity: sha512-pn3ZVEd2iobKJlR3H+bDilHjgRnNrQ6HMmK9ZzZw89Ckn3Dcbv48xOv4RJvu0aU8SDLl/SNCxppKjeLDTPGBNA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-sdk-s3@3.587.0':
    resolution: {integrity: sha512-vtXTGEiw1E9Fax4LmcU2Z208gbrC8ShrdsSLmGcRPpu5NPOGBFBSDG5sy5EDNClrFxIl/Le8coQnD0EDBtx+uQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-signing@3.587.0':
    resolution: {integrity: sha512-tiZaTDj4RvhXGRAlncFn7CSEfL3iNPO67WSaxAq+Ls5j1VgczPhu5262cWONNoMgth3nXR1hhLC4ITSl/a6AzA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-ssec@3.577.0':
    resolution: {integrity: sha512-i2BPJR+rp8xmRVIGc0h1kDRFcM2J9GnClqqpc+NLSjmYadlcg4mPklisz9HzwFVcRPJ5XcGf3U4BYs5G8+iTyg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-user-agent@3.587.0':
    resolution: {integrity: sha512-SyDomN+IOrygLucziG7/nOHkjUXES5oH5T7p8AboO8oakMQJdnudNXiYWTicQWO52R51U6CR27rcMPTGeMedYA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/region-config-resolver@3.587.0':
    resolution: {integrity: sha512-93I7IPZtulZQoRK+O20IJ4a1syWwYPzoO2gc3v+/GNZflZPV3QJXuVbIm0pxBsu0n/mzKGUKqSOLPIaN098HcQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/s3-request-presigner@3.592.0':
    resolution: {integrity: sha512-3VzhSWV1kPbVkp9PNmcYvdL8tnWx3OrVBkLfKQjbuP79FbftK2mnZV2MB/h9GsujJ6iE6No3XglG9mHe5OX9mg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/signature-v4-crt@3.587.0':
    resolution: {integrity: sha512-U523oCCCF6xWpDkPXFPtVEg6H39K0srKA6lQB5JtXts4DAIKZOal9f0MsOIWGKVl36uf5VpPC74ZIW4YE9D3Rw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/signature-v4-multi-region@3.587.0':
    resolution: {integrity: sha512-TR9+ZSjdXvXUz54ayHcCihhcvxI9W7102J1OK6MrLgBlPE7uRhAx42BR9L5lLJ86Xj3LuqPWf//o9d/zR9WVIg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/token-providers@3.587.0':
    resolution: {integrity: sha512-ULqhbnLy1hmJNRcukANBWJmum3BbjXnurLPSFXoGdV0llXYlG55SzIla2VYqdveQEEjmsBuTZdFvXAtNpmS5Zg==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sso-oidc': ^3.587.0

  '@aws-sdk/types@3.577.0':
    resolution: {integrity: sha512-FT2JZES3wBKN/alfmhlo+3ZOq/XJ0C7QOZcDNrpKjB0kqYoKjhVKZ/Hx6ArR0czkKfHzBBEs6y40ebIHx2nSmA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-arn-parser@3.568.0':
    resolution: {integrity: sha512-XUKJWWo+KOB7fbnPP0+g/o5Ulku/X53t7i/h+sPHr5xxYTJJ9CYnbToo95mzxe7xWvkLrsNtJ8L+MnNn9INs2w==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-endpoints@3.587.0':
    resolution: {integrity: sha512-8I1HG6Em8wQWqKcRW6m358mqebRVNpL8XrrEoT4In7xqkKkmYtHRNVYP6lcmiQh5pZ/c/FXu8dSchuFIWyEtqQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-format-url@3.577.0':
    resolution: {integrity: sha512-SyEGC2J+y/krFRuPgiF02FmMYhqbiIkOjDE6k4nYLJQRyS6XEAGxZoG+OHeOVEM+bsDgbxokXZiM3XKGu6qFIg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-locate-window@3.568.0':
    resolution: {integrity: sha512-3nh4TINkXYr+H41QaPelCceEB2FXP3fxp93YZXB/kqJvX0U9j0N0Uk45gvsjmEPzG8XxkPEeLIfT2I1M7A6Lig==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-user-agent-browser@3.577.0':
    resolution: {integrity: sha512-zEAzHgR6HWpZOH7xFgeJLc6/CzMcx4nxeQolZxVZoB5pPaJd3CjyRhZN0xXeZB0XIRCWmb4yJBgyiugXLNMkLA==}

  '@aws-sdk/util-user-agent-node@3.587.0':
    resolution: {integrity: sha512-Pnl+DUe/bvnbEEDHP3iVJrOtE3HbFJBPgsD6vJ+ml/+IYk1Eq49jEG+EHZdNTPz3SDG0kbp2+7u41MKYJHR/iQ==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      aws-crt: '>=1.0.0'
    peerDependenciesMeta:
      aws-crt:
        optional: true

  '@aws-sdk/util-utf8-browser@3.259.0':
    resolution: {integrity: sha512-UvFa/vR+e19XookZF8RzFZBrw2EUkQWxiBW0yYQAhvk3C+QVGl0H3ouca8LDBlBfQKXwmW3huo/59H8rwb1wJw==}

  '@aws-sdk/xml-builder@3.575.0':
    resolution: {integrity: sha512-cWgAwmbFYNCFzPwxL705+lWps0F3ZvOckufd2KKoEZUmtpVw9/txUXNrPySUXSmRTSRhoatIMABNfStWR043bQ==}
    engines: {node: '>=16.0.0'}

  '@babel/code-frame@7.24.2':
    resolution: {integrity: sha512-y5+tLQyV8pg3fsiln67BVLD1P13Eg4lh5RW9mF0zUuvLrv9uIQ4MCL+CRT+FTsBlBjcIan6PGsLcBN0m3ClUyQ==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.24.1':
    resolution: {integrity: sha512-Pc65opHDliVpRHuKfzI+gSA4zcgr65O4cl64fFJIWEEh8JoHIHh0Oez1Eo8Arz8zq/JhgKodQaxEwUPRtZylVA==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.24.3':
    resolution: {integrity: sha512-5FcvN1JHw2sHJChotgx8Ek0lyuh4kCKelgMTTqhYJJtloNvUfpAFMeNQUtdlIaktwrSV9LtCdqwk48wL2wBacQ==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.24.1':
    resolution: {integrity: sha512-DfCRfZsBcrPEHUfuBMgbJ1Ut01Y/itOs+hY2nFLgqsqXd52/iSiVq5TITtUasIUgm+IIKdY2/1I7auiQOEeC9A==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.22.5':
    resolution: {integrity: sha512-LvBTxu8bQSQkcyKOU+a1btnNFQ1dMAd0R6PyW3arXes06F6QLWLIrd681bxRPIXlrMGR3XYnW9JyML7dP3qgxg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.23.6':
    resolution: {integrity: sha512-9JB548GZoQVmzrFgp8o7KxdgkTGm6xs9DW0o/Pim72UDjzr5ObUQ6ZzYPqA+g9OTS2bBQoctLJrky0RDCAWRgQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.24.1':
    resolution: {integrity: sha512-1yJa9dX9g//V6fDebXoEfEsxkZHk3Hcbm+zLhyu6qVgYFLvmTALTeV+jNU9e5RnYtioBrGEOdoI2joMSNQ/+aA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-environment-visitor@7.22.20':
    resolution: {integrity: sha512-zfedSIzFhat/gFhWfHtgWvlec0nqB9YEIVrpuwjruLlXfUSnA8cJB0miHKwqDnQ7d32aKo2xt88/xZptwxbfhA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-function-name@7.23.0':
    resolution: {integrity: sha512-OErEqsrxjZTJciZ4Oo+eoZqeW9UIiOcuYKRJA4ZAgV9myA+pOXhhmpfNCKjEH/auVfEYVFJ6y1Tc4r0eIApqiw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-hoist-variables@7.22.5':
    resolution: {integrity: sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-member-expression-to-functions@7.23.0':
    resolution: {integrity: sha512-6gfrPwh7OuT6gZyJZvd6WbTfrqAo7vm4xCzAXOusKqq/vWdKXphTpj5klHKNmRUU6/QRGlBsyU9mAIPaWHlqJA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.22.15':
    resolution: {integrity: sha512-0pYVBnDKZO2fnSPCrgM/6WMc7eS20Fbok+0r88fp+YtWVLZrp4CkafFGIp+W0VKw4a22sgebPT99y+FDNMdP4w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.24.3':
    resolution: {integrity: sha512-viKb0F9f2s0BCS22QSF308z/+1YWKV/76mwt61NBzS5izMzDPwdq1pTrzf+Li3npBWX9KdQbkeCt1jSAM7lZqg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.23.3':
    resolution: {integrity: sha512-7bBs4ED9OmswdfDzpz4MpWgSrV7FXlc3zIagvLFjS5H+Mk7Snr21vQ6QwrsoCGMfNC4e4LQPdoULEt4ykz0SRQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.22.5':
    resolution: {integrity: sha512-HBwaojN0xFRx4yIvpwGqxiV2tUfl7401jlok564NgB9EHS1y6QT17FmKWm4ztqjeVdXLuC4fSvHc5ePpQjoTbw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.24.0':
    resolution: {integrity: sha512-9cUznXMG0+FxRuJfvL82QlTqIzhVW9sL0KjMPHhAOOvpQGL8QtdxnBKILjBqxlHyliz0yCa1G903ZXI/FuHy2w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-replace-supers@7.24.1':
    resolution: {integrity: sha512-QCR1UqC9BzG5vZl8BMicmZ28RuUBnHhAMddD8yHFHDRH9lLTZ9uUPehX8ctVPT8l0TKblJidqcgUUKGVrePleQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-simple-access@7.22.5':
    resolution: {integrity: sha512-n0H99E/K+Bika3++WNL17POvo4rKWZ7lZEp1Q+fStVbUi8nxPQEBOlTmCOxW/0JsS56SKKQ+ojAe2pHKJHN35w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-skip-transparent-expression-wrappers@7.22.5':
    resolution: {integrity: sha512-tK14r66JZKiC43p8Ki33yLBVJKlQDFoA8GYN67lWCDCqoL6EMMSuM9b+Iff2jHaM/RRFYl7K+iiru7hbRqNx8Q==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-split-export-declaration@7.22.6':
    resolution: {integrity: sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.24.1':
    resolution: {integrity: sha512-2ofRCjnnA9y+wk8b9IAREroeUP02KHp431N2mhKniy2yKIDKpbrHv9eXwm8cBeWQYcJmzv5qKCu65P47eCF7CQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.22.20':
    resolution: {integrity: sha512-Y4OZ+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.23.5':
    resolution: {integrity: sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.24.1':
    resolution: {integrity: sha512-BpU09QqEe6ZCHuIHFphEFgvNSrubve1FtyMton26ekZ85gRGi6LrTF7zArARp2YvyFxloeiRmtSCq5sjh1WqIg==}
    engines: {node: '>=6.9.0'}

  '@babel/highlight@7.24.2':
    resolution: {integrity: sha512-Yac1ao4flkTxTteCDZLEvdxg2fZfz1v8M4QpaGypq/WPDqg3ijHYbDfs+LG5hvzSoqaSZ9/Z9lKSP3CjZjv+pA==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.24.1':
    resolution: {integrity: sha512-Zo9c7N3xdOIQrNip7Lc9wvRPzlRtovHVE4lkz8WEDr7uYh/GMQhSiIgFxGIArRHYdJE5kxtZjAf8rT0xhdLCzg==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-syntax-jsx@7.24.1':
    resolution: {integrity: sha512-2eCtxZXf+kbkMIsXS4poTvT4Yu5rXiRa+9xGVT56raghjmBTKMpFNc9R4IDiB4emao9eO22Ox7CxuJG7BgExqA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-typescript@7.24.1':
    resolution: {integrity: sha512-Yhnmvy5HZEnHUty6i++gcfH1/l68AHnItFHnaCv6hn9dNh0hQvvQJsxpi4BMBFN5DLeHBuucT/0DgzXif/OyRw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.24.1':
    resolution: {integrity: sha512-szog8fFTUxBfw0b98gEWPaEqF42ZUD/T3bkynW/wtgx2p/XCP55WEsb+VosKceRSd6njipdZvNogqdtI4Q0chw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typescript@7.24.1':
    resolution: {integrity: sha512-liYSESjX2fZ7JyBFkYG78nfvHlMKE6IpNdTVnxmlYUR+j5ZLsitFbaAE+eJSK2zPPkNWNw4mXL51rQ8WrvdK0w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-typescript@7.24.1':
    resolution: {integrity: sha512-1DBaMmRDpuYQBPWD8Pf/WEwCrtgRHxsZnP4mIy9G/X+hFfbI47Q2G4t1Paakld84+qsk2fSsUPMKg71jkoOOaQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime-corejs2@7.24.7':
    resolution: {integrity: sha512-+Lf6xofiPZLtFwNkpjGHPgJck4b22Yo8h9+WHf3bEbS4ikOyOMNtJk6HSTolEQ2irH1XSoeguaCkrkcgyThrMA==}
    engines: {node: '>=6.9.0'}

  '@babel/runtime@7.24.7':
    resolution: {integrity: sha512-UwgBRMjJP+xv857DCngvqXI3Iq6J4v0wXmwc6sapg+zyhbwmQX67LUEFrkK5tbyJ30jGuG3ZvWpBiB9LCy1kWw==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.24.0':
    resolution: {integrity: sha512-Bkf2q8lMB0AFpX0NFEqSbx1OkTHf0f+0j82mkw+ZpzBnkk7e9Ql0891vlfgi+kHwOk8tQjiQHpqh4LaSa0fKEA==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.24.1':
    resolution: {integrity: sha512-xuU6o9m68KeqZbQuDt2TcKSxUw/mrsvavlEqQ1leZ/B+C9tk6E4sRWy97WaXgvq5E+nU3cXMxv3WKOCanVMCmQ==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.24.0':
    resolution: {integrity: sha512-+j7a5c253RfKh8iABBhywc8NSfP5LURe7Uh4qpsh6jc+aLJguvmIUBdjSdEMQv2bENrCR5MfRdjGo7vzS/ob7w==}
    engines: {node: '>=6.9.0'}

  '@ctrl/tinycolor@3.6.1':
    resolution: {integrity: sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==}
    engines: {node: '>=10'}

  '@element-plus/icons-vue@2.3.1':
    resolution: {integrity: sha512-XxVUZv48RZAd87ucGS48jPf6pKu0yV5UCg9f4FFwtrYxXOwWuVJo6wOvSLKEoMQKjv8GsX/mhP6UsC1lRwbUWg==}
    peerDependencies:
      vue: ^3.2.0

  '@esbuild/aix-ppc64@0.21.5':
    resolution: {integrity: sha512-1SDgH6ZSPTlggy1yI6+Dbkiz8xzpHJEVAlF/AM1tHPLsf5STom9rwtjE4hKAF20FfXXNTFqEYXyJNWh1GiZedQ==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.21.5':
    resolution: {integrity: sha512-c0uX9VAUBQ7dTDCjq+wdyGLowMdtR/GoC2U5IYk/7D1H1JYC0qseD7+11iMP2mRLN9RcCMRcjC4YMclCzGwS/A==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.21.5':
    resolution: {integrity: sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.21.5':
    resolution: {integrity: sha512-D7aPRUUNHRBwHxzxRvp856rjUHRFW1SdQATKXH2hqA0kAZb1hKmi02OpYRacl0TxIGz/ZmXWlbZgjwWYaCakTA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.21.5':
    resolution: {integrity: sha512-DwqXqZyuk5AiWWf3UfLiRDJ5EDd49zg6O9wclZ7kUMv2WRFr4HKjXp/5t8JZ11QbQfUS6/cRCKGwYhtNAY88kQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.21.5':
    resolution: {integrity: sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.21.5':
    resolution: {integrity: sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.21.5':
    resolution: {integrity: sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.21.5':
    resolution: {integrity: sha512-ibKvmyYzKsBeX8d8I7MH/TMfWDXBF3db4qM6sy+7re0YXya+K1cem3on9XgdT2EQGMu4hQyZhan7TeQ8XkGp4Q==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.21.5':
    resolution: {integrity: sha512-bPb5AHZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.21.5':
    resolution: {integrity: sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.21.5':
    resolution: {integrity: sha512-uHf1BmMG8qEvzdrzAqg2SIG/02+4/DHB6a9Kbya0XDvwDEKCoC8ZRWI5JJvNdUjtciBGFQ5PuBlpEOXQj+JQSg==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.21.5':
    resolution: {integrity: sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.21.5':
    resolution: {integrity: sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.21.5':
    resolution: {integrity: sha512-2HdXDMd9GMgTGrPWnJzP2ALSokE/0O5HhTUvWIbD3YdjME8JwvSCnNGBnTThKGEB91OZhzrJ4qIIxk/SBmyDDA==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.21.5':
    resolution: {integrity: sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.21.5':
    resolution: {integrity: sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-x64@0.21.5':
    resolution: {integrity: sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-x64@0.21.5':
    resolution: {integrity: sha512-HLNNw99xsvx12lFBUwoT8EVCsSvRNDVxNpjZ7bPn947b8gJPzeHWyNVhFsaerc0n3TsbOINvRP2byTZ5LKezow==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.21.5':
    resolution: {integrity: sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.21.5':
    resolution: {integrity: sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.21.5':
    resolution: {integrity: sha512-SWXFF1CL2RVNMaVs+BBClwtfZSvDgtL//G/smwAc5oVK/UPu2Gu9tIaRgFmYFFKrmg3SyAjSrElf0TiJ1v8fYA==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.21.5':
    resolution: {integrity: sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]

  '@eslint-community/eslint-utils@4.4.0':
    resolution: {integrity: sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.10.0':
    resolution: {integrity: sha512-Cu96Sd2By9mCNTx2iyKOmq10v22jUVQv0lQnlGNy16oE9589yE+QADPbrMGCkA51cKZSg3Pu/aTJVTGfL/qjUA==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint-community/regexpp@4.10.1':
    resolution: {integrity: sha512-Zm2NGpWELsQAD1xsJzGQpYfvICSsFkEpU0jxBjfdC6uNEWXcHnfs9hScFWtXVDVl+rBQJGrl4g1vcKIejpH9dA==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/eslintrc@2.1.4':
    resolution: {integrity: sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@eslint/js@8.57.0':
    resolution: {integrity: sha512-Ys+3g2TaW7gADOJzPt83SJtCDhMjndcDMFVQ/Tj9iA1BfJzFKD9mAUXT3OenpuPHbI6P/myECxRJrofUsDx/5g==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@fast-crud/fast-crud@1.21.2':
    resolution: {integrity: sha512-SEr3oUyuVlOnc+Ga35DmiPUABc4gy+lb4S7ZEg6b0rPkbjuHScWq+ecHd00xd9XIkAtSw001sEZiWUWmHAkruw==}

  '@fast-crud/fast-extends@1.21.2':
    resolution: {integrity: sha512-vy1q9qzEkgpbWY+/IN/vm5o5k2yNupZmIzdfJHCNs3xgpBQtrZ5m4CP4ZOUJlOB7ECi6vDWCWznqMXjA9xmGAg==}

  '@fast-crud/ui-element@1.21.2':
    resolution: {integrity: sha512-HzxZx8iyMZ04Myi8ewUsd8+j2S/ItNBiWD86RhWY+ZzFCYm0HsSZP6a9a1UHYaMpm05DZ2BLvAhvPPyF0Sl2GQ==}

  '@fast-crud/ui-interface@1.21.2':
    resolution: {integrity: sha512-XyKq4sv9k9ku2sE4BsEJePGLeqFAiD1xTavKD+KIY52nBn7xiis7mUP35cKsnpPkAsHIl1rvS4cv+XZMAfUlSQ==}

  '@floating-ui/core@1.6.0':
    resolution: {integrity: sha512-PcF++MykgmTj3CIyOQbKA/hDzOAiqI3mhuoN44WRCopIs1sgoDoU4oty4Jtqaj/y3oDU6fnVSm4QG0a3t5i0+g==}

  '@floating-ui/dom@1.6.3':
    resolution: {integrity: sha512-RnDthu3mzPlQ31Ss/BTwQ1zjzIhr3lk1gZB1OC56h/1vEtaXkESrOqL5fQVMfXpwGtRwX+YsZBdyHtJMQnkArw==}

  '@floating-ui/utils@0.2.1':
    resolution: {integrity: sha512-9TANp6GPoMtYzQdt54kfAyMmz1+osLlXdg2ENroU7zzrtflTLrrC/lgrIfaSe+Wu0b89GKccT7vxXA0MoAIO+Q==}

  '@httptoolkit/websocket-stream@6.0.1':
    resolution: {integrity: sha512-A0NOZI+Glp3Xgcz6Na7i7o09+/+xm2m0UCU8gdtM2nIv6/cjLmhMZMqehSpTlgbx9omtLmV8LVqOskPEyWnmZQ==}

  '@humanwhocodes/config-array@0.11.14':
    resolution: {integrity: sha512-3T8LkOmg45BV5FICb15QQMsyUSWrQ8AygVfC7ZG32zOalnqrilm018ZVCw0eapXux8FtA33q8PSRSstjee3jSg==}
    engines: {node: '>=10.10.0'}
    deprecated: Use @eslint/config-array instead

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}

  '@humanwhocodes/object-schema@2.0.3':
    resolution: {integrity: sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==}
    deprecated: Use @eslint/object-schema instead

  '@iconify/types@2.0.0':
    resolution: {integrity: sha512-+wluvCrRhXrhyOmRDJ3q8mux9JkKy5SJ/v8ol2tu4FVjyYvtEzkc/3pK15ET6RKg4b4w4BmTk1+gsCUhf21Ykg==}

  '@iconify/utils@2.1.22':
    resolution: {integrity: sha512-6UHVzTVXmvO8uS6xFF+L/QTSpTzA/JZxtgU+KYGFyDYMEObZ1bu/b5l+zNJjHy+0leWjHI+C0pXlzGvv3oXZMA==}

  '@iconify/vue@4.1.2':
    resolution: {integrity: sha512-CQnYqLiQD5LOAaXhBrmj1mdL2/NCJvwcC4jtW2Z8ukhThiFkLDkutarTOV2trfc9EXqUqRs0KqXOL9pZ/IyysA==}
    peerDependencies:
      vue: '>=3'

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}

  '@jest/schemas@29.6.3':
    resolution: {integrity: sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jridgewell/gen-mapping@0.3.5':
    resolution: {integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.4.15':
    resolution: {integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@one-ini/wasm@0.1.1':
    resolution: {integrity: sha512-XuySG1E38YScSJoMlqovLru4KTUNSjgVTIjyh7qMX6aNN5HY5Ct5LhRJdxO79JtTzKfzV/bnWpz+zquYrISsvw==}

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}

  '@polka/url@1.0.0-next.25':
    resolution: {integrity: sha512-j7P6Rgr3mmtdkeDGTe0E/aYyWEWVtc5yFXtHCRHs28/jptDEWfaVOc5T7cblqy1XKPPfCxJc/8DwQ5YgLOZOVQ==}

  '@popperjs/core@2.11.8':
    resolution: {integrity: sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==}

  '@remirror/core-constants@3.0.0':
    resolution: {integrity: sha512-42aWfPrimMfDKDi4YegyS7x+/0tlzaqwPQCULLanv3DMIlu96KTJR0fM5isWX2UViOqlGnX6YFgqWepcX+XMNg==}

  '@rollup/pluginutils@5.1.0':
    resolution: {integrity: sha512-XTIWOPPcpvyKI6L1NHo0lFlCyznUEyPmPY1mc3KpPVDYulHSTvyeLNVW00QTLIAFNhR3kYnJTQHeGqU4M3n09g==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/rollup-android-arm-eabi@4.13.0':
    resolution: {integrity: sha512-5ZYPOuaAqEH/W3gYsRkxQATBW3Ii1MfaT4EQstTnLKViLi2gLSQmlmtTpGucNP3sXEpOiI5tdGhjdE111ekyEg==}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.13.0':
    resolution: {integrity: sha512-BSbaCmn8ZadK3UAQdlauSvtaJjhlDEjS5hEVVIN3A4bbl3X+otyf/kOJV08bYiRxfejP3DXFzO2jz3G20107+Q==}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.13.0':
    resolution: {integrity: sha512-Ovf2evVaP6sW5Ut0GHyUSOqA6tVKfrTHddtmxGQc1CTQa1Cw3/KMCDEEICZBbyppcwnhMwcDce9ZRxdWRpVd6g==}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.13.0':
    resolution: {integrity: sha512-U+Jcxm89UTK592vZ2J9st9ajRv/hrwHdnvyuJpa5A2ngGSVHypigidkQJP+YiGL6JODiUeMzkqQzbCG3At81Gg==}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-linux-arm-gnueabihf@4.13.0':
    resolution: {integrity: sha512-8wZidaUJUTIR5T4vRS22VkSMOVooG0F4N+JSwQXWSRiC6yfEsFMLTYRFHvby5mFFuExHa/yAp9juSphQQJAijQ==}
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm64-gnu@4.13.0':
    resolution: {integrity: sha512-Iu0Kno1vrD7zHQDxOmvweqLkAzjxEVqNhUIXBsZ8hu8Oak7/5VTPrxOEZXYC1nmrBVJp0ZcL2E7lSuuOVaE3+w==}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm64-musl@4.13.0':
    resolution: {integrity: sha512-C31QrW47llgVyrRjIwiOwsHFcaIwmkKi3PCroQY5aVq4H0A5v/vVVAtFsI1nfBngtoRpeREvZOkIhmRwUKkAdw==}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-riscv64-gnu@4.13.0':
    resolution: {integrity: sha512-Oq90dtMHvthFOPMl7pt7KmxzX7E71AfyIhh+cPhLY9oko97Zf2C9tt/XJD4RgxhaGeAraAXDtqxvKE1y/j35lA==}
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-gnu@4.13.0':
    resolution: {integrity: sha512-yUD/8wMffnTKuiIsl6xU+4IA8UNhQ/f1sAnQebmE/lyQ8abjsVyDkyRkWop0kdMhKMprpNIhPmYlCxgHrPoXoA==}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-musl@4.13.0':
    resolution: {integrity: sha512-9RyNqoFNdF0vu/qqX63fKotBh43fJQeYC98hCaf89DYQpv+xu0D8QFSOS0biA7cGuqJFOc1bJ+m2rhhsKcw1hw==}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-win32-arm64-msvc@4.13.0':
    resolution: {integrity: sha512-46ue8ymtm/5PUU6pCvjlic0z82qWkxv54GTJZgHrQUuZnVH+tvvSP0LsozIDsCBFO4VjJ13N68wqrKSeScUKdA==}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.13.0':
    resolution: {integrity: sha512-P5/MqLdLSlqxbeuJ3YDeX37srC8mCflSyTrUsgbU1c/U9j6l2g2GiIdYaGD9QjdMQPMSgYm7hgg0551wHyIluw==}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.13.0':
    resolution: {integrity: sha512-UKXUQNbO3DOhzLRwHSpa0HnhhCgNODvfoPWv2FCXme8N/ANFfhIPMGuOT+QuKd16+B5yxZ0HdpNlqPvTMS1qfw==}
    cpu: [x64]
    os: [win32]

  '@sinclair/typebox@0.27.8':
    resolution: {integrity: sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==}

  '@smithy/abort-controller@3.0.0':
    resolution: {integrity: sha512-p6GlFGBt9K4MYLu72YuJ523NVR4A8oHlC5M2JO6OmQqN8kAc/uh1JqLE+FizTokrSJGg0CSvC+BrsmGzKtsZKA==}
    engines: {node: '>=16.0.0'}

  '@smithy/chunked-blob-reader-native@3.0.0':
    resolution: {integrity: sha512-VDkpCYW+peSuM4zJip5WDfqvg2Mo/e8yxOv3VF1m11y7B8KKMKVFtmZWDe36Fvk8rGuWrPZHHXZ7rR7uM5yWyg==}

  '@smithy/chunked-blob-reader@3.0.0':
    resolution: {integrity: sha512-sbnURCwjF0gSToGlsBiAmd1lRCmSn72nu9axfJu5lIx6RUEgHu6GwTMbqCdhQSi0Pumcm5vFxsi9XWXb2mTaoA==}

  '@smithy/config-resolver@3.0.1':
    resolution: {integrity: sha512-hbkYJc20SBDz2qqLzttjI/EqXemtmWk0ooRznLsiXp3066KQRTvuKHa7U4jCZCJq6Dozqvy0R1/vNESC9inPJg==}
    engines: {node: '>=16.0.0'}

  '@smithy/core@2.2.0':
    resolution: {integrity: sha512-ygLZSSKgt9bR8HAxR9mK+U5obvAJBr6zlQuhN5soYWx/amjDoQN4dTkydTypgKe6rIbUjTILyLU+W5XFwXr4kg==}
    engines: {node: '>=16.0.0'}

  '@smithy/credential-provider-imds@3.1.0':
    resolution: {integrity: sha512-q4A4d38v8pYYmseu/jTS3Z5I3zXlEOe5Obi+EJreVKgSVyWUHOd7/yaVCinC60QG4MRyCs98tcxBH1IMC0bu7Q==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-codec@3.0.0':
    resolution: {integrity: sha512-PUtyEA0Oik50SaEFCZ0WPVtF9tz/teze2fDptW6WRXl+RrEenH8UbEjudOz8iakiMl3lE3lCVqYf2Y+znL8QFQ==}

  '@smithy/eventstream-serde-browser@3.0.0':
    resolution: {integrity: sha512-NB7AFiPN4NxP/YCAnrvYR18z2/ZsiHiF7VtG30gshO9GbFrIb1rC8ep4NGpJSWrz6P64uhPXeo4M0UsCLnZKqw==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-serde-config-resolver@3.0.0':
    resolution: {integrity: sha512-RUQG3vQ3LX7peqqHAbmayhgrF5aTilPnazinaSGF1P0+tgM3vvIRWPHmlLIz2qFqB9LqFIxditxc8O2Z6psrRw==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-serde-node@3.0.0':
    resolution: {integrity: sha512-baRPdMBDMBExZXIUAoPGm/hntixjt/VFpU6+VmCyiYJYzRHRxoaI1MN+5XE+hIS8AJ2GCHLMFEIOLzq9xx1EgQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-serde-universal@3.0.0':
    resolution: {integrity: sha512-HNFfShmotWGeAoW4ujP8meV9BZavcpmerDbPIjkJbxKbN8RsUcpRQ/2OyIxWNxXNH2GWCAxuSB7ynmIGJlQ3Dw==}
    engines: {node: '>=16.0.0'}

  '@smithy/fetch-http-handler@3.0.1':
    resolution: {integrity: sha512-uaH74i5BDj+rBwoQaXioKpI0SHBJFtOVwzrCpxZxphOW0ki5jhj7dXvDMYM2IJem8TpdFvS2iC08sjOblfFGFg==}

  '@smithy/hash-blob-browser@3.0.0':
    resolution: {integrity: sha512-/Wbpdg+bwJvW7lxR/zpWAc1/x/YkcqguuF2bAzkJrvXriZu1vm8r+PUdE4syiVwQg7PPR2dXpi3CLBb9qRDaVQ==}

  '@smithy/hash-node@3.0.0':
    resolution: {integrity: sha512-84qXstNemP3XS5jcof0el6+bDfjzuvhJPQTEfro3lgtbCtKgzPm3MgiS6ehXVPjeQ5+JS0HqmTz8f/RYfzHVxw==}
    engines: {node: '>=16.0.0'}

  '@smithy/hash-stream-node@3.0.0':
    resolution: {integrity: sha512-J0i7de+EgXDEGITD4fxzmMX8CyCNETTIRXlxjMiNUvvu76Xn3GJ31wQR85ynlPk2wI1lqoknAFJaD1fiNDlbIA==}
    engines: {node: '>=16.0.0'}

  '@smithy/invalid-dependency@3.0.0':
    resolution: {integrity: sha512-F6wBBaEFgJzj0s4KUlliIGPmqXemwP6EavgvDqYwCH40O5Xr2iMHvS8todmGVZtuJCorBkXsYLyTu4PuizVq5g==}

  '@smithy/is-array-buffer@3.0.0':
    resolution: {integrity: sha512-+Fsu6Q6C4RSJiy81Y8eApjEB5gVtM+oFKTffg+jSuwtvomJJrhUJBu2zS8wjXSgH/g1MKEWrzyChTBe6clb5FQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/md5-js@3.0.0':
    resolution: {integrity: sha512-Tm0vrrVzjlD+6RCQTx7D3Ls58S3FUH1ZCtU1MIh/qQmaOo1H9lMN2as6CikcEwgattnA9SURSdoJJ27xMcEfMA==}

  '@smithy/middleware-content-length@3.0.0':
    resolution: {integrity: sha512-3C4s4d/iGobgCtk2tnWW6+zSTOBg1PRAm2vtWZLdriwTroFbbWNSr3lcyzHdrQHnEXYCC5K52EbpfodaIUY8sg==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-endpoint@3.0.1':
    resolution: {integrity: sha512-lQ/UOdGD4KM5kLZiAl0q8Qy3dPbynvAXKAdXnYlrA1OpaUwr+neSsVokDZpY6ZVb5Yx8jnus29uv6XWpM9P4SQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-retry@3.0.3':
    resolution: {integrity: sha512-Wve1qzJb83VEU/6q+/I0cQdAkDnuzELC6IvIBwDzUEiGpKqXgX1v10FUuZGbRS6Ov/P+HHthcAoHOJZQvZNAkA==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-serde@3.0.0':
    resolution: {integrity: sha512-I1vKG1foI+oPgG9r7IMY1S+xBnmAn1ISqployvqkwHoSb8VPsngHDTOgYGYBonuOKndaWRUGJZrKYYLB+Ane6w==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-stack@3.0.0':
    resolution: {integrity: sha512-+H0jmyfAyHRFXm6wunskuNAqtj7yfmwFB6Fp37enytp2q047/Od9xetEaUbluyImOlGnGpaVGaVfjwawSr+i6Q==}
    engines: {node: '>=16.0.0'}

  '@smithy/node-config-provider@3.1.0':
    resolution: {integrity: sha512-ngfB8QItUfTFTfHMvKuc2g1W60V1urIgZHqD1JNFZC2tTWXahqf2XvKXqcBS7yZqR7GqkQQZy11y/lNOUWzq7Q==}
    engines: {node: '>=16.0.0'}

  '@smithy/node-http-handler@3.0.0':
    resolution: {integrity: sha512-3trD4r7NOMygwLbUJo4eodyQuypAWr7uvPnebNJ9a70dQhVn+US8j/lCnvoJS6BXfZeF7PkkkI0DemVJw+n+eQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/property-provider@3.1.0':
    resolution: {integrity: sha512-Tj3+oVhqdZgemjCiWjFlADfhvLF4C/uKDuKo7/tlEsRQ9+3emCreR2xndj970QSRSsiCEU8hZW3/8JQu+n5w4Q==}
    engines: {node: '>=16.0.0'}

  '@smithy/protocol-http@4.0.0':
    resolution: {integrity: sha512-qOQZOEI2XLWRWBO9AgIYuHuqjZ2csyr8/IlgFDHDNuIgLAMRx2Bl8ck5U5D6Vh9DPdoaVpuzwWMa0xcdL4O/AQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/querystring-builder@3.0.0':
    resolution: {integrity: sha512-bW8Fi0NzyfkE0TmQphDXr1AmBDbK01cA4C1Z7ggwMAU5RDz5AAv/KmoRwzQAS0kxXNf/D2ALTEgwK0U2c4LtRg==}
    engines: {node: '>=16.0.0'}

  '@smithy/querystring-parser@3.0.0':
    resolution: {integrity: sha512-UzHwthk0UEccV4dHzPySnBy34AWw3V9lIqUTxmozQ+wPDAO9csCWMfOLe7V9A2agNYy7xE+Pb0S6K/J23JSzfQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/service-error-classification@3.0.0':
    resolution: {integrity: sha512-3BsBtOUt2Gsnc3X23ew+r2M71WwtpHfEDGhHYHSDg6q1t8FrWh15jT25DLajFV1H+PpxAJ6gqe9yYeRUsmSdFA==}
    engines: {node: '>=16.0.0'}

  '@smithy/shared-ini-file-loader@3.1.0':
    resolution: {integrity: sha512-dAM7wSX0NR3qTNyGVN/nwwpEDzfV9T/3AN2eABExWmda5VqZKSsjlINqomO5hjQWGv+IIkoXfs3u2vGSNz8+Rg==}
    engines: {node: '>=16.0.0'}

  '@smithy/signature-v4@3.0.0':
    resolution: {integrity: sha512-kXFOkNX+BQHe2qnLxpMEaCRGap9J6tUGLzc3A9jdn+nD4JdMwCKTJ+zFwQ20GkY+mAXGatyTw3HcoUlR39HwmA==}
    engines: {node: '>=16.0.0'}

  '@smithy/smithy-client@3.1.1':
    resolution: {integrity: sha512-tj4Ku7MpzZR8cmVuPcSbrLFVxmptWktmJMwST/uIEq4sarabEdF8CbmQdYB7uJ/X51Qq2EYwnRsoS7hdR4B7rA==}
    engines: {node: '>=16.0.0'}

  '@smithy/types@3.0.0':
    resolution: {integrity: sha512-VvWuQk2RKFuOr98gFhjca7fkBS+xLLURT8bUjk5XQoV0ZLm7WPwWPPY3/AwzTLuUBDeoKDCthfe1AsTUWaSEhw==}
    engines: {node: '>=16.0.0'}

  '@smithy/url-parser@3.0.0':
    resolution: {integrity: sha512-2XLazFgUu+YOGHtWihB3FSLAfCUajVfNBXGGYjOaVKjLAuAxx3pSBY3hBgLzIgB17haf59gOG3imKqTy8mcrjw==}

  '@smithy/util-base64@3.0.0':
    resolution: {integrity: sha512-Kxvoh5Qtt0CDsfajiZOCpJxgtPHXOKwmM+Zy4waD43UoEMA+qPxxa98aE/7ZhdnBFZFXMOiBR5xbcaMhLtznQQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-body-length-browser@3.0.0':
    resolution: {integrity: sha512-cbjJs2A1mLYmqmyVl80uoLTJhAcfzMOyPgjwAYusWKMdLeNtzmMz9YxNl3/jRLoxSS3wkqkf0jwNdtXWtyEBaQ==}

  '@smithy/util-body-length-node@3.0.0':
    resolution: {integrity: sha512-Tj7pZ4bUloNUP6PzwhN7K386tmSmEET9QtQg0TgdNOnxhZvCssHji+oZTUIuzxECRfG8rdm2PMw2WCFs6eIYkA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-buffer-from@3.0.0':
    resolution: {integrity: sha512-aEOHCgq5RWFbP+UDPvPot26EJHjOC+bRgse5A8V3FSShqd5E5UN4qc7zkwsvJPPAVsf73QwYcHN1/gt/rtLwQA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-config-provider@3.0.0':
    resolution: {integrity: sha512-pbjk4s0fwq3Di/ANL+rCvJMKM5bzAQdE5S/6RL5NXgMExFAi6UgQMPOm5yPaIWPpr+EOXKXRonJ3FoxKf4mCJQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-defaults-mode-browser@3.0.3':
    resolution: {integrity: sha512-3DFON2bvXJAukJe+qFgPV/rorG7ZD3m4gjCXHD1V5z/tgKQp5MCTCLntrd686tX6tj8Uli3lefWXJudNg5WmCA==}
    engines: {node: '>= 10.0.0'}

  '@smithy/util-defaults-mode-node@3.0.3':
    resolution: {integrity: sha512-D0b8GJXecT00baoSQ3Iieu3k3mZ7GY8w1zmg8pdogYrGvWJeLcIclqk2gbkG4K0DaBGWrO6v6r20iwIFfDYrmA==}
    engines: {node: '>= 10.0.0'}

  '@smithy/util-endpoints@2.0.1':
    resolution: {integrity: sha512-ZRT0VCOnKlVohfoABMc8lWeQo/JEFuPWctfNRXgTHbyOVssMOLYFUNWukxxiHRGVAhV+n3c0kPW+zUqckjVPEA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-hex-encoding@3.0.0':
    resolution: {integrity: sha512-eFndh1WEK5YMUYvy3lPlVmYY/fZcQE1D8oSf41Id2vCeIkKJXPcYDCZD+4+xViI6b1XSd7tE+s5AmXzz5ilabQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-middleware@3.0.0':
    resolution: {integrity: sha512-q5ITdOnV2pXHSVDnKWrwgSNTDBAMHLptFE07ua/5Ty5WJ11bvr0vk2a7agu7qRhrCFRQlno5u3CneU5EELK+DQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-retry@3.0.0':
    resolution: {integrity: sha512-nK99bvJiziGv/UOKJlDvFF45F00WgPLKVIGUfAK+mDhzVN2hb/S33uW2Tlhg5PVBoqY7tDVqL0zmu4OxAHgo9g==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-stream@3.0.1':
    resolution: {integrity: sha512-7F7VNNhAsfMRA8I986YdOY5fE0/T1/ZjFF6OLsqkvQVNP3vZ/szYDfGCyphb7ioA09r32K/0qbSFfNFU68aSzA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-uri-escape@3.0.0':
    resolution: {integrity: sha512-LqR7qYLgZTD7nWLBecUi4aqolw8Mhza9ArpNEQ881MJJIU2sE5iHCK6TdyqqzcDLy0OPe10IY4T8ctVdtynubg==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-utf8@3.0.0':
    resolution: {integrity: sha512-rUeT12bxFnplYDe815GXbq/oixEGHfRFFtcTF3YdDi/JaENIM6aSYYLJydG83UNzLXeRI5K8abYd/8Sp/QM0kA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-waiter@3.0.0':
    resolution: {integrity: sha512-+fEXJxGDLCoqRKVSmo0auGxaqbiCo+8oph+4auefYjaNxjOLKSY2MxVQfRzo65PaZv4fr+5lWg+au7vSuJJ/zw==}
    engines: {node: '>=16.0.0'}

  '@soerenmartius/vue3-clipboard@0.1.2':
    resolution: {integrity: sha512-a5er6cFGcFIQ/3qg6kWvU0yFqIz9iWoQGEVs9azjevyRyMAKvj1w3JuLH9y6+u0WhMNby3kn7nuR8nktSnDesg==}

  '@sphinxxxx/color-conversion@2.2.2':
    resolution: {integrity: sha512-XExJS3cLqgrmNBIP3bBw6+1oQ1ksGjFh0+oClDKFYpCCqx/hlqwWO5KO/S63fzUo67SxI9dMrF0y5T/Ey7h8Zw==}

  '@sxzz/popperjs-es@2.11.7':
    resolution: {integrity: sha512-Ccy0NlLkzr0Ex2FKvh2X+OyERHXJ88XJ1MXtsI9y9fGexlaXaVTPzBCRBwIxFkORuOb+uBqeu+RqnpgYTEZRUQ==}

  '@tiptap/core@2.11.5':
    resolution: {integrity: sha512-jb0KTdUJaJY53JaN7ooY3XAxHQNoMYti/H6ANo707PsLXVeEqJ9o8+eBup1JU5CuwzrgnDc2dECt2WIGX9f8Jw==}
    peerDependencies:
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-blockquote@2.11.5':
    resolution: {integrity: sha512-MZfcRIzKRD8/J1hkt/eYv49060GTL6qGR3NY/oTDuw2wYzbQXXLEbjk8hxAtjwNn7G+pWQv3L+PKFzZDxibLuA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-bold@2.11.5':
    resolution: {integrity: sha512-OAq03MHEbl7MtYCUzGuwb0VpOPnM0k5ekMbEaRILFU5ZC7cEAQ36XmPIw1dQayrcuE8GZL35BKub2qtRxyC9iA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-bubble-menu@2.11.5':
    resolution: {integrity: sha512-rx+rMd7EEdht5EHLWldpkzJ56SWYA9799b33ustePqhXd6linnokJCzBqY13AfZ9+xp3RsR6C0ZHI9GGea0tIA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-bullet-list@2.11.5':
    resolution: {integrity: sha512-VXwHlX6A/T6FAspnyjbKDO0TQ+oetXuat6RY1/JxbXphH42nLuBaGWJ6pgy6xMl6XY8/9oPkTNrfJw/8/eeRwA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-code-block@2.11.5':
    resolution: {integrity: sha512-ksxMMvqLDlC+ftcQLynqZMdlJT1iHYZorXsXw/n+wuRd7YElkRkd6YWUX/Pq/njFY6lDjKiqFLEXBJB8nrzzBA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-code@2.11.5':
    resolution: {integrity: sha512-xOvHevNIQIcCCVn9tpvXa1wBp0wHN/2umbAZGTVzS+AQtM7BTo0tz8IyzwxkcZJaImONcUVYLOLzt2AgW1LltA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-document@2.11.5':
    resolution: {integrity: sha512-7I4BRTpIux2a0O2qS3BDmyZ5LGp3pszKbix32CmeVh7lN9dV7W5reDqtJJ9FCZEEF+pZ6e1/DQA362dflwZw2g==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-dropcursor@2.11.5':
    resolution: {integrity: sha512-uIN7L3FU0904ec7FFFbndO7RQE/yiON4VzAMhNn587LFMyWO8US139HXIL4O8dpZeYwYL3d1FnDTflZl6CwLlg==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-floating-menu@2.11.5':
    resolution: {integrity: sha512-HsMI0hV5Lwzm530Z5tBeyNCBNG38eJ3qjfdV2OHlfSf3+KOEfn6a5AUdoNaZO02LF79/8+7BaYU2drafag9cxQ==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-gapcursor@2.11.5':
    resolution: {integrity: sha512-kcWa+Xq9cb6lBdiICvLReuDtz/rLjFKHWpW3jTTF3FiP3wx4H8Rs6bzVtty7uOVTfwupxZRiKICAMEU6iT0xrQ==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-hard-break@2.11.5':
    resolution: {integrity: sha512-q9doeN+Yg9F5QNTG8pZGYfNye3tmntOwch683v0CCVCI4ldKaLZ0jG3NbBTq+mosHYdgOH2rNbIORlRRsQ+iYQ==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-heading@2.11.5':
    resolution: {integrity: sha512-x/MV53psJ9baRcZ4k4WjnCUBMt8zCX7mPlKVT+9C/o+DEs/j/qxPLs95nHeQv70chZpSwCQCt93xMmuF0kPoAg==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-history@2.11.5':
    resolution: {integrity: sha512-b+wOS33Dz1azw6F1i9LFTEIJ/gUui0Jwz5ZvmVDpL2ZHBhq1Ui0/spTT+tuZOXq7Y/uCbKL8Liu4WoedIvhboQ==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-horizontal-rule@2.11.5':
    resolution: {integrity: sha512-3up2r1Du8/5/4ZYzTC0DjTwhgPI3dn8jhOCLu73m5F3OGvK/9whcXoeWoX103hYMnGDxBlfOje71yQuN35FL4A==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0

  '@tiptap/extension-italic@2.11.5':
    resolution: {integrity: sha512-9VGfb2/LfPhQ6TjzDwuYLRvw0A6VGbaIp3F+5Mql8XVdTBHb2+rhELbyhNGiGVR78CaB/EiKb6dO9xu/tBWSYA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-list-item@2.11.5':
    resolution: {integrity: sha512-Mp5RD/pbkfW1vdc6xMVxXYcta73FOwLmblQlFNn/l/E5/X1DUSA4iGhgDDH4EWO3swbs03x2f7Zka/Xoj3+WLg==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-ordered-list@2.11.5':
    resolution: {integrity: sha512-Cu8KwruBNWAaEfshRQR0yOSaUKAeEwxW7UgbvF9cN/zZuKgK5uZosPCPTehIFCcRe+TBpRtZQh+06f/gNYpYYg==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-paragraph@2.11.5':
    resolution: {integrity: sha512-YFBWeg7xu/sBnsDIF/+nh9Arf7R0h07VZMd0id5Ydd2Qe3c1uIZwXxeINVtH0SZozuPIQFAT8ICe9M0RxmE+TA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-strike@2.11.5':
    resolution: {integrity: sha512-PVfUiCqrjvsLpbIoVlegSY8RlkR64F1Rr2RYmiybQfGbg+AkSZXDeO0eIrc03//4gua7D9DfIozHmAKv1KN3ow==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-text-style@2.11.5':
    resolution: {integrity: sha512-YUmYl0gILSd/u/ZkOmNxjNXVw+mu8fpC2f8G4I4tLODm0zCx09j9DDEJXSrM5XX72nxJQqtSQsCpNKnL0hfeEQ==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/extension-text@2.11.5':
    resolution: {integrity: sha512-Gq1WwyhFpCbEDrLPIHt5A8aLSlf8bfz4jm417c8F/JyU0J5dtYdmx0RAxjnLw1i7ZHE7LRyqqAoS0sl7JHDNSQ==}
    peerDependencies:
      '@tiptap/core': ^2.7.0

  '@tiptap/pm@2.11.5':
    resolution: {integrity: sha512-z9JFtqc5ZOsdQLd9vRnXfTCQ8v5ADAfRt9Nm7SqP6FUHII8E1hs38ACzf5xursmth/VonJYb5+73Pqxk1hGIPw==}

  '@tiptap/starter-kit@2.11.5':
    resolution: {integrity: sha512-SLI7Aj2ruU1t//6Mk8f+fqW+18uTqpdfLUJYgwu0CkqBckrkRZYZh6GVLk/02k3H2ki7QkFxiFbZrdbZdng0JA==}

  '@tiptap/vue-3@2.11.5':
    resolution: {integrity: sha512-etOURQq85MpfkMPeTG0r79s7wmLM7PqLD4aj19m7Fk5rkWuoEZyBinnA+9jfIKEttnhQ1G8PQnHNaqJhfIHQfA==}
    peerDependencies:
      '@tiptap/core': ^2.7.0
      '@tiptap/pm': ^2.7.0
      vue: ^3.0.0

  '@transloadit/prettier-bytes@0.0.7':
    resolution: {integrity: sha512-VeJbUb0wEKbcwaSlj5n+LscBl9IPgLPkHVGBkh00cztv6X4L/TJXK58LzFuBKX7/GAfiGhIwH67YTLTlzvIzBA==}

  '@trysound/sax@0.2.0':
    resolution: {integrity: sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==}
    engines: {node: '>=10.13.0'}

  '@types/estree@1.0.5':
    resolution: {integrity: sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==}

  '@types/event-emitter@0.3.5':
    resolution: {integrity: sha512-zx2/Gg0Eg7gwEiOIIh5w9TrhKKTeQh7CPCOPNc0el4pLSwzebA8SmnHwZs2dWlLONvyulykSwGSQxQHLhjGLvQ==}

  '@types/js-cookie@3.0.6':
    resolution: {integrity: sha512-wkw9yd1kEXOPnvEeEV1Go1MmxtBJL0RR79aOTAApecWFVu7w0NNXNqhcWgvw2YgZDYadliXkl14pa3WXw5jlCQ==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/linkify-it@5.0.0':
    resolution: {integrity: sha512-sVDA58zAw4eWAffKOaQH5/5j3XeayukzDk+ewSsnv3p4yJEZHCCzMDiZM8e0OUrRvmpGZ85jf4yDHkHsgBNr9Q==}

  '@types/lodash-es@4.17.12':
    resolution: {integrity: sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ==}

  '@types/lodash@4.17.0':
    resolution: {integrity: sha512-t7dhREVv6dbNj0q17X12j7yDG4bD/DHYX7o5/DbDxobP0HnGPgpRz2Ej77aL7TZT3DSw13fqUTj8J4mMnqa7WA==}

  '@types/markdown-it@14.1.2':
    resolution: {integrity: sha512-promo4eFwuiW+TfGxhi+0x3czqTYJkG8qB17ZUJiVF10Xm7NLVRSLUsfRTU/6h1e24VvRnXCx+hG7li58lkzog==}

  '@types/mdurl@2.0.0':
    resolution: {integrity: sha512-RGdgjQUZba5p6QEFAVx2OGb8rQDL/cPRG7GiedRzMcJ1tYnUANBncjbSB1NRGwbvjcPeikRABz2nshyPk1bhWg==}

  '@types/node@20.11.30':
    resolution: {integrity: sha512-dHM6ZxwlmuZaRmUPfv1p+KrdD1Dci04FbdEm/9wEMouFqxYoFl5aMkt0VMAUtYRQDyYvD41WJLukhq/ha3YuTw==}

  '@types/nprogress@0.2.3':
    resolution: {integrity: sha512-k7kRA033QNtC+gLc4VPlfnue58CM1iQLgn1IMAU8VPHGOj7oIHPp9UlhedEnD/Gl8evoCjwkZjlBORtZ3JByUA==}

  '@types/path-browserify@1.0.2':
    resolution: {integrity: sha512-ZkC5IUqqIFPXx3ASTTybTzmQdwHwe2C0u3eL75ldQ6T9E9IWFJodn6hIfbZGab73DfyiHN4Xw15gNxUq2FbvBA==}

  '@types/semver@7.5.8':
    resolution: {integrity: sha512-I8EUhyrgfLrcTkzV3TSsGyl1tSuPrEDzr0yd5m90UgNxQkyDXULk3b6MlQqTCpZpNtWe1K0hzclnZkTcLBe2UQ==}

  '@types/svgo@2.6.4':
    resolution: {integrity: sha512-l4cmyPEckf8moNYHdJ+4wkHvFxjyW6ulm9l4YGaOxeyBWPhBOT0gvni1InpFPdzx1dKf/2s62qGITwxNWnPQng==}

  '@types/web-bluetooth@0.0.16':
    resolution: {integrity: sha512-oh8q2Zc32S6gd/j50GowEjKLoOVOwHP/bWVjKJInBwQqdOYMdPrf1oVlelTlyfFK3CKxL1uahMDAr+vy8T7yMQ==}

  '@types/web-bluetooth@0.0.20':
    resolution: {integrity: sha512-g9gZnnXVq7gM7v3tJCWV/qw7w+KeOlSHAhgF9RytFyifW6AF61hdT2ucrYhPq9hLs5JIryeupHV3qGk95dH9ow==}

  '@types/ws@8.5.10':
    resolution: {integrity: sha512-vmQSUcfalpIq0R9q7uTo2lXs6eGIpt9wtnLdMv9LVpIjCA/+ufZRozlVoVelIYixx1ugCBKDhn89vnsEGOCx9A==}

  '@typescript-eslint/eslint-plugin@7.3.1':
    resolution: {integrity: sha512-STEDMVQGww5lhCuNXVSQfbfuNII5E08QWkvAw5Qwf+bj2WT+JkG1uc+5/vXA3AOYMDHVOSpL+9rcbEUiHIm2dw==}
    engines: {node: ^18.18.0 || >=20.0.0}
    peerDependencies:
      '@typescript-eslint/parser': ^7.0.0
      eslint: ^8.56.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/parser@7.3.1':
    resolution: {integrity: sha512-Rq49+pq7viTRCH48XAbTA+wdLRrB/3sRq4Lpk0oGDm0VmnjBrAOVXH/Laalmwsv2VpekiEfVFwJYVk6/e8uvQw==}
    engines: {node: ^18.18.0 || >=20.0.0}
    peerDependencies:
      eslint: ^8.56.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/scope-manager@7.3.1':
    resolution: {integrity: sha512-fVS6fPxldsKY2nFvyT7IP78UO1/I2huG+AYu5AMjCT9wtl6JFiDnsv4uad4jQ0GTFzcUV5HShVeN96/17bTBag==}
    engines: {node: ^18.18.0 || >=20.0.0}

  '@typescript-eslint/type-utils@7.3.1':
    resolution: {integrity: sha512-iFhaysxFsMDQlzJn+vr3OrxN8NmdQkHks4WaqD4QBnt5hsq234wcYdyQ9uquzJJIDAj5W4wQne3yEsYA6OmXGw==}
    engines: {node: ^18.18.0 || >=20.0.0}
    peerDependencies:
      eslint: ^8.56.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/types@7.3.1':
    resolution: {integrity: sha512-2tUf3uWggBDl4S4183nivWQ2HqceOZh1U4hhu4p1tPiIJoRRXrab7Y+Y0p+dozYwZVvLPRI6r5wKe9kToF9FIw==}
    engines: {node: ^18.18.0 || >=20.0.0}

  '@typescript-eslint/typescript-estree@7.3.1':
    resolution: {integrity: sha512-tLpuqM46LVkduWP7JO7yVoWshpJuJzxDOPYIVWUUZbW+4dBpgGeUdl/fQkhuV0A8eGnphYw3pp8d2EnvPOfxmQ==}
    engines: {node: ^18.18.0 || >=20.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/utils@7.3.1':
    resolution: {integrity: sha512-jIERm/6bYQ9HkynYlNZvXpzmXWZGhMbrOvq3jJzOSOlKXsVjrrolzWBjDW6/TvT5Q3WqaN4EkmcfdQwi9tDjBQ==}
    engines: {node: ^18.18.0 || >=20.0.0}
    peerDependencies:
      eslint: ^8.56.0

  '@typescript-eslint/visitor-keys@7.3.1':
    resolution: {integrity: sha512-9RMXwQF8knsZvfv9tdi+4D/j7dMG28X/wMJ8Jj6eOHyHWwDW4ngQJcqEczSsqIKKjFiLFr40Mnr7a5ulDD3vmw==}
    engines: {node: ^18.18.0 || >=20.0.0}

  '@ungap/structured-clone@1.2.0':
    resolution: {integrity: sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ==}

  '@unocss/astro@0.58.6':
    resolution: {integrity: sha512-0BvbhEp5Ln6wFNnhISusB2hcfycWkdgnjlFMcLT69efvj4G39MzB6JYT/1qiidLfpj35HcqkpBz7TfZ4bUmOAw==}
    peerDependencies:
      vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0
    peerDependenciesMeta:
      vite:
        optional: true

  '@unocss/cli@0.58.6':
    resolution: {integrity: sha512-cfJBiEAKElo9vcA4ZU1WTXeUha5qVBtn3+ZjgdgOvveSX0oY0gOheG+DD2LaEjwWzjCstFU38lblUHWzHRv0sQ==}
    engines: {node: '>=14'}
    hasBin: true

  '@unocss/config@0.58.6':
    resolution: {integrity: sha512-YyywfigaEzGkDJ0ztp/tvruD+0xfdCcMZUvaGH4IElAwZDdcSsuaHdR1HW3EixCETOyFHBcuFk6sJwfLPIZLTw==}
    engines: {node: '>=14'}

  '@unocss/core@0.58.6':
    resolution: {integrity: sha512-m87iuENM/PLqLKnfE2mJbpfzj7NQ99LP2go0r+x5X4kXKppyGRJNQg6RIW3AqTS22jup1YPgd62UNXvvRtHtww==}

  '@unocss/extractor-arbitrary-variants@0.58.6':
    resolution: {integrity: sha512-iSrjLEM+dtcpCmJ8myttPZ/0Dg4HKYVYpM71rNTybrX1HB/cfRsGwIHobjsvvRnve+LAxC4Hmdi8EXRd8V1xPg==}

  '@unocss/inspector@0.58.6':
    resolution: {integrity: sha512-JCcnIwSofdIdpR/GYTZ78d4dMZP8Z2oF/g9f9MlPG96LLLwqXSmYtR83vC34fUWm1hz7FZyTLK5OqTV7VfUb+w==}

  '@unocss/postcss@0.58.6':
    resolution: {integrity: sha512-jTwu7llhRm7luscsk0JekCeo6RS2W98CXiCJRE1H6IDQnjGQQ/9uRAqW4wuLsv7OQ1ThF8m9NKEj0wFJv7ePNg==}
    engines: {node: '>=14'}
    peerDependencies:
      postcss: ^8.4.21

  '@unocss/preset-attributify@0.58.6':
    resolution: {integrity: sha512-IW2aIBH0RiYsP50VckgxXhMwjDk/XVZykUUklgko9aT+gNC2xDBm3936RWLVLmHdBHG1Vnnjc71BG6aUAqrtEA==}

  '@unocss/preset-icons@0.58.6':
    resolution: {integrity: sha512-20P1ISxEX2J5mhyNkMm0nKp2p0qJfmDArolC10Fjyl+GUTCsojDNFfonzQEkHeIIJwzgRcubdtNaotTgYSKSRg==}

  '@unocss/preset-mini@0.58.6':
    resolution: {integrity: sha512-COaM/04Z6/gawzrFcCwoyD6t0FCpuKRyljKKs3VxitoGxsN9BHKiFE3U/cPSQarpG789rmZg8D/wmLHCChSXvg==}

  '@unocss/preset-tagify@0.58.6':
    resolution: {integrity: sha512-xhpHMz8KLrFlLXaOCeShu0/yVpe6J8bd2WcOTd5VuU5W1uCsplcaagpfTRkBpaH1tJt/MTTU5e4cQLa20l0JZQ==}

  '@unocss/preset-typography@0.58.6':
    resolution: {integrity: sha512-s4SSw3SK/JjAqYlga51XnxzPc7jF+TmkEkW09IkYYLPrmqJ76U4i5Po2N7HzyjpVGWnmIp1fQ5mCvbhj4tNyNQ==}

  '@unocss/preset-uno@0.58.6':
    resolution: {integrity: sha512-QFbgvxCvTFqKIA/VGcPM+XcfPGDgbmaiXKbnh7hIbkER6TRxjt4DIGapPt150w/kQT+zC/jerWditSdyn+s3xQ==}

  '@unocss/preset-web-fonts@0.58.6':
    resolution: {integrity: sha512-W/8102vurABwfnb9QQyp9GBKP80kpy/IADSM7hbqLNaqmgxHASELXkjhqqt+et39Zn2tnXpIHNVWoqG6QNWBwQ==}

  '@unocss/preset-wind@0.58.6':
    resolution: {integrity: sha512-zs41CpFVvLB8CutroUeI1iqxzPsztiur9ei0QjRshxpYerPSsTSgdTkF+eul5D1q3BcsebLebzqtCSqnpFOuOA==}

  '@unocss/reset@0.58.6':
    resolution: {integrity: sha512-mYyOe5QDRHc1BBRff+G97j3SGGC8s3Vk7cCcXc/l48FvPHKtLCphMZaBS+KkGkzWqwAtxv0QRbGhTixJCVDRug==}

  '@unocss/rule-utils@0.58.6':
    resolution: {integrity: sha512-qbVDeyKzExFY/YeoIbMw/lu8PXEGJzSHrFXRumNHX5lvTu8ggAjH6p9xyvYa/YnuNU0+pFKuYxG8LNK2F8f1EQ==}
    engines: {node: '>=14'}

  '@unocss/scope@0.58.6':
    resolution: {integrity: sha512-22BDrZPOkfEKuXY4NROhY+J25TAXN+GIxZpE4IbT1Wh+dkjnMXyC85VKGzOAuY/0v0zbaj12dYkmtndueQ/04w==}

  '@unocss/transformer-attributify-jsx-babel@0.58.6':
    resolution: {integrity: sha512-IVU/ZozKTFhP9z1I9ZgSKiEx3WfDFDNXbTYTqwNW2SHPmAj5Qf99kn6o7br7Kd1dnxSjKFXOmNfNGkW9AMDraA==}

  '@unocss/transformer-attributify-jsx@0.58.6':
    resolution: {integrity: sha512-S+mTR5qwMsEaq8nYtqUgYdYIIRAA4oltxfGMYGj3Gxy9V0uteyo56QNp7IOjlYxAB8Dq9jQ8GLYEQe5JlffO1A==}

  '@unocss/transformer-compile-class@0.58.6':
    resolution: {integrity: sha512-iung3cPA+IpBHoP6dTs9gkPN/wEVujqMZYxf6FQLjRH+v70rPxqTgaeGDyuQZAYikfDhdUZF8PJ4IUMOxEFsmw==}

  '@unocss/transformer-directives@0.58.6':
    resolution: {integrity: sha512-8/+KGRmRnlkAJCRt4VqcYK16SnEr9wf1q7D9OCM2kZhk33Sx5SlsO6vjNZ1IH4c67/WviwNV6+KeS4elnQY8bA==}

  '@unocss/transformer-variant-group@0.58.6':
    resolution: {integrity: sha512-6IFmfzketh4j+Mc/ik4nU+Oec0cptNR+aVfZZkGb4wd2h1lH+teAT2Y/Vz2xY8rDExOrbjY5y5FgRGd16LY2Rw==}

  '@unocss/vite@0.58.6':
    resolution: {integrity: sha512-DPXCoYU/Ozqc/Jeptd41XvtW8MSgVxmtTyhpMAsm/hJuBfwIV7Fy3TZquf4V9BpaTb4ao1LVXzgXmVUmj2HXpA==}
    peerDependencies:
      vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0

  '@uppy/companion-client@2.2.2':
    resolution: {integrity: sha512-5mTp2iq97/mYSisMaBtFRry6PTgZA6SIL7LePteOV5x0/DxKfrZW3DEiQERJmYpHzy7k8johpm2gHnEKto56Og==}

  '@uppy/core@2.3.4':
    resolution: {integrity: sha512-iWAqppC8FD8mMVqewavCz+TNaet6HPXitmGXpGGREGrakZ4FeuWytVdrelydzTdXx6vVKkOmI2FLztGg73sENQ==}

  '@uppy/store-default@2.1.1':
    resolution: {integrity: sha512-xnpTxvot2SeAwGwbvmJ899ASk5tYXhmZzD/aCFsXePh/v8rNvR2pKlcQUH7cF/y4baUGq3FHO/daKCok/mpKqQ==}

  '@uppy/utils@4.1.3':
    resolution: {integrity: sha512-nTuMvwWYobnJcytDO3t+D6IkVq/Qs4Xv3vyoEZ+Iaf8gegZP+rEyoaFT2CK5XLRMienPyqRqNbIfRuFaOWSIFw==}

  '@uppy/xhr-upload@2.1.3':
    resolution: {integrity: sha512-YWOQ6myBVPs+mhNjfdWsQyMRWUlrDLMoaG7nvf/G6Y3GKZf8AyjFDjvvJ49XWQ+DaZOftGkHmF1uh/DBeGivJQ==}
    peerDependencies:
      '@uppy/core': ^2.3.3

  '@vitejs/plugin-vue-jsx@3.1.0':
    resolution: {integrity: sha512-w9M6F3LSEU5kszVb9An2/MmXNxocAnUb3WhRr8bHlimhDrXNt6n6D2nJQR3UXpGlZHh/EsgouOHCsM8V3Ln+WA==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^4.0.0 || ^5.0.0
      vue: ^3.0.0

  '@vitejs/plugin-vue@5.0.4':
    resolution: {integrity: sha512-WS3hevEszI6CEVEx28F8RjTX97k3KsrcY6kvTg7+Whm5y3oYvcqzVeGCU3hxSAn4uY2CLCkeokkGKpoctccilQ==}
    engines: {node: ^18.0.0 || >=20.0.0}
    peerDependencies:
      vite: ^5.0.0
      vue: ^3.2.25

  '@vitest/expect@1.4.0':
    resolution: {integrity: sha512-Jths0sWCJZ8BxjKe+p+eKsoqev1/T8lYcrjavEaz8auEJ4jAVY0GwW3JKmdVU4mmNPLPHixh4GNXP7GFtAiDHA==}

  '@vitest/runner@1.4.0':
    resolution: {integrity: sha512-EDYVSmesqlQ4RD2VvWo3hQgTJ7ZrFQ2VSJdfiJiArkCerDAGeyF1i6dHkmySqk573jLp6d/cfqCN+7wUB5tLgg==}

  '@vitest/snapshot@1.4.0':
    resolution: {integrity: sha512-saAFnt5pPIA5qDGxOHxJ/XxhMFKkUSBJmVt5VgDsAqPTX6JP326r5C/c9UuCMPoXNzuudTPsYDZCoJ5ilpqG2A==}

  '@vitest/spy@1.4.0':
    resolution: {integrity: sha512-Ywau/Qs1DzM/8Uc+yA77CwSegizMlcgTJuYGAi0jujOteJOUf1ujunHThYo243KG9nAyWT3L9ifPYZ5+As/+6Q==}

  '@vitest/utils@1.4.0':
    resolution: {integrity: sha512-mx3Yd1/6e2Vt/PUC98DcqTirtfxUyAZ32uK82r8rZzbtBeBo+nqgnjx/LvqQdWsrvNtm14VmurNgcf4nqY5gJg==}

  '@volar/language-core@2.1.5':
    resolution: {integrity: sha512-u1OHmVkCFsJqNdaM2GKuMhE67TxcEnOqJNF+VtYv2Ji8DnrUaF4FAFSNxY+MRGICl+873CsSJVKas9TQtW14LA==}

  '@volar/source-map@2.1.5':
    resolution: {integrity: sha512-GIkAM6fHgDcTXcdH4i10fAiAZzO0HLIer8/pt3oZ9A0n7n4R5d1b2F8Xxzh/pgmgNoL+SrHX3MFxs35CKgfmtA==}

  '@volar/typescript@2.1.5':
    resolution: {integrity: sha512-zo9a3NrNMSkufIvHuExDGTfYv+zO7C5p2wg8fyP7vcqF/Qo0ztjb0ZfOgq/A85EO/MBc1Kj2Iu7PaOBtP++NMw==}

  '@vue/babel-helper-vue-transform-on@1.2.2':
    resolution: {integrity: sha512-nOttamHUR3YzdEqdM/XXDyCSdxMA9VizUKoroLX6yTyRtggzQMHXcmwh8a7ZErcJttIBIc9s68a1B8GZ+Dmvsw==}

  '@vue/babel-plugin-jsx@1.2.2':
    resolution: {integrity: sha512-nYTkZUVTu4nhP199UoORePsql0l+wj7v/oyQjtThUVhJl1U+6qHuoVhIvR3bf7eVKjbCK+Cs2AWd7mi9Mpz9rA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    peerDependenciesMeta:
      '@babel/core':
        optional: true

  '@vue/babel-plugin-resolve-type@1.2.2':
    resolution: {integrity: sha512-EntyroPwNg5IPVdUJupqs0CFzuf6lUrVvCspmv2J1FITLeGnUCuoGNNk78dgCusxEiYj6RMkTJflGSxk5aIC4A==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@vue/compiler-core@3.4.21':
    resolution: {integrity: sha512-MjXawxZf2SbZszLPYxaFCjxfibYrzr3eYbKxwpLR9EQN+oaziSu3qKVbwBERj1IFIB8OLUewxB5m/BFzi613og==}

  '@vue/compiler-dom@3.4.21':
    resolution: {integrity: sha512-IZC6FKowtT1sl0CR5DpXSiEB5ayw75oT2bma1BEhV7RRR1+cfwLrxc2Z8Zq/RGFzJ8w5r9QtCOvTjQgdn0IKmA==}

  '@vue/compiler-sfc@3.4.21':
    resolution: {integrity: sha512-me7epoTxYlY+2CUM7hy9PCDdpMPfIwrOvAXud2Upk10g4YLv9UBW7kL798TvMeDhPthkZ0CONNrK2GoeI1ODiQ==}

  '@vue/compiler-ssr@3.4.21':
    resolution: {integrity: sha512-M5+9nI2lPpAsgXOGQobnIueVqc9sisBFexh5yMIMRAPYLa7+5wEJs8iqOZc1WAa9WQbx9GR2twgznU8LTIiZ4Q==}

  '@vue/devtools-api@6.6.1':
    resolution: {integrity: sha512-LgPscpE3Vs0x96PzSSB4IGVSZXZBZHpfxs+ZA1d+VEPwHdOXowy/Y2CsvCAIFrf+ssVU1pD1jidj505EpUnfbA==}

  '@vue/language-core@2.0.7':
    resolution: {integrity: sha512-Vh1yZX3XmYjn9yYLkjU8DN6L0ceBtEcapqiyclHne8guG84IaTzqtvizZB1Yfxm3h6m7EIvjerLO5fvOZO6IIQ==}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@vue/reactivity@3.4.21':
    resolution: {integrity: sha512-UhenImdc0L0/4ahGCyEzc/pZNwVgcglGy9HVzJ1Bq2Mm9qXOpP8RyNTjookw/gOCUlXSEtuZ2fUg5nrHcoqJcw==}

  '@vue/runtime-core@3.4.21':
    resolution: {integrity: sha512-pQthsuYzE1XcGZznTKn73G0s14eCJcjaLvp3/DKeYWoFacD9glJoqlNBxt3W2c5S40t6CCcpPf+jG01N3ULyrA==}

  '@vue/runtime-dom@3.4.21':
    resolution: {integrity: sha512-gvf+C9cFpevsQxbkRBS1NpU8CqxKw0ebqMvLwcGQrNpx6gqRDodqKqA+A2VZZpQ9RpK2f9yfg8VbW/EpdFUOJw==}

  '@vue/server-renderer@3.4.21':
    resolution: {integrity: sha512-aV1gXyKSN6Rz+6kZ6kr5+Ll14YzmIbeuWe7ryJl5muJ4uwSwY/aStXTixx76TwkZFJLm1aAlA/HSWEJ4EyiMkg==}
    peerDependencies:
      vue: 3.4.21

  '@vue/shared@3.4.21':
    resolution: {integrity: sha512-PuJe7vDIi6VYSinuEbUIQgMIRZGgM8e4R+G+/dQTk0X1NEdvgvvgv7m+rfmDH1gZzyA1OjjoWskvHlfRNfQf3g==}

  '@vue/test-utils@2.4.5':
    resolution: {integrity: sha512-oo2u7vktOyKUked36R93NB7mg2B+N7Plr8lxp2JBGwr18ch6EggFjixSCdIVVLkT6Qr0z359Xvnafc9dcKyDUg==}

  '@vueuse/core@10.9.0':
    resolution: {integrity: sha512-/1vjTol8SXnx6xewDEKfS0Ra//ncg4Hb0DaZiwKf7drgfMsKFExQ+FnnENcN6efPen+1kIzhLQoGSy0eDUVOMg==}

  '@vueuse/core@9.13.0':
    resolution: {integrity: sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw==}

  '@vueuse/metadata@10.9.0':
    resolution: {integrity: sha512-iddNbg3yZM0X7qFY2sAotomgdHK7YJ6sKUvQqbvwnf7TmaVPxS4EJydcNsVejNdS8iWCtDk+fYXr7E32nyTnGA==}

  '@vueuse/metadata@9.13.0':
    resolution: {integrity: sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ==}

  '@vueuse/shared@10.9.0':
    resolution: {integrity: sha512-Uud2IWncmAfJvRaFYzv5OHDli+FbOzxiVEQdLCKQKLyhz94PIyFC3CHcH7EDMwIn8NPtD06+PNbC/PiO0LGLtw==}

  '@vueuse/shared@9.13.0':
    resolution: {integrity: sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw==}

  '@wangeditor-next/basic-modules@1.5.29':
    resolution: {integrity: sha512-GjcdCWN7OMnfDMGyeSl33Y/f2i7pKahIQwrqLFGXEAfd3bnzevsSYQFJCRQSeVX0Jo0/REJKyEAC0Nvy5ecSmw==}
    peerDependencies:
      '@wangeditor-next/core': 1.7.29
      dom7: ^3.0.0 || ^4.0.0
      lodash.throttle: ^4.1.1
      nanoid: ^5.0.0
      slate: ^0.72.0
      snabbdom: ^3.1.0

  '@wangeditor-next/code-highlight@1.3.26':
    resolution: {integrity: sha512-fis/CPTyhW2Gp0Z2h6rA1RB3vW+pVEPtlFe5M7e1ynQraYNiaaVf/tDYlma2y6pXlvZ+4wcaDdbQrmybyYZ5rw==}
    peerDependencies:
      '@wangeditor-next/core': 1.7.29
      dom7: ^3.0.0 || ^4.0.0
      slate: ^0.72.0
      snabbdom: ^3.1.0

  '@wangeditor-next/core@1.7.29':
    resolution: {integrity: sha512-hHYQpOzukc2FCW7EAV4pkx1xa5/BPlETjA4h6XdS/mGrTQSXiLalSQgIweyTjiQu0Z9Pdfh0x4aijNh/bSzNSg==}
    peerDependencies:
      '@uppy/core': ^2.1.1
      '@uppy/xhr-upload': ^2.0.3
      dom7: ^3.0.0 || ^4.0.0
      is-hotkey: ^0.2.0
      lodash.camelcase: ^4.3.0
      lodash.clonedeep: ^4.5.0
      lodash.debounce: ^4.0.8
      lodash.foreach: ^4.5.0
      lodash.isequal: ^4.5.0
      lodash.throttle: ^4.1.1
      lodash.toarray: ^4.4.0
      nanoid: ^5.0.0
      slate: ^0.72.0
      snabbdom: ^3.1.0

  '@wangeditor-next/editor-for-vue@5.1.14':
    resolution: {integrity: sha512-Xkrdo590AhLHvzyR+U246t6T89nIWHz1weAgMuo8jEA2HS5RiUnsA4U6+iUGaQ2E5c8mYQaeNqzHQXUp9Okbiw==}
    peerDependencies:
      '@wangeditor-next/editor': '>=5.1.0'
      vue: ^3.0.5

  '@wangeditor-next/editor@5.6.27':
    resolution: {integrity: sha512-i6T7H8BXBQ3c73KRUyy4s+UoJ4SOt8pEjt63oHmE/P6kORtZJ8C1QulryRjN9WYky3ZjSwv4Luo0Re2ClXayfA==}

  '@wangeditor-next/list-module@1.1.35':
    resolution: {integrity: sha512-6mW9diRYgsqTdyXMeoCMx6u9nHlpENFGAdR4YEjfQEr6s4kMtiKLOdNipStZZKOYJuRIpsNS6QJo52pgMkXYKg==}
    peerDependencies:
      '@wangeditor-next/core': 1.7.29
      dom7: ^3.0.0 || ^4.0.0
      slate: ^0.72.0
      snabbdom: ^3.1.0

  '@wangeditor-next/table-module@1.6.38':
    resolution: {integrity: sha512-3xt3G4ZiIAntuqesIuE0V7L822EXGAn4AoRcVz8hsL4XWRCAltcuhdvyVzIKF5ACyYW9dmFwkCLD4dI9C/7USw==}
    peerDependencies:
      '@wangeditor-next/core': 1.7.29
      dom7: ^3.0.0 || ^4.0.0
      lodash.debounce: ^4.0.8
      lodash.isequal: ^4.5.0
      lodash.throttle: ^4.1.1
      nanoid: ^5.0.0
      slate: ^0.72.0
      snabbdom: ^3.1.0

  '@wangeditor-next/upload-image-module@1.1.32':
    resolution: {integrity: sha512-duqE6qBsjFwcZhx46TOxs7Oh9PXUvdwk0nzr3jVPGdxhXOLy1JzXwi3aDBtLPm/8dzkdj8vGsm19BohFeCjhzg==}
    peerDependencies:
      '@uppy/core': ^2.0.3
      '@uppy/xhr-upload': ^2.0.3
      '@wangeditor-next/basic-modules': 1.5.29
      '@wangeditor-next/core': 1.7.29
      dom7: ^3.0.0 || ^4.0.0
      lodash.foreach: ^4.5.0
      slate: ^0.72.0
      snabbdom: ^3.1.0

  '@wangeditor-next/video-module@1.3.35':
    resolution: {integrity: sha512-yNr2Hh5HABYgBlvUe5tKzrrv0CkVohgwjLaQfQPc2MOZD38nHhmW0L5YywIUPtTCXxAsyzTAwtx5nWe+2XoPPg==}
    peerDependencies:
      '@uppy/core': ^2.1.4
      '@uppy/xhr-upload': ^2.0.7
      '@wangeditor-next/core': 1.7.29
      dom7: ^3.0.0 || ^4.0.0
      nanoid: ^5.0.0
      slate: ^0.72.0
      snabbdom: ^3.1.0

  '@wangeditor/basic-modules@1.1.7':
    resolution: {integrity: sha512-cY9CPkLJaqF05STqfpZKWG4LpxTMeGSIIF1fHvfm/mz+JXatCagjdkbxdikOuKYlxDdeqvOeBmsUBItufDLXZg==}
    peerDependencies:
      '@wangeditor/core': 1.x
      dom7: ^3.0.0
      lodash.throttle: ^4.1.1
      nanoid: ^3.2.0
      slate: ^0.72.0
      snabbdom: ^3.1.0

  '@wangeditor/code-highlight@1.0.3':
    resolution: {integrity: sha512-iazHwO14XpCuIWJNTQTikqUhGKyqj+dUNWJ9288Oym9M2xMVHvnsOmDU2sgUDWVy+pOLojReMPgXCsvvNlOOhw==}
    peerDependencies:
      '@wangeditor/core': 1.x
      dom7: ^3.0.0
      slate: ^0.72.0
      snabbdom: ^3.1.0

  '@wangeditor/core@1.1.19':
    resolution: {integrity: sha512-KevkB47+7GhVszyYF2pKGKtCSj/YzmClsD03C3zTt+9SR2XWT5T0e3yQqg8baZpcMvkjs1D8Dv4fk8ok/UaS2Q==}
    peerDependencies:
      '@uppy/core': ^2.1.1
      '@uppy/xhr-upload': ^2.0.3
      dom7: ^3.0.0
      is-hotkey: ^0.2.0
      lodash.camelcase: ^4.3.0
      lodash.clonedeep: ^4.5.0
      lodash.debounce: ^4.0.8
      lodash.foreach: ^4.5.0
      lodash.isequal: ^4.5.0
      lodash.throttle: ^4.1.1
      lodash.toarray: ^4.4.0
      nanoid: ^3.2.0
      slate: ^0.72.0
      snabbdom: ^3.1.0

  '@wangeditor/editor-for-vue@5.1.12':
    resolution: {integrity: sha512-0Ds3D8I+xnpNWezAeO7HmPRgTfUxHLMd9JKcIw+QzvSmhC5xUHbpCcLU+KLmeBKTR/zffnS5GQo6qi3GhTMJWQ==}
    peerDependencies:
      '@wangeditor/editor': '>=5.1.0'
      vue: ^3.0.5

  '@wangeditor/editor@5.1.23':
    resolution: {integrity: sha512-0RxfeVTuK1tktUaPROnCoFfaHVJpRAIE2zdS0mpP+vq1axVQpLjM8+fCvKzqYIkH0Pg+C+44hJpe3VVroSkEuQ==}

  '@wangeditor/list-module@1.0.5':
    resolution: {integrity: sha512-uDuYTP6DVhcYf7mF1pTlmNn5jOb4QtcVhYwSSAkyg09zqxI1qBqsfUnveeDeDqIuptSJhkh81cyxi+MF8sEPOQ==}
    peerDependencies:
      '@wangeditor/core': 1.x
      dom7: ^3.0.0
      slate: ^0.72.0
      snabbdom: ^3.1.0

  '@wangeditor/table-module@1.1.4':
    resolution: {integrity: sha512-5saanU9xuEocxaemGdNi9t8MCDSucnykEC6jtuiT72kt+/Hhh4nERYx1J20OPsTCCdVr7hIyQenFD1iSRkIQ6w==}
    peerDependencies:
      '@wangeditor/core': 1.x
      dom7: ^3.0.0
      lodash.isequal: ^4.5.0
      lodash.throttle: ^4.1.1
      nanoid: ^3.2.0
      slate: ^0.72.0
      snabbdom: ^3.1.0

  '@wangeditor/upload-image-module@1.0.2':
    resolution: {integrity: sha512-z81lk/v71OwPDYeQDxj6cVr81aDP90aFuywb8nPD6eQeECtOymrqRODjpO6VGvCVxVck8nUxBHtbxKtjgcwyiA==}
    peerDependencies:
      '@uppy/core': ^2.0.3
      '@uppy/xhr-upload': ^2.0.3
      '@wangeditor/basic-modules': 1.x
      '@wangeditor/core': 1.x
      dom7: ^3.0.0
      lodash.foreach: ^4.5.0
      slate: ^0.72.0
      snabbdom: ^3.1.0

  '@wangeditor/video-module@1.1.4':
    resolution: {integrity: sha512-ZdodDPqKQrgx3IwWu4ZiQmXI8EXZ3hm2/fM6E3t5dB8tCaIGWQZhmqd6P5knfkRAd3z2+YRSRbxOGfoRSp/rLg==}
    peerDependencies:
      '@uppy/core': ^2.1.4
      '@uppy/xhr-upload': ^2.0.7
      '@wangeditor/core': 1.x
      dom7: ^3.0.0
      nanoid: ^3.2.0
      slate: ^0.72.0
      snabbdom: ^3.1.0

  '@xmldom/xmldom@0.8.10':
    resolution: {integrity: sha512-2WALfTl4xo2SkGCYRt6rDTFfk9R1czmBvUQy12gK2KuRKIpWEhcbbzy8EZXtz/jkRqHX8bFEc6FC1HjX4TUWYw==}
    engines: {node: '>=10.0.0'}

  abbrev@2.0.0:
    resolution: {integrity: sha512-6/mh1E2u2YgEsCHdY0Yx5oW+61gZU+1vXaoiHHrpKeuRNNgFvS+/jrwHiQhB5apAf5oB7UB7E19ol2R2LKH8hQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  ace-builds@1.34.2:
    resolution: {integrity: sha512-wiOZYuxyOSYfZzDasQTe+ZWmRlYxXSJM/kMKZ/bSqO1VgrBl+PaaTz/Sc+y7hXCKAUj3syUdpwxQyvwv9vQe6w==}

  acorn-jsx@5.3.2:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn-walk@8.3.2:
    resolution: {integrity: sha512-cjkyv4OtNCIeqhHrfS81QWXoCBPExR/J62oyEqepVw8WaQeSqpW2uhuLPh1m9eWhDuOo/jUXVTlifvesOWp/4A==}
    engines: {node: '>=0.4.0'}

  acorn@8.11.3:
    resolution: {integrity: sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  add@2.0.6:
    resolution: {integrity: sha512-j5QzrmsokwWWp6kUcJQySpbG+xfOBqqKnup3OIk1pz+kB/80SLorZ9V8zHFLO92Lcd+hbvq8bT+zOGoPkmBV0Q==}

  address@1.2.2:
    resolution: {integrity: sha512-4B/qKCfeE/ODUaAUpSwfzazo5x29WD4r3vXiWsB7I2mSDAihwEqKO+g8GELZUQSSAo5e1XTYh3ZVfLyxBc12nA==}
    engines: {node: '>= 10.0.0'}

  adler-32@1.3.1:
    resolution: {integrity: sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A==}
    engines: {node: '>=0.8'}

  agent-base@7.1.0:
    resolution: {integrity: sha512-o/zjMZRhJxny7OyEF+Op8X+efiELC7k7yOjMzgfzVqOzXqkBkWI79YoTdOtsuWd5BWhAGAuOY/Xa6xpiaWXiNg==}
    engines: {node: '>= 14'}

  agentkeepalive@3.5.3:
    resolution: {integrity: sha512-yqXL+k5rr8+ZRpOAntkaaRgWgE5o8ESAj5DyRmVTCSoZxXmqemb9Dd7T4i5UzwuERdLAJUy6XzR9zFVuf0kzkw==}
    engines: {node: '>= 4.0.0'}

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ali-oss@6.20.0:
    resolution: {integrity: sha512-TzFXgGlw81sy2JvcCveSYsa2b2+6kv+HA6WTc+cXg6bu8nUAmVPfncRGbn3x2getSOniOFA+TyGy3V4l3Fks+Q==}
    engines: {node: '>=8'}

  ansi-escapes@6.2.1:
    resolution: {integrity: sha512-4nJ3yixlEthEJ9Rk4vPcdBRkZvQZlYyu8j4/Mqz5sgIkddmEnH2Yj2ZrnP9S3tQOvSNRUIgVNF/1yPpRAGNRig==}
    engines: {node: '>=14.16'}

  ansi-regex@2.1.1:
    resolution: {integrity: sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==}
    engines: {node: '>=0.10.0'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-regex@6.0.1:
    resolution: {integrity: sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==}
    engines: {node: '>=12'}

  ansi-styles@2.2.1:
    resolution: {integrity: sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA==}
    engines: {node: '>=0.10.0'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@5.2.0:
    resolution: {integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==}
    engines: {node: '>=10'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  any-promise@1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  arr-diff@4.0.0:
    resolution: {integrity: sha512-YVIQ82gZPGBebQV/a8dar4AitzCQs0jjXwMPZllpXMaGjXPYVUawSxQrRsjhjupyVxEvbHgUmIhKVlND+j02kA==}
    engines: {node: '>=0.10.0'}

  arr-flatten@1.1.0:
    resolution: {integrity: sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==}
    engines: {node: '>=0.10.0'}

  arr-union@3.1.0:
    resolution: {integrity: sha512-sKpyeERZ02v1FeCZT8lrfJq5u6goHCtpTAzPwJYe7c8SPFOboNjNg1vz2L4VTn9T4PQxEx13TbXLmYUcS6Ug7Q==}
    engines: {node: '>=0.10.0'}

  array-union@2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}

  array-unique@0.3.2:
    resolution: {integrity: sha512-SleRWjh9JUud2wH1hPs9rZBZ33H6T9HOiL0uwGnGx9FpE6wKGyfWugmbkEOIs6qWrZhg0LWeLziLrEwQJhs5mQ==}
    engines: {node: '>=0.10.0'}

  assertion-error@1.1.0:
    resolution: {integrity: sha512-jgsaNduz+ndvGyFt3uSuWqvy4lCnIJiovtouQN5JZHOKCS2QuhEdbcQHFhVksz2N2U9hXJo8odG7ETyWlEeuDw==}

  assign-symbols@1.0.0:
    resolution: {integrity: sha512-Q+JC7Whu8HhmTdBph/Tq59IoRtoy6KAm5zzPv00WdujX82lbAL8K7WVjne7vdCsAmbF4AYaDOPyO3k0kl8qIrw==}
    engines: {node: '>=0.10.0'}

  async-validator@4.2.5:
    resolution: {integrity: sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  atob@2.1.2:
    resolution: {integrity: sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==}
    engines: {node: '>= 4.5.0'}
    hasBin: true

  aws-crt@1.21.2:
    resolution: {integrity: sha512-/jq5yJwdethIaC+HyqCrgW2ZqxjUURBo/6CIfnD0HbpIE+vLIPQxENW/pp3Atu20Hm2WkjM9diJD4zJycK+olA==}

  axios@1.6.8:
    resolution: {integrity: sha512-v/ZHtJDU39mDpyBoFVkETcd/uNdxrWRrg3bKpOKzXFA6Bvqopts6ALSMU3y6ijYxbw2B+wPrIv46egTzJXCLGQ==}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  base@0.11.2:
    resolution: {integrity: sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg==}
    engines: {node: '>=0.10.0'}

  big.js@5.2.2:
    resolution: {integrity: sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==}

  binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}

  bl@4.1.0:
    resolution: {integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==}

  bluebird@3.7.2:
    resolution: {integrity: sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==}

  boolbase@1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==}

  bowser@1.9.4:
    resolution: {integrity: sha512-9IdMmj2KjigRq6oWhmwv1W36pDuA4STQZ8q6YO9um+x07xgYNCD3Oou+WP/3L1HNz7iqythGet3/p4wvc8AAwQ==}

  bowser@2.11.0:
    resolution: {integrity: sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==}

  brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}

  brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}

  braces@2.3.2:
    resolution: {integrity: sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==}
    engines: {node: '>=0.10.0'}

  braces@3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    engines: {node: '>=8'}

  browserslist@4.23.0:
    resolution: {integrity: sha512-QW8HiM1shhT2GuzkvklfjcKDiWFXHOeFCIA/huJPwHsslwcydgk7X+z2zXpEijP98UCY7HbubZt5J2Zgvf0CaQ==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  buffer@5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==}

  buffer@6.0.3:
    resolution: {integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==}

  builtin-status-codes@3.0.0:
    resolution: {integrity: sha512-HpGFw18DgFWlncDfjTa2rcQ4W88O1mC8e8yZ2AvQY5KDaktSTwo+KRf6nHK6FRI5FyRyb/5T6+TSxfP7QyGsmQ==}

  cac@6.7.14:
    resolution: {integrity: sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==}
    engines: {node: '>=8'}

  cache-base@1.0.1:
    resolution: {integrity: sha512-AKcdTnFSWATd5/GCPRxr2ChwIJ85CeyrEyjRHlKxQ56d4XJMGym0uAiKn0xbLOGOl3+yRpOTi484dVCEc5AUzQ==}
    engines: {node: '>=0.10.0'}

  call-bind@1.0.7:
    resolution: {integrity: sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  camelcase@6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==}
    engines: {node: '>=10'}

  caniuse-lite@1.0.30001600:
    resolution: {integrity: sha512-+2S9/2JFhYmYaDpZvo0lKkfvuKIglrx68MwOBqMGHhQsNkLjB5xtc/TGoEPs+MxjSyN/72qer2g97nzR641mOQ==}

  cfb@1.2.2:
    resolution: {integrity: sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==}
    engines: {node: '>=0.8'}

  chai@4.4.1:
    resolution: {integrity: sha512-13sOfMv2+DWduEU+/xbun3LScLoqN17nBeTLUsmDfKdoiC1fr0n9PU4guu4AhRcOVFk/sW8LyZWHuhWtQZiF+g==}
    engines: {node: '>=4'}

  chalk@1.1.3:
    resolution: {integrity: sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==}
    engines: {node: '>=0.10.0'}

  chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  chalk@5.3.0:
    resolution: {integrity: sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}

  check-error@1.0.3:
    resolution: {integrity: sha512-iKEoDYaRmd1mxM90a2OEfWhjsjPpYPuQ+lMYsoxB126+t8fw7ySEO48nmDg5COTjxDI65/Y2OWpeEHk3ZOe8zg==}

  chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}

  class-utils@0.3.6:
    resolution: {integrity: sha512-qOhPa/Fj7s6TY8H8esGu5QNpMMQxz79h+urzrNYN6mn+9BnxlDGf5QZ+XeCDsxSjPqsSR56XOZOJmpeurnLMeg==}
    engines: {node: '>=0.10.0'}

  cli-cursor@4.0.0:
    resolution: {integrity: sha512-VGtlMu3x/4DOtIUwEkRezxUZ2lBacNJCHash0N0WeZDBS+7Ux1dm3XWAgWYxLJFMMdOeXMHXorshEFhbMSGelg==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  cli-truncate@4.0.0:
    resolution: {integrity: sha512-nPdaFdQ0h/GEigbPClz11D0v/ZJEwxmeVZGeMo3Z5StPtUTkA9o1lD6QwoirYiSDzbcwn2XcjwmCp68W1IS4TA==}
    engines: {node: '>=18'}

  clipboard@2.0.11:
    resolution: {integrity: sha512-C+0bbOqkezLIsmWSvlsXS0Q0bmkugu7jcfMIACB+RDEntIzQIkdr148we28AfSloQLRdZlYL/QYyrq05j/3Faw==}

  clone@2.1.2:
    resolution: {integrity: sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==}
    engines: {node: '>=0.8'}

  codepage@1.15.0:
    resolution: {integrity: sha512-3g6NUTPd/YtuuGrhMnOMRjFc+LJw/bnMp3+0r/Wcz3IXUuCosKRJvMphm5+Q+bvTVGcJJuRvVLuYba+WojaFaA==}
    engines: {node: '>=0.8'}

  collection-visit@1.0.0:
    resolution: {integrity: sha512-lNkKvzEeMBBjUGHZ+q6z9pSJla0KWAQPvtzhEV9+iGyQYG+pBpl7xKDhxoNSOZH2hhv0v5k0y2yAM4o4SjoSkw==}
    engines: {node: '>=0.10.0'}

  color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  colorette@2.0.20:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  commander@10.0.1:
    resolution: {integrity: sha512-y4Mg2tXshplEbSGzx7amzPwKKOCGuoSRP/CjEdwwk0FOGlUbq6lKuoyDZTNZkmxHdJtp54hdfY/JUrdL7Xfdug==}
    engines: {node: '>=14'}

  commander@11.1.0:
    resolution: {integrity: sha512-yPVavfyCcRhmorC7rWlkHn15b4wDVgVmBA7kV4QVBsF7kv/9TKJAbAXVTxvTnwP8HHKjRCJDClKbciiYS7p0DQ==}
    engines: {node: '>=16'}

  commander@7.2.0:
    resolution: {integrity: sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==}
    engines: {node: '>= 10'}

  commist@1.1.0:
    resolution: {integrity: sha512-rraC8NXWOEjhADbZe9QBNzLAN5Q3fsTPQtBV+fEVj6xKIgDgNiEVE6ZNfHpZOqfQ21YUzfVNUXLOEZquYvQPPg==}

  component-emitter@1.3.1:
    resolution: {integrity: sha512-T0+barUSQRTUQASh8bx02dl+DhF54GtIDY13Y3m9oWTklKbb3Wv974meRpeZ3lp1JpLVECWWNHC4vaG2XHXouQ==}

  compute-scroll-into-view@1.0.20:
    resolution: {integrity: sha512-UCB0ioiyj8CRjtrvaceBLqqhZCVP+1B8+NWQhmdsm0VXOJtobBCf1dBQmebCCo34qZmUwZfIH2MZLqNHazrfjg==}

  compute-scroll-into-view@3.1.0:
    resolution: {integrity: sha512-rj8l8pD4bJ1nx+dAkMhV1xB5RuZEyVysfxJqB1pRchh1KVvwOv9b7CGB8ZfjTImVv2oF+sYMUkMZq6Na5Ftmbg==}

  computeds@0.0.1:
    resolution: {integrity: sha512-7CEBgcMjVmitjYo5q8JTJVra6X5mQ20uTThdK+0kR7UEaDrAWEQcRiBtWJzga4eRpP6afNwwLsX2SET2JhVB1Q==}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  concat-stream@2.0.0:
    resolution: {integrity: sha512-MWufYdFw53ccGjCA+Ol7XJYpAlW6/prSMzuPOTRnJGcGzuhLn4Scrz7qf6o8bROZ514ltazcIFJZevcfbo0x7A==}
    engines: {'0': node >= 6.0}

  config-chain@1.1.13:
    resolution: {integrity: sha512-qj+f8APARXHrM0hraqXYb2/bOVSV4PvJQlNZ/DVj0QrmNM2q2euizkeuVckQ57J+W0mRH6Hvi+k50M4Jul2VRQ==}

  consola@3.2.3:
    resolution: {integrity: sha512-I5qxpzLv+sJhTVEoLYNcTW+bThDCPsit0vLNKShZx6rLtpilNpmmeTPaeqJb9ZE9dV3DGaeby6Vuhrw38WjeyQ==}
    engines: {node: ^14.18.0 || >=16.10.0}

  content-type@1.0.5:
    resolution: {integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==}
    engines: {node: '>= 0.6'}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  copy-descriptor@0.1.1:
    resolution: {integrity: sha512-XgZ0pFcakEUlbwQEVNg3+QAis1FyTL3Qel9FYy8pSkQqoG3PNoT0bOCQtOXcOkur21r2Eq2kI+IE+gsmAEVlYw==}
    engines: {node: '>=0.10.0'}

  copy-to@2.0.1:
    resolution: {integrity: sha512-3DdaFaU/Zf1AnpLiFDeNCD4TOWe3Zl2RZaTzUvWiIk5ERzcCodOE20Vqq4fzCbNoHURFHT4/us/Lfq+S2zyY4w==}

  core-js@2.6.12:
    resolution: {integrity: sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==}
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.

  core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}

  cors@2.8.5:
    resolution: {integrity: sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==}
    engines: {node: '>= 0.10'}

  cos-js-sdk-v5@1.8.0:
    resolution: {integrity: sha512-3m3Xru6rys8FHtzbb8sFMnpNUCQx+kiQRXoo1v7loMHdmHYg2HXes3P0TaAyrCl/TOMnpsWlPAjydQhkG2HsCQ==}

  crc-32@1.2.2:
    resolution: {integrity: sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==}
    engines: {node: '>=0.8'}
    hasBin: true

  crelt@1.0.6:
    resolution: {integrity: sha512-VQ2MBenTq1fWZUH9DJNGti7kKv6EeAuYr3cLwxUWhIu1baTaXh4Ib5W2CqHVqib4/MqbYGJqiL3Zb8GJZr3l4g==}

  cropperjs@1.6.2:
    resolution: {integrity: sha512-nhymn9GdnV3CqiEHJVai54TULFAE3VshJTXSqSJKa8yXAKyBKDWdhHarnlIPrshJ0WMFTGuFvG02YjLXfPiuOA==}

  cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}

  crypto-js@4.2.0:
    resolution: {integrity: sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==}

  css-select@4.3.0:
    resolution: {integrity: sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==}

  css-select@5.1.0:
    resolution: {integrity: sha512-nwoRF1rvRRnnCqqY7updORDsuqKzqYJ28+oSMaJMMgOauh3fvwHqMS7EZpIPqK8GL+g9mKxF1vP/ZjSeNjEVHg==}

  css-tree@1.1.3:
    resolution: {integrity: sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==}
    engines: {node: '>=8.0.0'}

  css-tree@2.2.1:
    resolution: {integrity: sha512-OA0mILzGc1kCOCSJerOeqDxDQ4HOh+G8NbOJFOTgOCzpw7fCBubk0fEyxp8AgOL/jvLgYA/uV0cMbe43ElF1JA==}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0, npm: '>=7.0.0'}

  css-tree@2.3.1:
    resolution: {integrity: sha512-6Fv1DV/TYw//QF5IzQdqsNDjx/wc8TrMBZsqjL9eW01tWb7R7k/mq+/VXfJCl7SoD5emsJop9cOByJZfs8hYIw==}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0}

  css-what@6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==}
    engines: {node: '>= 6'}

  csso@4.2.0:
    resolution: {integrity: sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA==}
    engines: {node: '>=8.0.0'}

  csso@5.0.5:
    resolution: {integrity: sha512-0LrrStPOdJj+SPCCrGhzryycLjwcgUSHBtxNA8aIDxf0GLsRh1cKYhB00Gd1lDOS4yGH69+SNn13+TWbVHETFQ==}
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0, npm: '>=7.0.0'}

  cssstyle@4.0.1:
    resolution: {integrity: sha512-8ZYiJ3A/3OkDd093CBT/0UKDWry7ak4BdPTFP2+QEP7cmhouyq/Up709ASSj2cK02BbZiMgk7kYjZNS4QP5qrQ==}
    engines: {node: '>=18'}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  d@1.0.2:
    resolution: {integrity: sha512-MOqHvMWF9/9MX6nza0KgvFH4HpMU0EF5uUDXqX/BtxtU8NfB0QzRtJ8Oe/6SuS4kbhyzVJwjd97EA4PKrzJ8bw==}
    engines: {node: '>=0.12'}

  data-urls@5.0.0:
    resolution: {integrity: sha512-ZYP5VBHshaDAiVZxjbRVcFJpc+4xGgT0bK3vzy1HLN8jTO975HEbuYzZJcHoQEY5K1a0z8YayJkyVETa08eNTg==}
    engines: {node: '>=18'}

  date-fns-tz@3.2.0:
    resolution: {integrity: sha512-sg8HqoTEulcbbbVXeg84u5UnlsQa8GS5QXMqjjYIhS4abEVVKIUwe0/l/UhrZdKaL/W5eWZNlbTeEIiOXTcsBQ==}
    peerDependencies:
      date-fns: ^3.0.0 || ^4.0.0

  date-fns@4.1.0:
    resolution: {integrity: sha512-Ukq0owbQXxa/U3EGtsdVBkR1w7KOQ5gIBqdH2hkvknzZPYvBxb/aa6E8L7tmjFtkwZBu3UXBbjIgPo/Ez4xaNg==}

  dateformat@2.2.0:
    resolution: {integrity: sha512-GODcnWq3YGoTnygPfi02ygEiRxqUxpJwuRHjdhJYuxpcZmDq4rjBiXYmbCCzStxo176ixfLT6i4NPwQooRySnw==}

  dayjs@1.11.10:
    resolution: {integrity: sha512-vjAczensTgRcqDERK0SR2XMwsF/tSvnvlv6VcF2GIhg6Sx4yOIt/irsr1RDJsKiIyBzJDpCoXiWWq28MqH2cnQ==}

  de-indent@1.0.2:
    resolution: {integrity: sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==}

  debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.5:
    resolution: {integrity: sha512-pt0bNEmneDIvdL1Xsd9oDQ/wrQRkXDT4AUWlNZNPKvW5x/jyO9VFXkJUP07vQ2upmw5PlaITaPKc31jK13V+jg==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decimal.js@10.4.3:
    resolution: {integrity: sha512-VBBaLc1MgL5XpzgIP7ny5Z6Nx3UrRkIViUkPUdtl9aya5amy3De1gsUUSB1g3+3sExYNjCAsAznmukyxCb1GRA==}

  decode-uri-component@0.2.2:
    resolution: {integrity: sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ==}
    engines: {node: '>=0.10'}

  deep-eql@4.1.3:
    resolution: {integrity: sha512-WaEtAOpRA1MQ0eohqZjpGD8zdI0Ovsm8mmFhaDN8dvDZzyoUMcYDnf5Y6iu7HTXxf8JDS23qWa4a+hKCDyOPzw==}
    engines: {node: '>=6'}

  deep-equal@1.1.2:
    resolution: {integrity: sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg==}
    engines: {node: '>= 0.4'}

  deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  default-user-agent@1.0.0:
    resolution: {integrity: sha512-bDF7bg6OSNcSwFWPu4zYKpVkJZQYVrAANMYB8bc9Szem1D0yKdm4sa/rOCs2aC9+2GMqQ7KnwtZRvDhmLF0dXw==}
    engines: {node: '>= 0.10.0'}

  define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}

  define-properties@1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}

  define-property@0.2.5:
    resolution: {integrity: sha512-Rr7ADjQZenceVOAKop6ALkkRAmH1A4Gx9hV/7ZujPUN2rkATqFO0JZLZInbAjpZYoJ1gUx8MRMQVkYemcbMSTA==}
    engines: {node: '>=0.10.0'}

  define-property@1.0.0:
    resolution: {integrity: sha512-cZTYKFWspt9jZsMscWo8sc/5lbPC9Q0N5nBLgb+Yd915iL3udB1uFgS3B8YCx66UVHq018DAVFoee7x+gxggeA==}
    engines: {node: '>=0.10.0'}

  define-property@2.0.2:
    resolution: {integrity: sha512-jwK2UV4cnPpbcG7+VRARKTZPUWowwXA8bzH5NP6ud0oeAxyYPuGZUAC7hMugpCdz4BeSZl2Dl9k66CHJ/46ZYQ==}
    engines: {node: '>=0.10.0'}

  defu@6.1.4:
    resolution: {integrity: sha512-mEQCMmwJu317oSz8CwdIOdwf3xMif1ttiM8LTufzc3g6kR+9Pe236twL8j3IYT1F7GfRgGcW6MWxzZjLIkuHIg==}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  delegate@3.2.0:
    resolution: {integrity: sha512-IofjkYBZaZivn0V8nnsMJGBr4jVLxHDheKSW88PyxS5QC4Vo9ZbZVvhzlSxY87fVq3STR6r+4cGepyHkcWOQSw==}

  destr@2.0.3:
    resolution: {integrity: sha512-2N3BOUU4gYMpTP24s5rF5iP7BDr7uNTCs4ozw3kf/eKfvWSIu93GEBi5m427YoyJoeOzQ5smuu4nNAPGb8idSQ==}

  destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  diff-sequences@29.6.3:
    resolution: {integrity: sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  digest-header@1.1.0:
    resolution: {integrity: sha512-glXVh42vz40yZb9Cq2oMOt70FIoWiv+vxNvdKdU8CwjLad25qHM3trLxhl9bVjdr6WaslIXhWpn0NO8T/67Qjg==}
    engines: {node: '>= 8.0.0'}

  dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}

  doctrine@3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    engines: {node: '>=6.0.0'}

  dom-serializer@0.2.2:
    resolution: {integrity: sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g==}

  dom-serializer@1.4.1:
    resolution: {integrity: sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==}

  dom-serializer@2.0.0:
    resolution: {integrity: sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==}

  dom7@3.0.0:
    resolution: {integrity: sha512-oNlcUdHsC4zb7Msx7JN3K0Nro1dzJ48knvBOnDPKJ2GV9wl1i5vydJZUSyOfrkKFDZEud/jBsTk92S/VGSAe/g==}

  dom7@4.0.6:
    resolution: {integrity: sha512-emjdpPLhpNubapLFdjNL9tP06Sr+GZkrIHEXLWvOGsytACUrkbeIdjO5g77m00BrHTznnlcNqgmn7pCN192TBA==}

  domelementtype@1.3.1:
    resolution: {integrity: sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w==}

  domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}

  domhandler@2.4.2:
    resolution: {integrity: sha512-JiK04h0Ht5u/80fdLMCEmV4zkNh2BcoMFBmZ/91WtYZ8qVXSKjiw7fXMgFPnHcSZgOo3XdinHvmnDUeMf5R4wA==}

  domhandler@4.3.1:
    resolution: {integrity: sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==}
    engines: {node: '>= 4'}

  domhandler@5.0.3:
    resolution: {integrity: sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==}
    engines: {node: '>= 4'}

  domutils@1.7.0:
    resolution: {integrity: sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg==}

  domutils@2.8.0:
    resolution: {integrity: sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==}

  domutils@3.1.0:
    resolution: {integrity: sha512-H78uMmQtI2AhgDJjWeQmHwJJ2bLPD3GMmO7Zja/ZZh84wkm+4ut+IUnUdRa8uCGX88DiVx1j6FRe1XfxEgjEZA==}

  duplexer@0.1.2:
    resolution: {integrity: sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==}

  duplexify@3.7.1:
    resolution: {integrity: sha512-07z8uv2wMyS51kKhD1KsdXJg5WQ6t93RneqRxUHnskXVtlYYkLqM0gqStQZ3pj073g687jPCHrqNfCzawLYh5g==}

  duplexify@4.1.3:
    resolution: {integrity: sha512-M3BmBhwJRZsSx38lZyhE53Csddgzl5R7xGJNk7CVddZD6CcmwMCH8J+7AprIrQKH7TonKxaCjcv27Qmf+sQ+oA==}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}

  editorconfig@1.0.4:
    resolution: {integrity: sha512-L9Qe08KWTlqYMVvMcTIvMAdl1cDUubzRNYL+WfA4bLDMHe4nemKkpmYzkznE1FwLKu0EEmy6obgQKzMJrg4x9Q==}
    engines: {node: '>=14'}
    hasBin: true

  ee-first@1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==}

  electron-to-chromium@1.4.715:
    resolution: {integrity: sha512-XzWNH4ZSa9BwVUQSDorPWAUQ5WGuYz7zJUNpNif40zFCiCl20t8zgylmreNmn26h5kiyw2lg7RfTmeMBsDklqg==}

  element-plus@2.6.2:
    resolution: {integrity: sha512-WFMv1v83l437Xu+GeeM+ytxd9VUQpR4418BowvTVIPYItsoj6yK0ITIuSv19iCesF405FbAOaCIHXhJch0ilFA==}
    peerDependencies:
      vue: ^3.2.0

  emoji-regex@10.3.0:
    resolution: {integrity: sha512-QpLs9D9v9kArv4lfDEgg1X/gN5XLnf/A6l9cs8SPZLRZR3ZkY9+kwIQTxm+fsSej5UMYGE8fdoaZVIBlqG0XTw==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}

  emojis-list@3.0.0:
    resolution: {integrity: sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==}
    engines: {node: '>= 4'}

  end-of-stream@1.4.4:
    resolution: {integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==}

  end-or-error@1.0.1:
    resolution: {integrity: sha512-OclLMSug+k2A0JKuf494im25ANRBVW8qsjmwbgX7lQ8P82H21PQ1PWkoYwb9y5yMBS69BPlwtzdIFClo3+7kOQ==}
    engines: {node: '>= 0.11.14'}

  entities@1.1.2:
    resolution: {integrity: sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w==}

  entities@2.2.0:
    resolution: {integrity: sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==}

  entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}

  es-define-property@1.0.0:
    resolution: {integrity: sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es5-ext@0.10.64:
    resolution: {integrity: sha512-p2snDhiLaXe6dahss1LddxqEm+SkuDvV8dnIQG0MWjyHpcMNfXKPE+/Cc0y+PhxJX3A4xGNeFCj5oc0BUh6deg==}
    engines: {node: '>=0.10'}

  es6-iterator@2.0.3:
    resolution: {integrity: sha512-zw4SRzoUkd+cl+ZoE15A9o1oQd920Bb0iOJMQkQhl3jNc03YqVjAhG7scf9C5KWRU/R13Orf588uCC6525o02g==}

  es6-symbol@3.1.4:
    resolution: {integrity: sha512-U9bFFjX8tFiATgtkJ1zg25+KviIXpgRvRHS8sau3GfhVzThRQrOeksPeT0BWW2MNZs1OEWJ1DPXOQMn0KKRkvg==}
    engines: {node: '>=0.12'}

  esbuild@0.21.5:
    resolution: {integrity: sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==}
    engines: {node: '>=12'}
    hasBin: true

  escalade@3.1.2:
    resolution: {integrity: sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}

  eslint-scope@7.2.2:
    resolution: {integrity: sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint@8.57.0:
    resolution: {integrity: sha512-dZ6+mexnaTIbSBZWgou51U6OmzIhYM2VcNdtiTtI7qPNZm35Akpr0f6vtw3w1Kmn5PYo+tZVfh13WrhpS6oLqQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    deprecated: This version is no longer supported. Please see https://eslint.org/version-support for other options.
    hasBin: true

  esniff@2.0.1:
    resolution: {integrity: sha512-kTUIGKQ/mDPFoJ0oVfcmyJn4iBDRptjNVIzwIFR7tqWXdVI9xfA2RMwY/gbSpJG3lkdWNEjLap/NqVHZiJsdfg==}
    engines: {node: '>=0.10'}

  espree@9.6.1:
    resolution: {integrity: sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  esquery@1.5.0:
    resolution: {integrity: sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}

  estree-walker@3.0.3:
    resolution: {integrity: sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  etag@1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==}
    engines: {node: '>= 0.6'}

  event-emitter@0.3.5:
    resolution: {integrity: sha512-D9rRn9y7kLPnJ+hMq7S/nhvoKwwvVJahBi2BPmx3bvbsEdK3W9ii8cBSGjP+72/LnM4n6fo3+dkCX5FeTQruXA==}

  eventemitter3@2.0.3:
    resolution: {integrity: sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg==}

  eventemitter3@5.0.1:
    resolution: {integrity: sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==}

  execa@5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==}
    engines: {node: '>=10'}

  execa@8.0.1:
    resolution: {integrity: sha512-VyhnebXciFV2DESc+p6B+y0LjSm0krU4OgJN44qFAhBY0TJ+1V61tYD2+wHusZ6F9n5K+vl8k0sTy7PEfV4qpg==}
    engines: {node: '>=16.17'}

  expand-brackets@2.1.4:
    resolution: {integrity: sha512-w/ozOKR9Obk3qoWeY/WDi6MFta9AoMR+zud60mdnbniMcBxRuFJyDt2LdX/14A1UABeqk+Uk+LDfUpvoGKppZA==}
    engines: {node: '>=0.10.0'}

  ext@1.7.0:
    resolution: {integrity: sha512-6hxeJYaL110a9b5TEJSj0gojyHQAmA2ch5Os+ySCiA1QGdS697XWY1pzsrSjqA9LDEEgdB/KypIlR59RcLuHYw==}

  extend-shallow@2.0.1:
    resolution: {integrity: sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==}
    engines: {node: '>=0.10.0'}

  extend-shallow@3.0.2:
    resolution: {integrity: sha512-BwY5b5Ql4+qZoefgMj2NUmx+tehVTH/Kf4k1ZEtOHNFcm2wSxMRo992l6X3TIgni2eZVTZ85xMOjF31fwZAj6Q==}
    engines: {node: '>=0.10.0'}

  extend@3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}

  extglob@2.0.4:
    resolution: {integrity: sha512-Nmb6QXkELsuBr24CJSkilo6UHHgbekK5UiZgfE6UHD3Eb27YC6oD+bhcT+tJ6cl8dmsgdQxnWlcry8ksBIBLpw==}
    engines: {node: '>=0.10.0'}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-diff@1.1.2:
    resolution: {integrity: sha512-KaJUt+M9t1qaIteSvjc6P3RbMdXsNhK61GRftR6SNxqmhthcd9MGIi4T+o0jD8LUSpSnSKXE20nLtJ3fOHxQig==}

  fast-glob@3.3.2:
    resolution: {integrity: sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  fast-xml-parser@4.2.5:
    resolution: {integrity: sha512-B9/wizE4WngqQftFPmdaMYlXoJlJOYxGQOanC77fq9k8+Z0v5dDSVh+3glErdIROP//s/jgb7ZuxKfB8nVyo0g==}
    hasBin: true

  fastq@1.17.1:
    resolution: {integrity: sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==}

  file-entry-cache@6.0.1:
    resolution: {integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==}
    engines: {node: ^10.12.0 || >=12.0.0}

  file-saver@2.0.5:
    resolution: {integrity: sha512-P9bmyZ3h/PRG+Nzga+rbdI4OEpNDzAVyy74uVO9ATgzLK6VtAsYybF/+TOCvrc0MO793d6+42lLyZTw7/ArVzA==}

  fill-range@4.0.0:
    resolution: {integrity: sha512-VcpLTWqWDiTerugjj8e3+esbg+skS3M9e54UuR3iCeIDMXCLTsAH8hTSzDQU/X6/6t3eYkOKoZSef2PlU6U1XQ==}
    engines: {node: '>=0.10.0'}

  fill-range@7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}

  flat-cache@3.2.0:
    resolution: {integrity: sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==}
    engines: {node: ^10.12.0 || >=12.0.0}

  flatted@3.3.1:
    resolution: {integrity: sha512-X8cqMLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw==}

  follow-redirects@1.15.6:
    resolution: {integrity: sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  for-in@1.0.2:
    resolution: {integrity: sha512-7EwmXrOjyL+ChxMhmG5lnW9MPt1aIeZEwKhQzoBUdTV0N3zuwWDZYVJatDvZ2OyzPUvdIAZDsCetk3coyMfcnQ==}
    engines: {node: '>=0.10.0'}

  foreground-child@3.1.1:
    resolution: {integrity: sha512-TMKDUnIte6bfb5nWv7V/caI169OHgvwjb7V4WkeUvbQQdjr5rWKqHFiKWb/fcOwB+CzBT+qbWjvj+DVwRskpIg==}
    engines: {node: '>=14'}

  form-data@4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==}
    engines: {node: '>= 6'}

  formstream@1.5.1:
    resolution: {integrity: sha512-q7ORzFqotpwn3Y/GBK2lK7PjtZZwJHz9QE9Phv8zb5IrL9ftGLyi2zjGURON3voK8TaZ+mqJKERYN4lrHYTkUQ==}

  frac@1.1.2:
    resolution: {integrity: sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA==}
    engines: {node: '>=0.8'}

  fragment-cache@0.2.1:
    resolution: {integrity: sha512-GMBAbW9antB8iZRHLoGw0b3HANt57diZYFO/HL1JGIC1MjKrdmhxvrJbupnVvpys0zsz7yBApXdQyfepKly2kA==}
    engines: {node: '>=0.10.0'}

  fs-extra@10.1.0:
    resolution: {integrity: sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==}
    engines: {node: '>=12'}

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-east-asian-width@1.2.0:
    resolution: {integrity: sha512-2nk+7SIVb14QrgXFHcm84tD4bKQz0RxPuMT8Ag5KPOq7J5fEmAg0UbXdTOSHqNuHSU28k55qnceesxXRZGzKWA==}
    engines: {node: '>=18'}

  get-func-name@2.0.2:
    resolution: {integrity: sha512-8vXOvuE167CtIc3OyItco7N/dpRtBbYOsPsXCz7X/PMnlGjYjSGuZJgM1Y7mmew7BKf9BqvLX2tnOVy1BBUsxQ==}

  get-intrinsic@1.2.4:
    resolution: {integrity: sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==}
    engines: {node: '>= 0.4'}

  get-ready@1.0.0:
    resolution: {integrity: sha512-mFXCZPJIlcYcth+N8267+mghfYN9h3EhsDa6JSnbA3Wrhh/XFpuowviFcsDeYZtKspQyWyJqfs4O6P8CHeTwzw==}

  get-stream@6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}

  get-stream@8.0.1:
    resolution: {integrity: sha512-VaUJspBffn/LMCJVoMvSAdmscJyS1auj5Zulnn5UoYcY531UWmdwhRWkcGKnGU93m5HSXP9LP2usOryrBtQowA==}
    engines: {node: '>=16'}

  get-value@2.0.6:
    resolution: {integrity: sha512-Ln0UQDlxH1BapMu3GPtf7CuYNwRZf2gwCuPqbyG6pB8WfmFpzqcy4xtAaAMUhnNqjMKTiCPZG2oMT3YSx8U2NA==}
    engines: {node: '>=0.10.0'}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  glob@10.3.10:
    resolution: {integrity: sha512-fa46+tv1Ak0UPK1TOy/pZrIybNNt4HCv7SDzwyfiOZkvZLEbjsZkJBPtDHVshZjbecAoAGSC20MjLDG/qr679g==}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true

  glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  globals@13.24.0:
    resolution: {integrity: sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==}
    engines: {node: '>=8'}

  globby@11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}

  good-listener@1.2.2:
    resolution: {integrity: sha512-goW1b+d9q/HIwbVYZzZ6SsTr4IgE+WA44A0GmPIQstuOrgsFcT7VEJ48nmr9GaRtNu0XTKacFLGnBPAM6Afouw==}

  gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}

  gzip-size@6.0.0:
    resolution: {integrity: sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q==}
    engines: {node: '>=10'}

  has-ansi@2.0.0:
    resolution: {integrity: sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg==}
    engines: {node: '>=0.10.0'}

  has-flag@1.0.0:
    resolution: {integrity: sha512-DyYHfIYwAJmjAjSSPKANxI8bFY9YtFrgkAfinBojQ8YJTOuOuav64tMUJv584SES4xl74PmuaevIyaLESHdTAA==}
    engines: {node: '>=0.10.0'}

  has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}

  has-proto@1.0.3:
    resolution: {integrity: sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==}
    engines: {node: '>= 0.4'}

  has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}

  has-value@0.3.1:
    resolution: {integrity: sha512-gpG936j8/MzaeID5Yif+577c17TxaDmhuyVgSwtnL/q8UUTySg8Mecb+8Cf1otgLoD7DDH75axp86ER7LFsf3Q==}
    engines: {node: '>=0.10.0'}

  has-value@1.0.0:
    resolution: {integrity: sha512-IBXk4GTsLYdQ7Rvt+GRBrFSVEkmuOUy4re0Xjd9kJSUQpnTrWR4/y9RpfexN9vkAPMFuQoeWKwqzPozRTlasGw==}
    engines: {node: '>=0.10.0'}

  has-values@0.1.4:
    resolution: {integrity: sha512-J8S0cEdWuQbqD9//tlZxiMuMNmxB8PlEwvYwuxsTmR1G5RXUePEX/SJn7aD0GMLieuZYSwNH0cQuJGwnYunXRQ==}
    engines: {node: '>=0.10.0'}

  has-values@1.0.0:
    resolution: {integrity: sha512-ODYZC64uqzmtfGMEAX/FvZiRyWLpAC3vYnNunURUnkGVTS+mI0smVsWaPydRBsE3g+ok7h960jChO8mFcWlHaQ==}
    engines: {node: '>=0.10.0'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true

  help-me@3.0.0:
    resolution: {integrity: sha512-hx73jClhyk910sidBB7ERlnhMlFsJJIBqSVMFDwPN8o2v9nmp5KgLq1Xz1Bf1fCMMZ6mPrX159iG0VLy/fPMtQ==}

  html-encoding-sniffer@4.0.0:
    resolution: {integrity: sha512-Y22oTqIU4uuPgEemfz7NDJz6OeKf12Lsu+QC+s3BVpda64lTiMYCyGwg5ki4vFxkMwQdeZDl2adZoqUgdFuTgQ==}
    engines: {node: '>=18'}

  html-tags@3.3.1:
    resolution: {integrity: sha512-ztqyC3kLto0e9WbNp0aeP+M3kTt+nbaIveGmUxAtZa+8iFgKLUOD4YKM5j+f3QD89bra7UeumolZHKuOXnTmeQ==}
    engines: {node: '>=8'}

  html-void-elements@2.0.1:
    resolution: {integrity: sha512-0quDb7s97CfemeJAnW9wC0hw78MtW7NU3hqtCD75g2vFlDLt36llsYD7uB7SUzojLMP24N5IatXf7ylGXiGG9A==}

  html-void-elements@3.0.0:
    resolution: {integrity: sha512-bEqo66MRXsUGxWHV5IP0PUiAWwoEjba4VCzg0LjFJBpchPaTfyfCKTG6bc5F8ucKec3q5y6qOdGyYTSBEvhCrg==}

  htmlparser2@3.10.1:
    resolution: {integrity: sha512-IgieNijUMbkDovyoKObU1DUhm1iwNYE/fuifEoEHfd1oZKZDaONBSkal7Y01shxsM49R4XaMdGez3WnF9UfiCQ==}

  http-proxy-agent@7.0.2:
    resolution: {integrity: sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==}
    engines: {node: '>= 14'}

  https-proxy-agent@7.0.4:
    resolution: {integrity: sha512-wlwpilI7YdjSkWaQ/7omYBMTliDcmCN8OLihO6I9B86g06lMyAoqgoDpV0XqoaPOKj+0DIdAvnsWfyAAhmimcg==}
    engines: {node: '>= 14'}

  human-signals@2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==}
    engines: {node: '>=10.17.0'}

  human-signals@5.0.0:
    resolution: {integrity: sha512-AXcZb6vzzrFAUE61HnN4mpLqd/cSIwNQjtNWR0euPm6y0iqx3G4gOXaIDdtdDwZmhwe82LA6+zinmW4UBWVePQ==}
    engines: {node: '>=16.17.0'}

  humanize-duration@3.32.1:
    resolution: {integrity: sha512-inh5wue5XdfObhu/IGEMiA1nUXigSGcaKNemcbLRKa7jXYGDZXr3LoT9pTIzq2hPEbld7w/qv9h+ikWGz8fL1g==}

  humanize-ms@1.2.1:
    resolution: {integrity: sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==}

  i18next@20.6.1:
    resolution: {integrity: sha512-yCMYTMEJ9ihCwEQQ3phLo7I/Pwycf8uAx+sRHwwk5U9Aui/IZYgQRyMqXafQOw5QQ7DM1Z+WyEXWIqSuJHhG2A==}

  i18next@23.16.8:
    resolution: {integrity: sha512-06r/TitrM88Mg5FdUXAKL96dJMzgqLE5dv3ryBAra4KCwD9mJ4ndOTS95ZuymIGoE+2hzfdaMak2X11/es7ZWg==}

  iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}

  iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}

  ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  ignore@5.3.1:
    resolution: {integrity: sha512-5Fytz/IraMjqpwfd34ke28PTVMjZjJG2MPn5t7OE4eUCUNf8BAa7b5WUS9/Qvr6mwOQS7Mk6vdsMno5he+T8Xw==}
    engines: {node: '>= 4'}

  image-size@0.5.5:
    resolution: {integrity: sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  immer@9.0.21:
    resolution: {integrity: sha512-bc4NBHqOqSfRW7POMkHd51LvClaeMXpm8dx0e8oE2GORbq5aRK7Bxl4FyzVLdGtLmvLKL7BTDBG5ACQm4HWjTA==}

  immutable@4.3.5:
    resolution: {integrity: sha512-8eabxkth9gZatlwl5TBuJnCsoTADlL6ftEr7A4qgdaTsPyreilDSnUk57SO+jfKcNtxPa22U5KK6DSeAYhpBJw==}

  import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  ini@1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}

  ip@1.1.9:
    resolution: {integrity: sha512-cyRxvOEpNHNtchU3Ln9KC/auJgup87llfQpQ+t5ghoC/UhL16SWzbueiCsdTnWmqAWl7LadfuwhlqmtOaqMHdQ==}

  is-accessor-descriptor@1.0.1:
    resolution: {integrity: sha512-YBUanLI8Yoihw923YeFUS5fs0fF2f5TSFTNiYAAzhhDscDa3lEqYuz1pDOEP5KvX94I9ey3vsqjJcLVFVU+3QA==}
    engines: {node: '>= 0.10'}

  is-arguments@1.1.1:
    resolution: {integrity: sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==}
    engines: {node: '>= 0.4'}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-buffer@1.1.6:
    resolution: {integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==}

  is-class-hotfix@0.0.6:
    resolution: {integrity: sha512-0n+pzCC6ICtVr/WXnN2f03TK/3BfXY7me4cjCAqT8TYXEl0+JBRoqBo94JJHXcyDSLUeWbNX8Fvy5g5RJdAstQ==}

  is-data-descriptor@1.0.1:
    resolution: {integrity: sha512-bc4NlCDiCr28U4aEsQ3Qs2491gVq4V8G7MQyws968ImqjKuYtTJXrl7Vq7jsN7Ly/C3xj5KWFrY7sHNeDkAzXw==}
    engines: {node: '>= 0.4'}

  is-date-object@1.0.5:
    resolution: {integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==}
    engines: {node: '>= 0.4'}

  is-descriptor@0.1.7:
    resolution: {integrity: sha512-C3grZTvObeN1xud4cRWl366OMXZTj0+HGyk4hvfpx4ZHt1Pb60ANSXqCK7pdOTeUQpRzECBSTphqvD7U+l22Eg==}
    engines: {node: '>= 0.4'}

  is-descriptor@1.0.3:
    resolution: {integrity: sha512-JCNNGbwWZEVaSPtS45mdtrneRWJFp07LLmykxeFV5F6oBvNF8vHSfJuJgoT472pSfk+Mf8VnlrspaFBHWM8JAw==}
    engines: {node: '>= 0.4'}

  is-extendable@0.1.1:
    resolution: {integrity: sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw==}
    engines: {node: '>=0.10.0'}

  is-extendable@1.0.1:
    resolution: {integrity: sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==}
    engines: {node: '>=0.10.0'}

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-fullwidth-code-point@4.0.0:
    resolution: {integrity: sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==}
    engines: {node: '>=12'}

  is-fullwidth-code-point@5.0.0:
    resolution: {integrity: sha512-OVa3u9kkBbw7b8Xw5F9P+D/T9X+Z4+JruYVNapTjPYZYUznQ5YfWeFkOj606XYYW8yugTfC8Pj0hYqvi4ryAhA==}
    engines: {node: '>=18'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-hotkey@0.2.0:
    resolution: {integrity: sha512-UknnZK4RakDmTgz4PI1wIph5yxSs/mvChWs9ifnlXsKuXgWmOkY/hAE0H/k2MIqH0RlRye0i1oC07MCRSD28Mw==}

  is-number@3.0.0:
    resolution: {integrity: sha512-4cboCqIpliH+mAvFNegjZQ4kgKc3ZUhQVr3HvWbSh5q3WH2v82ct+T2Y1hdU5Gdtorx/cLifQjqCbL7bpznLTg==}
    engines: {node: '>=0.10.0'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-path-inside@3.0.3:
    resolution: {integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==}
    engines: {node: '>=8'}

  is-plain-obj@1.1.0:
    resolution: {integrity: sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==}
    engines: {node: '>=0.10.0'}

  is-plain-object@2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==}
    engines: {node: '>=0.10.0'}

  is-plain-object@5.0.0:
    resolution: {integrity: sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q==}
    engines: {node: '>=0.10.0'}

  is-potential-custom-element-name@1.0.1:
    resolution: {integrity: sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==}

  is-regex@1.1.4:
    resolution: {integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==}
    engines: {node: '>= 0.4'}

  is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}

  is-stream@3.0.0:
    resolution: {integrity: sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  is-type-of@1.4.0:
    resolution: {integrity: sha512-EddYllaovi5ysMLMEN7yzHEKh8A850cZ7pykrY1aNRQGn/CDjRDE9qEWbIdt7xGEVJmjBXzU/fNnC4ABTm8tEQ==}

  is-url@1.2.4:
    resolution: {integrity: sha512-ITvGim8FhRiYe4IQ5uHSkj7pVaPDrCTkNd3yq3cV7iZAcJdHTUMPMEHcqSOy9xZ9qFenQCvi+2wjH9a1nXqHww==}

  is-windows@1.0.2:
    resolution: {integrity: sha512-eXK1UInq2bPmjyX6e3VHIzMLobc4J94i4AWn+Hpq3OU5KkrRC96OAcR3PRJ/pGu6m8TRnBHP9dkXQVsT/COVIA==}
    engines: {node: '>=0.10.0'}

  isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  isobject@2.1.0:
    resolution: {integrity: sha512-+OUdGJlgjOBZDfxnDjYYG6zp487z0JGNQq3cYQYg5f5hKR+syHMsaztzGeml/4kGG55CSpKSpWTY+jYGgsHLgA==}
    engines: {node: '>=0.10.0'}

  isobject@3.0.1:
    resolution: {integrity: sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==}
    engines: {node: '>=0.10.0'}

  isomorphic-ws@4.0.1:
    resolution: {integrity: sha512-BhBvN2MBpWTaSHdWRb/bwdZJ1WaehQ2L1KngkCkfLUGF0mAWAT1sQUQacEmQ0jXkFw/czDXPNQSL5u2/Krsz1w==}
    peerDependencies:
      ws: '*'

  isstream@0.1.2:
    resolution: {integrity: sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==}

  jackspeak@2.3.6:
    resolution: {integrity: sha512-N3yCS/NegsOBokc8GAdM8UcmfsKiSS8cipheD/nivzr700H+nsMOxJjQnvwOcRYVuFkdH0wGUvW2WbXGmrZGbQ==}
    engines: {node: '>=14'}

  javascript-natural-sort@0.7.1:
    resolution: {integrity: sha512-nO6jcEfZWQXDhOiBtG2KvKyEptz7RVbpGP4vTD2hLBdmNQSsCiicO2Ioinv6UI4y9ukqnBpy+XZ9H6uLNgJTlw==}

  jiti@1.21.0:
    resolution: {integrity: sha512-gFqAIbuKyyso/3G2qhiO2OM6shY6EPP/R0+mkDbyspxKazh8BXDC5FiFsUjlczgdNz/vfra0da2y+aHrusLG/Q==}
    hasBin: true

  jmespath@0.16.0:
    resolution: {integrity: sha512-9FzQjJ7MATs1tSpnco1K6ayiYE3figslrXA72G2HQ/n76RzvYlofyi5QM+iX4YRs/pu3yzxlVQSST23+dMDknw==}
    engines: {node: '>= 0.6.0'}

  js-base64@2.6.4:
    resolution: {integrity: sha512-pZe//GGmwJndub7ZghVHz7vjb2LgC1m8B07Au3eYqeqv9emhESByMXxaEgkUkEqJe87oBbSniGYoQNIBklc7IQ==}

  js-beautify@1.15.1:
    resolution: {integrity: sha512-ESjNzSlt/sWE8sciZH8kBF8BPlwXPwhR6pWKAw8bw4Bwj+iZcnKW6ONWUutJ7eObuBZQpiIb8S7OYspWrKt7rA==}
    engines: {node: '>=14'}
    hasBin: true

  js-cookie@3.0.5:
    resolution: {integrity: sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==}
    engines: {node: '>=14'}

  js-sdsl@4.3.0:
    resolution: {integrity: sha512-mifzlm2+5nZ+lEcLJMoBK0/IH/bDg8XnJfd/Wq6IP+xoCjLZsTOnV2QpxlVbX9bMnkl5PdEjNtBJ9Cj1NjifhQ==}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-tokens@8.0.3:
    resolution: {integrity: sha512-UfJMcSJc+SEXEl9lH/VLHSZbThQyLpw1vLO1Lb+j4RWDvG3N2f7yj3PVQA3cmkTBNldJ9eFnM+xEXxHIXrYiJw==}

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsdom@24.0.0:
    resolution: {integrity: sha512-UDS2NayCvmXSXVP6mpTj+73JnNQadZlr9N68189xib2tx5Mls7swlTNao26IoHv46BZJFvXygyRtyXd1feAk1A==}
    engines: {node: '>=18'}
    peerDependencies:
      canvas: ^2.11.2
    peerDependenciesMeta:
      canvas:
        optional: true

  jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-source-map@0.6.1:
    resolution: {integrity: sha512-1QoztHPsMQqhDq0hlXY5ZqcEdUzxQEIxgFkKl4WUp2pgShObl+9ovi4kRh2TfvAfxAoHOJ9vIMEqk3k4iex7tg==}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  json5@1.0.2:
    resolution: {integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==}
    hasBin: true

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsonc-parser@3.2.1:
    resolution: {integrity: sha512-AilxAyFOAcK5wA1+LeaySVBrHsGQvUFCDWXKpZjzaL0PqW+xfBOttn8GNtWKFWqneyMZj41MWF9Kl6iPWLwgOA==}

  jsoneditor@9.10.5:
    resolution: {integrity: sha512-fVZ0NMt+zm4rqTKBv2x7zPdLeaRyKo1EjJkaR1QjK4gEM1rMwICILYSW1OPxSc1qqyAoDaA/eeNrluKoxOocCA==}

  jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}

  jsonrepair@3.1.0:
    resolution: {integrity: sha512-idqReg23J0PVRAADmZMc5xQM3xeOX5bTB6OTyMnzq33IXJXmn9iJuWIEvGmrN80rQf4d7uLTMEDwpzujNcI0Rg==}
    hasBin: true

  jstoxml@2.2.9:
    resolution: {integrity: sha512-OYWlK0j+roh+eyaMROlNbS5cd5R25Y+IUpdl7cNdB8HNrkgwQzIS7L9MegxOiWNBj9dQhA/yAxiMwCC5mwNoBw==}

  keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}

  kind-of@3.2.2:
    resolution: {integrity: sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==}
    engines: {node: '>=0.10.0'}

  kind-of@4.0.0:
    resolution: {integrity: sha512-24XsCxmEbRwEDbz/qz3stgin8TTzZ1ESR56OMCN0ujYg+vRutNSiOj9bHH9u85DKgXguraugV5sFuvbD4FW/hw==}
    engines: {node: '>=0.10.0'}

  kind-of@5.1.0:
    resolution: {integrity: sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw==}
    engines: {node: '>=0.10.0'}

  kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}

  kolorist@1.8.0:
    resolution: {integrity: sha512-Y+60/zizpJ3HRH8DCss+q95yr6145JXZo46OTpFvDZWLfRCE4qChOyk1b26nMaNpfHHgxagk9dXT5OP0Tfe+dQ==}

  leven@2.1.0:
    resolution: {integrity: sha512-nvVPLpIHUxCUoRLrFqTgSxXJ614d8AgQoWl7zPe/2VadE8+1dpU3LBhowRuBAcuwruWtOdD8oYC9jDNJjXDPyA==}
    engines: {node: '>=0.10.0'}

  levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}

  lilconfig@3.0.0:
    resolution: {integrity: sha512-K2U4W2Ff5ibV7j7ydLr+zLAkIg5JJ4lPn1Ltsdt+Tz/IjQ8buJ55pZAxoP34lqIiwtF9iAvtLv3JGv7CAyAg+g==}
    engines: {node: '>=14'}

  linkify-it@5.0.0:
    resolution: {integrity: sha512-5aHCbzQRADcdP+ATqnDuhhJ/MRIqDkZX5pyjFHRRysS8vZ5AbqGEoFIb6pYHPZ+L/OC2Lc+xT8uHVVR5CAK/wQ==}

  lint-staged@15.2.2:
    resolution: {integrity: sha512-TiTt93OPh1OZOsb5B7k96A/ATl2AjIZo+vnzFZ6oHK5FuTk63ByDtxGQpHm+kFETjEWqgkF95M8FRXKR/LEBcw==}
    engines: {node: '>=18.12.0'}
    hasBin: true

  listr2@8.0.1:
    resolution: {integrity: sha512-ovJXBXkKGfq+CwmKTjluEqFi3p4h8xvkxGQQAQan22YCgef4KZ1mKGjzfGh6PL6AW5Csw0QiQPNuQyH+6Xk3hA==}
    engines: {node: '>=18.0.0'}

  loader-utils@1.4.2:
    resolution: {integrity: sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==}
    engines: {node: '>=4.0.0'}

  local-pkg@0.5.0:
    resolution: {integrity: sha512-ok6z3qlYyCDS4ZEU27HaU6x/xZa9Whf8jD4ptH5UZTQYZVYeb9bnZ3ojVhiJNLiXK1Hfc0GNbLXcmZ5plLDDBg==}
    engines: {node: '>=14'}

  locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}

  lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}

  lodash-unified@1.0.3:
    resolution: {integrity: sha512-WK9qSozxXOD7ZJQlpSqOT+om2ZfcT4yO+03FuzAHD0wF6S0l0090LRPDx3vhTTLZ8cFKpBn+IOcVXK6qOcIlfQ==}
    peerDependencies:
      '@types/lodash-es': '*'
      lodash: '*'
      lodash-es: '*'

  lodash.camelcase@4.3.0:
    resolution: {integrity: sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==}

  lodash.clonedeep@4.5.0:
    resolution: {integrity: sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ==}

  lodash.debounce@4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==}

  lodash.foreach@4.5.0:
    resolution: {integrity: sha512-aEXTF4d+m05rVOAUG3z4vZZ4xVexLKZGF0lIxuHZ1Hplpk/3B6Z1+/ICICYRLm7c41Z2xiejbkCkJoTlypoXhQ==}

  lodash.isequal@4.5.0:
    resolution: {integrity: sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==}

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  lodash.throttle@4.1.1:
    resolution: {integrity: sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ==}

  lodash.toarray@4.4.0:
    resolution: {integrity: sha512-QyffEA3i5dma5q2490+SgCvDN0pXLmRGSyAANuVi0HQ01Pkfr9fuoKQW8wm1wGBnJITs/mS7wQvS6VshUEBFCw==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  log-update@6.0.0:
    resolution: {integrity: sha512-niTvB4gqvtof056rRIrTZvjNYE4rCUzO6X/X+kYjd7WFxXeJ0NwEFnRxX6ehkvv3jTwrXnNdtAak5XYZuIyPFw==}
    engines: {node: '>=18'}

  loupe@2.3.7:
    resolution: {integrity: sha512-zSMINGVYkdpYSOBmLi0D1Uo7JU9nVdQKrHxC8eYlV+9YKK9WePqAlL7lSlorG/U2Fw1w0hTBmaa/jrQ3UbPHtA==}

  lru-cache@10.2.0:
    resolution: {integrity: sha512-2bIM8x+VAf6JT4bKAljS1qUWgMsqZRPGJS6FSahIMPVvctcNhyVp7AJu7quxOW9jwkryBReKZY5tY5JYv2n/7Q==}
    engines: {node: 14 || >=16.14}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}

  magic-string@0.30.8:
    resolution: {integrity: sha512-ISQTe55T2ao7XtlAStud6qwYPZjE4GK1S/BeVPus4jrq6JuOnQ00YKQC581RWhR122W7msZV263KzVeLoqidyQ==}
    engines: {node: '>=12'}

  map-cache@0.2.2:
    resolution: {integrity: sha512-8y/eV9QQZCiyn1SprXSrCmqJN0yNRATe+PO8ztwqrvrbdRLA3eYJF0yaR0YayLWkMbsQSKWS9N2gPcGEc4UsZg==}
    engines: {node: '>=0.10.0'}

  map-visit@1.0.0:
    resolution: {integrity: sha512-4y7uGv8bd2WdM9vpQsiQNo41Ln1NvhvDRuVt0k2JZQ+ezN2uaQes7lZeZ+QQUHOLQAtDaBJ+7wCbi+ab/KFs+w==}
    engines: {node: '>=0.10.0'}

  markdown-it@14.1.0:
    resolution: {integrity: sha512-a54IwgWPaeBCAAsv13YgmALOF1elABB08FxO9i+r4VFk5Vl4pKokRPeX8u5TCgSsPi6ec1otfLjdOpVcgbpshg==}
    hasBin: true

  mdn-data@2.0.14:
    resolution: {integrity: sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==}

  mdn-data@2.0.28:
    resolution: {integrity: sha512-aylIc7Z9y4yzHYAJNuESG3hfhC+0Ibp/MAMiaOZgNv4pmEdFyfZhhhny4MNiAfWdBQ1RQ2mfDWmM1x8SvGyp8g==}

  mdn-data@2.0.30:
    resolution: {integrity: sha512-GaqWWShW4kv/G9IEucWScBx9G1/vsFZZJUO+tD26M8J8z3Kw5RDQjaoZe03YAClgeS/SWPOcb4nkFBTEi5DUEA==}

  mdurl@2.0.0:
    resolution: {integrity: sha512-Lf+9+2r+Tdp5wXDXC4PcIBjTDtq4UKjCPMQhKIuzpJNW0b96kVqSwW0bT7FhRSfmAiFYgP+SCRvdrDozfh0U5w==}

  memoize-one@6.0.0:
    resolution: {integrity: sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==}

  merge-descriptors@1.0.3:
    resolution: {integrity: sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==}

  merge-options@1.0.1:
    resolution: {integrity: sha512-iuPV41VWKWBIOpBsjoxjDZw8/GbSfZ2mk7N1453bwMrfzdrIk7EzBd+8UVR6rkw67th7xnk9Dytl3J+lHPdxvg==}
    engines: {node: '>=4'}

  merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  micromatch@3.1.0:
    resolution: {integrity: sha512-3StSelAE+hnRvMs8IdVW7Uhk8CVed5tp+kLLGlBP6WiRAXS21GPGu/Nat4WNPXj2Eoc24B02SaeoyozPMfj0/g==}
    engines: {node: '>=0.10.0'}

  micromatch@4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-match@1.0.2:
    resolution: {integrity: sha512-VXp/ugGDVh3eCLOBCiHZMYWQaTNUHv2IJrut+yXA6+JbLPXHglHwfS/5A5L0ll+jkCY7fIzRJcH6OIunF+c6Cg==}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@2.6.0:
    resolution: {integrity: sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==}
    engines: {node: '>=4.0.0'}
    hasBin: true

  mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}

  mimic-fn@4.0.0:
    resolution: {integrity: sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==}
    engines: {node: '>=12'}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@9.0.1:
    resolution: {integrity: sha512-0jWhJpD/MdhPXwPuiRkCbfYfSKp2qnn2eOc279qI7f+osl/l+prKSrvhg157zSYvx/1nmgn2NqdT6k2Z7zSH9w==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimatch@9.0.3:
    resolution: {integrity: sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  minipass@7.0.4:
    resolution: {integrity: sha512-jYofLM5Dam9279rdkWzqHozUo4ybjdZmCsDHePy5V/PbBcVMiSZR97gmAy45aqi8CK1lG2ECd356FU86avfwUQ==}
    engines: {node: '>=16 || 14 >=14.17'}

  mitt@3.0.1:
    resolution: {integrity: sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw==}

  mixin-deep@1.3.2:
    resolution: {integrity: sha512-WRoDn//mXBiJ1H40rqa3vH0toePwSsGb45iInWlTySa+Uu4k3tYUSxa2v1KqAiLtvlrSzaExqS1gtk96A9zvEA==}
    engines: {node: '>=0.10.0'}

  mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true

  mlly@1.6.1:
    resolution: {integrity: sha512-vLgaHvaeunuOXHSmEbZ9izxPx3USsk8KCQ8iC+aTlp5sKRSoZvwhHh5L9VbKSaVC6sJDqbyohIS76E2VmHIPAA==}

  mobius1-selectr@2.4.13:
    resolution: {integrity: sha512-Mk9qDrvU44UUL0EBhbAA1phfQZ7aMZPjwtL7wkpiBzGh8dETGqfsh50mWoX9EkjDlkONlErWXArHCKfoxVg0Bw==}

  mqtt-packet@6.10.0:
    resolution: {integrity: sha512-ja8+mFKIHdB1Tpl6vac+sktqy3gA8t9Mduom1BA75cI+R9AHnZOiaBQwpGiWnaVJLDGRdNhQmFaAqd7tkKSMGA==}

  mqtt@4.3.8:
    resolution: {integrity: sha512-2xT75uYa0kiPEF/PE0VPdavmEkoBzMT/UL9moid0rAvlCtV48qBwxD62m7Ld/4j8tSkIO1E/iqRl/S72SEOhOw==}
    engines: {node: '>=10.0.0'}
    hasBin: true

  mrmime@2.0.0:
    resolution: {integrity: sha512-eu38+hdgojoyq63s+yTpN4XMBdt5l8HhMhc4VKLO9KM5caLIBvUm4thi7fFaxyTmCKeNnXZ5pAlBwCUnhA09uw==}
    engines: {node: '>=10'}

  ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}

  muggle-string@0.4.1:
    resolution: {integrity: sha512-VNTrAak/KhO2i8dqqnqnAHOa3cYBwXEZe9h+D5h/1ZqFSTEFHdM65lR7RoIqq3tBBYavsOXV84NoHXZ0AkPyqQ==}

  mz@2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}

  namespace-emitter@2.0.1:
    resolution: {integrity: sha512-N/sMKHniSDJBjfrkbS/tpkPj4RAbvW3mr8UAzvlMHyun93XEm83IAvhWtJVHo+RHn/oO8Job5YN4b+wRjSVp5g==}

  nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  nanoid@5.0.9:
    resolution: {integrity: sha512-Aooyr6MXU6HpvvWXKoVoXwKMs/KyVakWwg7xQfv5/S/RIgJMy0Ifa45H9qqYy7pTCszrHzP21Uk4PZq2HpEM8Q==}
    engines: {node: ^18 || >=20}
    hasBin: true

  nanomatch@1.2.13:
    resolution: {integrity: sha512-fpoe2T0RbHwBTBUOftAfBPaDEi06ufaUai0mE6Yn1kacc3SnTErfb/h+X94VXzI64rKFHYImXSvdwGGCmwOqCA==}
    engines: {node: '>=0.10.0'}

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  next-tick@1.1.0:
    resolution: {integrity: sha512-CXdUiJembsNjuToQvxayPZF9Vqht7hewsvy2sOWafLvi2awflj9mOC6bHIg50orX8IJvWKY9wYQ/zB2kogPslQ==}

  node-fetch-native@1.6.4:
    resolution: {integrity: sha512-IhOigYzAKHd244OC0JIMIUrjzctirCmPkaIfhDeGcEETWof5zKYUW7e7MYvChGWh/4CJeXEgsRyGzuF334rOOQ==}

  node-hex@1.0.1:
    resolution: {integrity: sha512-iwpZdvW6Umz12ICmu9IYPRxg0tOLGmU3Tq2tKetejCj3oZd7b2nUXwP3a7QA5M9glWy8wlPS1G3RwM/CdsUbdQ==}
    engines: {node: '>=8.0.0'}

  node-releases@2.0.14:
    resolution: {integrity: sha512-y10wOWt8yZpqXmOgRo77WaHEmhYQYGNA6y421PKsKYWEK8aW+cqAphborZDhqfyKrbZEN92CN1X2KbafY2s7Yw==}

  nopt@7.2.0:
    resolution: {integrity: sha512-CVDtwCdhYIvnAzFoJ6NJ6dX3oga9/HyciQDnG1vQDjSLMeKLJ4A93ZqYKDrgYSr1FBY5/hMYC+2VCi24pgpkGA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}
    hasBin: true

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  normalize-wheel-es@1.2.0:
    resolution: {integrity: sha512-Wj7+EJQ8mSuXr2iWfnujrimU35R2W4FAErEyTmJoJ7ucwTn2hOUSsRehMb5RSYkxXGTM7Y9QpvPmp++w5ftoJw==}

  normalize.css@8.0.1:
    resolution: {integrity: sha512-qizSNPO93t1YUuUhP22btGOo3chcvDFqFaj2TRybP0DMxkHOCTYwp3n34fel4a31ORXy4m1Xq0Gyqpb5m33qIg==}

  npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}

  npm-run-path@5.3.0:
    resolution: {integrity: sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  nprogress@0.2.0:
    resolution: {integrity: sha512-I19aIingLgR1fmhftnbWWO3dXc0hSxqHQHQb3H8m+K3TnEn/iSeTZZOyvKXWqQESMwuUVnatlCnZdLBZZt2VSA==}

  nth-check@2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}

  number-allocator@1.0.14:
    resolution: {integrity: sha512-OrL44UTVAvkKdOdRQZIJpLkAdjXGTRda052sN4sO77bKEzYYqWKMBjQvrJFzqygI99gL6Z4u2xctPW1tB8ErvA==}

  nwsapi@2.2.7:
    resolution: {integrity: sha512-ub5E4+FBPKwAZx0UwIQOjYWGHTEq5sPqHQNRN8Z9e4A7u3Tj1weLJsL59yH9vmvqEtBHaOmT6cYQKIZOxp35FQ==}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  object-copy@0.1.0:
    resolution: {integrity: sha512-79LYn6VAb63zgtmAteVOWo9Vdj71ZVBy3Pbse+VqxDpEP83XuujMrGqHIwAXJ5I/aM0zU7dIyIAhifVTPrNItQ==}
    engines: {node: '>=0.10.0'}

  object-inspect@1.13.1:
    resolution: {integrity: sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ==}

  object-is@1.1.6:
    resolution: {integrity: sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==}
    engines: {node: '>= 0.4'}

  object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  object-visit@1.0.1:
    resolution: {integrity: sha512-GBaMwwAVK9qbQN3Scdo0OyvgPW7l3lnaVMj84uTOZlswkX0KpF6fyDBJhtTthf7pymztoN36/KEr1DyhF96zEA==}
    engines: {node: '>=0.10.0'}

  object.pick@1.3.0:
    resolution: {integrity: sha512-tqa/UMy/CCoYmj+H5qc07qvSL9dqcs/WZENZ1JbtWBlATP+iVOe778gE6MSijnyCnORzDuX6hU+LA4SZ09YjFQ==}
    engines: {node: '>=0.10.0'}

  ofetch@1.3.4:
    resolution: {integrity: sha512-KLIET85ik3vhEfS+3fDlc/BAZiAp+43QEC/yCo5zkNoY2YaKvNkOaFr/6wCFgFH1kuYQM5pMNi0Tg8koiIemtw==}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}

  onetime@6.0.0:
    resolution: {integrity: sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==}
    engines: {node: '>=12'}

  optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}

  orderedmap@2.1.1:
    resolution: {integrity: sha512-TvAWxi0nDe1j/rtMcWcIj94+Ffe6n7zhow33h40SKxmsmozs6dz/e+EajymfoFcHd7sxNn8yHM8839uixMOV6g==}

  os-name@1.0.3:
    resolution: {integrity: sha512-f5estLO2KN8vgtTRaILIgEGBoBrMnZ3JQ7W9TMZCnOIGwHe8TRGSpcagnWDo+Dfhd/z08k9Xe75hvciJJ8Qaew==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  osx-release@1.1.0:
    resolution: {integrity: sha512-ixCMMwnVxyHFQLQnINhmIpWqXIfS2YOXchwQrk+OFzmo6nDjQ0E4KXAyyUh0T0MZgV4bUhkRrAbVqlE4yLVq4A==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}

  p-limit@5.0.0:
    resolution: {integrity: sha512-/Eaoq+QyLSiXQ4lyYV23f14mZRQcXnxfHrN0vCai+ak9G0pp9iEQukIIZq5NccEvwRB8PUnZT0KsOoDCINS1qQ==}
    engines: {node: '>=18'}

  p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}

  parchment@1.1.4:
    resolution: {integrity: sha512-J5FBQt/pM2inLzg4hEWmzQx/8h8D0CiDxaG3vyp9rKrQRSDgBlhjdP5jQGgosEajXPSQouXGHOmVdgo7QmJuOg==}

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parse5@7.1.2:
    resolution: {integrity: sha512-Czj1WaSVpaoj0wbhMzLmWD69anp2WH7FXMB9n1Sy8/ZFF9jolSQVMu1Ij5WIyGmcBmhk7EOndpO4mIpihVqAXw==}

  pascalcase@0.1.1:
    resolution: {integrity: sha512-XHXfu/yOQRy9vYOtUDVMN60OEJjW013GoObG1o+xwQTpB9eYJX/BjXMsdW13ZDPruFhYYn0AG22w0xgQMwl3Nw==}
    engines: {node: '>=0.10.0'}

  path-browserify@1.0.1:
    resolution: {integrity: sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-key@4.0.0:
    resolution: {integrity: sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==}
    engines: {node: '>=12'}

  path-scurry@1.10.1:
    resolution: {integrity: sha512-MkhCqzzBEpPvxxQ71Md0b1Kk51W01lrYvlMzSUaIzNsODdd7mqhiimSZlr+VegAz5Z6Vzt9Xg2ttE//XBhH3EQ==}
    engines: {node: '>=16 || 14 >=14.17'}

  path-to-regexp@6.2.1:
    resolution: {integrity: sha512-JLyh7xT1kizaEvcaXOQwOc2/Yhw6KZOvPf1S8401UyLk86CU79LN3vl7ztXGm/pZ+YjoyAJ4rxmHwbkBXJX+yw==}

  path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  pathe@0.2.0:
    resolution: {integrity: sha512-sTitTPYnn23esFR3RlqYBWn4c45WGeLcsKzQiUpXJAyfcWkolvlYpV8FLo7JishK946oQwMFUCHXQ9AjGPKExw==}

  pathe@1.1.2:
    resolution: {integrity: sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==}

  pathval@1.1.1:
    resolution: {integrity: sha512-Dp6zGqpTdETdR63lehJYPeIOqpiNBNtc7BpWSLrOje7UaIsE5aY92r/AunQA7rsXvet3lrJ3JnZX29UPTKXyKQ==}

  pause-stream@0.0.11:
    resolution: {integrity: sha512-e3FBlXLmN/D1S+zHzanP4E/4Z60oFAa3O051qt1pxa7DEJWKAyil6upYVXCWadEnuoqa4Pkc9oUx9zsxYeRv8A==}

  perfect-debounce@1.0.0:
    resolution: {integrity: sha512-xCy9V055GLEqoFaHoC1SoLIaLmWctgCUaBaWxDZ7/Zx4CTyX7cJQLJOok/orfjZAh9kEYpjJa4d0KcJmCbctZA==}

  picocolors@1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==}

  picocolors@1.0.1:
    resolution: {integrity: sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  picomodal@3.0.0:
    resolution: {integrity: sha512-FoR3TDfuLlqUvcEeK5ifpKSVVns6B4BQvc8SDF6THVMuadya6LLtji0QgUDSStw0ZR2J7I6UGi5V2V23rnPWTw==}

  pidtree@0.6.0:
    resolution: {integrity: sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==}
    engines: {node: '>=0.10'}
    hasBin: true

  pinia@2.1.7:
    resolution: {integrity: sha512-+C2AHFtcFqjPih0zpYuvof37SFxMQ7OEG2zV9jRI12i9BOy3YQVAHwdKtyyc8pDcDyIc33WCIsZaCFWU7WWxGQ==}
    peerDependencies:
      '@vue/composition-api': ^1.4.0
      typescript: '>=4.4.4'
      vue: ^2.6.14 || ^3.3.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
      typescript:
        optional: true

  pinyin-pro@3.26.0:
    resolution: {integrity: sha512-HcBZZb0pvm0/JkPhZHWA5Hqp2cWHXrrW/WrV+OtaYYM+kf35ffvZppIUuGmyuQ7gDr1JDJKMkbEE+GN0wfMoGg==}

  pkg-types@1.0.3:
    resolution: {integrity: sha512-nN7pYi0AQqJnoLPC9eHFQ8AcyaixBUOwvqc5TDnIKCMEE6I0y8P7OKA7fPexsXGCGxQDl/cmrLAp26LhcwxZ4A==}

  platform@1.3.6:
    resolution: {integrity: sha512-fnWVljUchTro6RiCFvCXBbNhJc2NijN7oIQxbwsyL0buWJPG85v81ehlHI9fXrJsMNgTofEoWIQeClKpgxFLrg==}

  posix-character-classes@0.1.1:
    resolution: {integrity: sha512-xTgYBc3fuo7Yt7JbiuFxSYGToMoz8fLoE6TC9Wx1P/u+LfeThMOAqmuyECnlBaaJb+u1m9hHiXUEtwW4OzfUJg==}
    engines: {node: '>=0.10.0'}

  postcss-prefix-selector@1.16.0:
    resolution: {integrity: sha512-rdVMIi7Q4B0XbXqNUEI+Z4E+pueiu/CS5E6vRCQommzdQ/sgsS4dK42U7GX8oJR+TJOtT+Qv3GkNo6iijUMp3Q==}
    peerDependencies:
      postcss: '>4 <9'

  postcss@5.2.18:
    resolution: {integrity: sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==}
    engines: {node: '>=0.12'}

  postcss@8.4.38:
    resolution: {integrity: sha512-Wglpdk03BSfXkHoQa3b/oulrotAkwrlLDRSOb9D0bN86FdRyE9lppSp33aHNPgBa0JKCoB+drFLZkQoRRYae5A==}
    engines: {node: ^10 || ^12 || >=14}

  postcss@8.4.39:
    resolution: {integrity: sha512-0vzE+lAiG7hZl1/9I8yzKLx3aR9Xbof3fBHKunvMfOCYAtMhrsnccJY2iTURb9EZd5+pLuiNV9/c/GZJOHsgIw==}
    engines: {node: ^10 || ^12 || >=14}

  posthtml-parser@0.2.1:
    resolution: {integrity: sha512-nPC53YMqJnc/+1x4fRYFfm81KV2V+G9NZY+hTohpYg64Ay7NemWWcV4UWuy/SgMupqQ3kJ88M/iRfZmSnxT+pw==}

  posthtml-rename-id@1.0.12:
    resolution: {integrity: sha512-UKXf9OF/no8WZo9edRzvuMenb6AD5hDLzIepJW+a4oJT+T/Lx7vfMYWT4aWlGNQh0WMhnUx1ipN9OkZ9q+ddEw==}

  posthtml-render@1.4.0:
    resolution: {integrity: sha512-W1779iVHGfq0Fvh2PROhCe2QhB8mEErgqzo1wpIt36tCgChafP+hbXIhLDOM8ePJrZcFs0vkNEtdibEWVqChqw==}
    engines: {node: '>=10'}

  posthtml-svg-mode@1.0.3:
    resolution: {integrity: sha512-hEqw9NHZ9YgJ2/0G7CECOeuLQKZi8HjWLkBaSVtOWjygQ9ZD8P7tqeowYs7WrFdKsWEKG7o+IlsPY8jrr0CJpQ==}

  posthtml@0.9.2:
    resolution: {integrity: sha512-spBB5sgC4cv2YcW03f/IAUN1pgDJWNWD8FzkyY4mArLUMJW+KlQhlmUdKAHQuPfb00Jl5xIfImeOsf6YL8QK7Q==}
    engines: {node: '>=0.10.0'}

  preact@10.22.0:
    resolution: {integrity: sha512-RRurnSjJPj4rp5K6XoP45Ui33ncb7e4H7WiOHVpjbkvqvA3U+N8Z6Qbo0AE6leGYBV66n8EhEaFixvIu3SkxFw==}

  prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}

  prettier@3.2.5:
    resolution: {integrity: sha512-3/GWa9aOC0YeD7LUfvOG2NiDyhOWRvt1k+rcKhOuYnMY24iiCphgneUfJDyFXd6rZCAnuLBv6UeAULtrhT/F4A==}
    engines: {node: '>=14'}
    hasBin: true

  pretty-format@29.7.0:
    resolution: {integrity: sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  prismjs@1.29.0:
    resolution: {integrity: sha512-Kx/1w86q/epKcmte75LNrEoT+lX8pBpavuAbvJWRXar7Hz8jrtF+e3vY751p0R8H9HdArwaCTNDDzHg/ScJK1Q==}
    engines: {node: '>=6'}

  process-nextick-args@2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}

  process@0.11.10:
    resolution: {integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==}
    engines: {node: '>= 0.6.0'}

  prosemirror-changeset@2.2.1:
    resolution: {integrity: sha512-J7msc6wbxB4ekDFj+n9gTW/jav/p53kdlivvuppHsrZXCaQdVgRghoZbSS3kwrRyAstRVQ4/+u5k7YfLgkkQvQ==}

  prosemirror-collab@1.3.1:
    resolution: {integrity: sha512-4SnynYR9TTYaQVXd/ieUvsVV4PDMBzrq2xPUWutHivDuOshZXqQ5rGbZM84HEaXKbLdItse7weMGOUdDVcLKEQ==}

  prosemirror-commands@1.7.0:
    resolution: {integrity: sha512-6toodS4R/Aah5pdsrIwnTYPEjW70SlO5a66oo5Kk+CIrgJz3ukOoS+FYDGqvQlAX5PxoGWDX1oD++tn5X3pyRA==}

  prosemirror-dropcursor@1.8.1:
    resolution: {integrity: sha512-M30WJdJZLyXHi3N8vxN6Zh5O8ZBbQCz0gURTfPmTIBNQ5pxrdU7A58QkNqfa98YEjSAL1HUyyU34f6Pm5xBSGw==}

  prosemirror-gapcursor@1.3.2:
    resolution: {integrity: sha512-wtjswVBd2vaQRrnYZaBCbyDqr232Ed4p2QPtRIUK5FuqHYKGWkEwl08oQM4Tw7DOR0FsasARV5uJFvMZWxdNxQ==}

  prosemirror-history@1.4.1:
    resolution: {integrity: sha512-2JZD8z2JviJrboD9cPuX/Sv/1ChFng+xh2tChQ2X4bB2HeK+rra/bmJ3xGntCcjhOqIzSDG6Id7e8RJ9QPXLEQ==}

  prosemirror-inputrules@1.4.0:
    resolution: {integrity: sha512-6ygpPRuTJ2lcOXs9JkefieMst63wVJBgHZGl5QOytN7oSZs3Co/BYbc3Yx9zm9H37Bxw8kVzCnDsihsVsL4yEg==}

  prosemirror-keymap@1.2.2:
    resolution: {integrity: sha512-EAlXoksqC6Vbocqc0GtzCruZEzYgrn+iiGnNjsJsH4mrnIGex4qbLdWWNza3AW5W36ZRrlBID0eM6bdKH4OStQ==}

  prosemirror-markdown@1.13.1:
    resolution: {integrity: sha512-Sl+oMfMtAjWtlcZoj/5L/Q39MpEnVZ840Xo330WJWUvgyhNmLBLN7MsHn07s53nG/KImevWHSE6fEj4q/GihHw==}

  prosemirror-menu@1.2.4:
    resolution: {integrity: sha512-S/bXlc0ODQup6aiBbWVsX/eM+xJgCTAfMq/nLqaO5ID/am4wS0tTCIkzwytmao7ypEtjj39i7YbJjAgO20mIqA==}

  prosemirror-model@1.24.1:
    resolution: {integrity: sha512-YM053N+vTThzlWJ/AtPtF1j0ebO36nvbmDy4U7qA2XQB8JVaQp1FmB9Jhrps8s+z+uxhhVTny4m20ptUvhk0Mg==}

  prosemirror-schema-basic@1.2.3:
    resolution: {integrity: sha512-h+H0OQwZVqMon1PNn0AG9cTfx513zgIG2DY00eJ00Yvgb3UD+GQ/VlWW5rcaxacpCGT1Yx8nuhwXk4+QbXUfJA==}

  prosemirror-schema-list@1.5.0:
    resolution: {integrity: sha512-gg1tAfH1sqpECdhIHOA/aLg2VH3ROKBWQ4m8Qp9mBKrOxQRW61zc+gMCI8nh22gnBzd1t2u1/NPLmO3nAa3ssg==}

  prosemirror-state@1.4.3:
    resolution: {integrity: sha512-goFKORVbvPuAQaXhpbemJFRKJ2aixr+AZMGiquiqKxaucC6hlpHNZHWgz5R7dS4roHiwq9vDctE//CZ++o0W1Q==}

  prosemirror-tables@1.6.4:
    resolution: {integrity: sha512-TkDY3Gw52gRFRfRn2f4wJv5WOgAOXLJA2CQJYIJ5+kdFbfj3acR4JUW6LX2e1hiEBiUwvEhzH5a3cZ5YSztpIA==}

  prosemirror-trailing-node@3.0.0:
    resolution: {integrity: sha512-xiun5/3q0w5eRnGYfNlW1uU9W6x5MoFKWwq/0TIRgt09lv7Hcser2QYV8t4muXbEr+Fwo0geYn79Xs4GKywrRQ==}
    peerDependencies:
      prosemirror-model: ^1.22.1
      prosemirror-state: ^1.4.2
      prosemirror-view: ^1.33.8

  prosemirror-transform@1.10.2:
    resolution: {integrity: sha512-2iUq0wv2iRoJO/zj5mv8uDUriOHWzXRnOTVgCzSXnktS/2iQRa3UUQwVlkBlYZFtygw6Nh1+X4mGqoYBINn5KQ==}

  prosemirror-view@1.38.0:
    resolution: {integrity: sha512-O45kxXQTaP9wPdXhp8TKqCR+/unS/gnfg9Q93svQcB3j0mlp2XSPAmsPefxHADwzC+fbNS404jqRxm3UQaGvgw==}

  proto-list@1.2.4:
    resolution: {integrity: sha512-vtK/94akxsTMhe0/cbfpR+syPuszcuwhqVjJq26CuNDgFGj682oRBXOP5MJpv2r7JtE8MsiepGIqvvOTBwn2vA==}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  psl@1.9.0:
    resolution: {integrity: sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==}

  pump@3.0.0:
    resolution: {integrity: sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==}

  punycode.js@2.3.1:
    resolution: {integrity: sha512-uxFIHU0YlHYhDQtV4R9J6a52SLx28BCjT+4ieh7IGbgwVJWO+km431c4yRlREUAsAmt/uMjQUyQHNEPf0M39CA==}
    engines: {node: '>=6'}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  qiniu-js@3.4.1:
    resolution: {integrity: sha512-8vxrLqDPlJUk3fUAaTozh3TAT3ww9B5KqGogmGuTiFHnewXDoMxTCSY5z8Ab5UNdrCo6ZxDM07G/o++CICRUFw==}

  qs@6.12.1:
    resolution: {integrity: sha512-zWmv4RSuB9r2mYQw3zxQuHWeU+42aKi1wWig/j4ele4ygELZ7PEO6MM7rim9oAQH2A5MWfsAVf/jPvTPgCbvUQ==}
    engines: {node: '>=0.6'}

  query-string@4.3.4:
    resolution: {integrity: sha512-O2XLNDBIg1DnTOa+2XrIwSiXEV8h2KImXUnjhhn2+UsvZ+Es2uyd5CCRTNQlDGbzUQOW3aYCBx9rVA6dzsiY7Q==}
    engines: {node: '>=0.10.0'}

  querystring@0.2.1:
    resolution: {integrity: sha512-wkvS7mL/JMugcup3/rMitHmd9ecIGd2lhFhK9N3UUQ450h66d1r3Y9nvXzQAW1Lq+wyx61k/1pfKS5KuKiyEbg==}
    engines: {node: '>=0.4.x'}
    deprecated: The querystring API is considered Legacy. new code should use the URLSearchParams API instead.

  querystringify@2.2.0:
    resolution: {integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==}

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  quill-delta@3.6.3:
    resolution: {integrity: sha512-wdIGBlcX13tCHOXGMVnnTVFtGRLoP0imqxM696fIPwIf5ODIYUHIvHbZcyvGlZFiFhK5XzDC2lpjbxRhnM05Tg==}
    engines: {node: '>=0.10'}

  quill@1.3.7:
    resolution: {integrity: sha512-hG/DVzh/TiknWtE6QmWAF/pxoZKYxfe3J/d/+ShUWkDvvkZQVTPeVmUJVu1uE6DDooC4fWTiCLh84ul89oNz5g==}

  react-is@18.2.0:
    resolution: {integrity: sha512-xWGDIW6x921xtzPkhiULtthJHoJvBbF3q26fzloPCK0hsvxtPVelvftw3zjbHWSkR2km9Z+4uxbDDK/6Zw9B8w==}

  readable-stream@2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==}

  readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==}

  regex-not@1.0.2:
    resolution: {integrity: sha512-J6SDjUgDxQj5NusnOtdFxDwN/+HWykR8GELwctJ7mdqhcyy1xEc4SRFHUXvxTp661YaVKAjfRLZ9cCqS6tn32A==}
    engines: {node: '>=0.10.0'}

  regexp.prototype.flags@1.5.2:
    resolution: {integrity: sha512-NcDiDkTLuPR+++OCKB0nWafEmhg/Da8aUPLPMQbK+bxKKCm1/S5he+AqYa4PlMCVBalb4/yxIRub6qkEx5yJbw==}
    engines: {node: '>= 0.4'}

  reinterval@1.1.0:
    resolution: {integrity: sha512-QIRet3SYrGp0HUHO88jVskiG6seqUGC5iAG7AwI/BV4ypGcuqk9Du6YQBUOUqm9c8pw1eyLoIaONifRua1lsEQ==}

  repeat-element@1.1.4:
    resolution: {integrity: sha512-LFiNfRcSu7KK3evMyYOuCzv3L10TW7yC1G2/+StMjK8Y6Vqd2MG7r/Qjw4ghtuCOjFvlnms/iMmLqpvW/ES/WQ==}
    engines: {node: '>=0.10.0'}

  repeat-string@1.6.1:
    resolution: {integrity: sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w==}
    engines: {node: '>=0.10'}

  requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve-url@0.2.1:
    resolution: {integrity: sha512-ZuF55hVUQaaczgOIwqWzkEcEidmlD/xl44x1UZnhOXcYuFN2S6+rcxpG+C1N3So0wvNI3DmJICUFfu2SxhBmvg==}
    deprecated: https://github.com/lydell/resolve-url#deprecated

  restore-cursor@4.0.0:
    resolution: {integrity: sha512-I9fPXU9geO9bHOt9pHHOhOkYerIMsmVaWB0rA2AI9ERh/+x/i7MV5HKBNrg+ljO5eoPVgCcnFuRjJ9uH6I/3eg==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  ret@0.1.15:
    resolution: {integrity: sha512-TTlYpa+OL+vMMNG24xSlQGEJ3B/RzEfUlLct7b5G/ytav+wPrplCpVMFuwzXbkecJrb6IYo1iFb0S9v37754mg==}
    engines: {node: '>=0.12'}

  reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rfdc@1.3.1:
    resolution: {integrity: sha512-r5a3l5HzYlIC68TpmYKlxWjmOP6wiPJ1vWv2HeLhNsRZMrCkxeqxiHlQ21oXmQ4F3SiryXBHhAD7JZqvOJjFmg==}

  rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rollup@4.13.0:
    resolution: {integrity: sha512-3YegKemjoQnYKmsBlOHfMLVPPA5xLkQ8MHLLSw/fBrFaVkEayL51DilPpNNLq1exr98F2B1TzrV0FUlN3gWRPg==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  rope-sequence@1.3.4:
    resolution: {integrity: sha512-UT5EDe2cu2E/6O4igUr5PSFs23nvvukicWHx6GnOPlHAiiYbzNuCRQCuiUdHJQcqKalLKlrYJnjY0ySGsXNQXQ==}

  rrweb-cssom@0.6.0:
    resolution: {integrity: sha512-APM0Gt1KoXBz0iIkkdB/kfvGOwC4UuJFeG/c+yV7wSc7q96cG/kJ0HiYCnzivD9SB53cLV1MlHFNfOuPaadYSw==}

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safe-regex@1.1.0:
    resolution: {integrity: sha512-aJXcif4xnaNUzvUuC5gcb46oTS7zvg4jpMTnuqtrEPlR3vFr4pxtdTwaF1Qs3Enjn9HK+ZlwQui+a7z0SywIzg==}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  sass@1.72.0:
    resolution: {integrity: sha512-Gpczt3WA56Ly0Mn8Sl21Vj94s1axi9hDIzDFn9Ph9x3C3p4nNyvsqJoQyVXKou6cBlfFWEgRW4rT8Tb4i3XnVA==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  sax@1.4.1:
    resolution: {integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==}

  saxes@6.0.0:
    resolution: {integrity: sha512-xAg7SOnEhrm5zI3puOOKyy1OMcMlIJZYNJY7xLBwSze0UjhPLnWfj2GF2EpT0jmzaJKIWKHLsaSSajf35bcYnA==}
    engines: {node: '>=v12.22.7'}

  screenfull@6.0.2:
    resolution: {integrity: sha512-AQdy8s4WhNvUZ6P8F6PB21tSPIYKniic+Ogx0AacBMjKP1GUHN2E9URxQHtCusiwxudnCKkdy4GrHXPPJSkCCw==}
    engines: {node: ^14.13.1 || >=16.0.0}

  scroll-into-view-if-needed@2.2.31:
    resolution: {integrity: sha512-dGCXy99wZQivjmjIqihaBQNjryrz5rueJY7eHfTdyWEiR4ttYpsajb14rn9s5d4DY4EcY6+4+U/maARBXJedkA==}

  scroll-into-view-if-needed@3.1.0:
    resolution: {integrity: sha512-49oNpRjWRvnU8NyGVmUaYG4jtTkNonFZI86MmGRDqBphEK2EXT9gdEUoQPZhuBM8yWHxCWbobltqYO5M4XrUvQ==}

  sdk-base@2.0.1:
    resolution: {integrity: sha512-eeG26wRwhtwYuKGCDM3LixCaxY27Pa/5lK4rLKhQa7HBjJ3U3Y+f81MMZQRsDw/8SC2Dao/83yJTXJ8aULuN8Q==}

  select@1.1.2:
    resolution: {integrity: sha512-OwpTSOfy6xSs1+pwcNrv0RBMOzI39Lp3qQKUTPVVPRjCdNa5JH/oPRiqsesIskK8TVgmRiHwO4KXlV2Li9dANA==}

  semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.6.0:
    resolution: {integrity: sha512-EnwXhrlwXMk9gKu5/flx5sv/an57AkRplG3hTK68W7FRDN+k+OWBj65M7719OkA82XLBxrcX0KSHj+X5COhOVg==}
    engines: {node: '>=10'}
    hasBin: true

  set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}

  set-function-name@2.0.2:
    resolution: {integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==}
    engines: {node: '>= 0.4'}

  set-value@2.0.1:
    resolution: {integrity: sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==}
    engines: {node: '>=0.10.0'}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  side-channel@1.0.6:
    resolution: {integrity: sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==}
    engines: {node: '>= 0.4'}

  siginfo@2.0.0:
    resolution: {integrity: sha512-ybx0WO1/8bSBLEWXZvEd7gMW3Sn3JFlW3TvX1nREbDLRNQNaeNN8WK0meBwPdAaOI7TtRRRJn/Es1zhrrCHu7g==}

  signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  sirv@2.0.4:
    resolution: {integrity: sha512-94Bdh3cC2PKrbgSOUqTiGPWVZeSiXfKOVZNJniWoqrWrRkB1CJzBU3NEbiTsPcYy1lDsANA/THzS+9WBiy5nfQ==}
    engines: {node: '>= 10'}

  slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}

  slate-history@0.109.0:
    resolution: {integrity: sha512-DHavPwrTTAEAV66eAocB3iQHEj65N6IVtbRK98ZuqGT0S44T3zXlhzY+5SZ7EPxRcoOYVt1dioRxXYM/+PmCiQ==}
    peerDependencies:
      slate: '>=0.65.3'

  slate-history@0.66.0:
    resolution: {integrity: sha512-6MWpxGQZiMvSINlCbMW43E2YBSVMCMCIwQfBzGssjWw4kb0qfvj0pIdblWNRQZD0hR6WHP+dHHgGSeVdMWzfng==}
    peerDependencies:
      slate: '>=0.65.3'

  slate@0.72.8:
    resolution: {integrity: sha512-/nJwTswQgnRurpK+bGJFH1oM7naD5qDmHd89JyiKNT2oOKD8marW0QSBtuFnwEbL5aGCS8AmrhXQgNOsn4osAw==}

  slice-ansi@5.0.0:
    resolution: {integrity: sha512-FC+lgizVPfie0kkhqUScwRu1O/lF6NOgJmlCgK+/LYxDCTk8sGelYaHDhFcDN+Sn3Cv+3VSa4Byeo+IMCzpMgQ==}
    engines: {node: '>=12'}

  slice-ansi@7.1.0:
    resolution: {integrity: sha512-bSiSngZ/jWeX93BqeIAbImyTbEihizcwNjFoRUIY/T1wWQsfsm2Vw1agPKylXvQTU7iASGdHhyqRlqQzfz+Htg==}
    engines: {node: '>=18'}

  snabbdom@3.6.2:
    resolution: {integrity: sha512-ig5qOnCDbugFntKi6c7Xlib8bA6xiJVk8O+WdFrV3wxbMqeHO0hXFQC4nAhPVWfZfi8255lcZkNhtIBINCc4+Q==}
    engines: {node: '>=12.17.0'}

  snapdragon-node@2.1.1:
    resolution: {integrity: sha512-O27l4xaMYt/RSQ5TR3vpWCAB5Kb/czIcqUFOM/C4fYcLnbZUc1PkjTAMjof2pBWaSTwOUd6qUHcFGVGj7aIwnw==}
    engines: {node: '>=0.10.0'}

  snapdragon-util@3.0.1:
    resolution: {integrity: sha512-mbKkMdQKsjX4BAL4bRYTj21edOf8cN7XHdYUJEe+Zn99hVEYcMvKPct1IqNe7+AZPirn8BCDOQBHQZknqmKlZQ==}
    engines: {node: '>=0.10.0'}

  snapdragon@0.8.2:
    resolution: {integrity: sha512-FtyOnWN/wCHTVXOMwvSv26d+ko5vWlIDD6zoUJ7LW8vh+ZBC8QdljveRP+crNrtBwioEUWy/4dMtbBjA4ioNlg==}
    engines: {node: '>=0.10.0'}

  sortablejs@1.14.0:
    resolution: {integrity: sha512-pBXvQCs5/33fdN1/39pPL0NZF20LeRbLQ5jtnheIPN9JQAaufGjKdWduZn4U7wCtVuzKhmRkI0DFYHYRbB2H1w==}

  source-map-js@1.2.0:
    resolution: {integrity: sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==}
    engines: {node: '>=0.10.0'}

  source-map-resolve@0.5.3:
    resolution: {integrity: sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw==}
    deprecated: See https://github.com/lydell/source-map-resolve#deprecated

  source-map-url@0.4.1:
    resolution: {integrity: sha512-cPiFOTLUKvJFIg4SKVScy4ilPPW6rFgMgfuZJPNoDuMs3nC1HbMUycBoJw77xFIp6z1UJQJOfx6C9GMH80DiTw==}
    deprecated: See https://github.com/lydell/source-map-url#deprecated

  source-map@0.5.7:
    resolution: {integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==}
    engines: {node: '>=0.10.0'}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  spark-md5@3.0.2:
    resolution: {integrity: sha512-wcFzz9cDfbuqe0FZzfi2or1sgyIrsDwmPwfZC4hiNidPdPINjeUwNfv5kldczoEAcjl9Y1L3SM7Uz2PUEQzxQw==}

  split-string@3.1.0:
    resolution: {integrity: sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw==}
    engines: {node: '>=0.10.0'}

  split2@3.2.2:
    resolution: {integrity: sha512-9NThjpgZnifTkJpzTZ7Eue85S49QwpNhZTq6GRJwObb6jnLFNGB7Qm73V5HewTROPyxD0C29xqmaI68bQtV+hg==}

  ssf@0.11.2:
    resolution: {integrity: sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g==}
    engines: {node: '>=0.8'}

  ssr-window@3.0.0:
    resolution: {integrity: sha512-q+8UfWDg9Itrg0yWK7oe5p/XRCJpJF9OBtXfOPgSJl+u3Xd5KI328RUEvUqSMVM9CiQUEf1QdBzJMkYGErj9QA==}

  ssr-window@4.0.2:
    resolution: {integrity: sha512-ISv/Ch+ig7SOtw7G2+qkwfVASzazUnvlDTwypdLoPoySv+6MqlOV10VwPSE6EWkGjhW50lUmghPmpYZXMu/+AQ==}

  stable@0.1.8:
    resolution: {integrity: sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==}
    deprecated: 'Modern JS already guarantees Array#sort() is a stable sort, so this library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility'

  stackback@0.0.2:
    resolution: {integrity: sha512-1XMJE5fQo1jGH6Y/7ebnwPOBEkIEnT4QF32d5R1+VXdXveM0IBMJt8zfaxX1P3QhVwrYe+576+jkANtSS2mBbw==}

  static-extend@0.1.2:
    resolution: {integrity: sha512-72E9+uLc27Mt718pMHt9VMNiAL4LMsmDbBva8mxWUCkT07fSzEGMYUCk0XWY6lp0j6RBAG4cJ3mWuZv2OE3s0g==}
    engines: {node: '>=0.10.0'}

  statuses@1.5.0:
    resolution: {integrity: sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==}
    engines: {node: '>= 0.6'}

  std-env@3.7.0:
    resolution: {integrity: sha512-JPbdCEQLj1w5GilpiHAx3qJvFndqybBysA3qUOnznweH4QbNYUsW/ea8QzSrnh0vNsezMMw5bcVool8lM0gwzg==}

  stream-http@2.8.2:
    resolution: {integrity: sha512-QllfrBhqF1DPcz46WxKTs6Mz1Bpc+8Qm6vbqOpVav5odAXwbyzwnEczoWqtxrsmlO+cJqtPrp/8gWKWjaKLLlA==}

  stream-shift@1.0.3:
    resolution: {integrity: sha512-76ORR0DO1o1hlKwTbi/DM3EXWGf3ZJYO8cXX5RJwnul2DEg2oyoZyjLNoQM8WsvZiFKCRfC1O0J7iCvie3RZmQ==}

  stream-wormhole@1.1.0:
    resolution: {integrity: sha512-gHFfL3px0Kctd6Po0M8TzEvt3De/xu6cnRrjlfYNhwbhLPLwigI2t1nc6jrzNuaYg5C4YF78PPFuQPzRiqn9ew==}
    engines: {node: '>=4.0.0'}

  strict-uri-encode@1.1.0:
    resolution: {integrity: sha512-R3f198pcvnB+5IpnBlRkphuE9n46WyVl8I39W/ZUTZLz4nqSP/oLYUrcnJrw462Ds8he4YKMov2efsTIw1BDGQ==}
    engines: {node: '>=0.10.0'}

  string-argv@0.3.2:
    resolution: {integrity: sha512-aqD2Q0144Z+/RqG52NeHEkZauTAUWJO8c6yTftGJKO3Tja5tUgIfmIl6kExvhtxSDP7fXB6DvzkfMpCd/F3G+Q==}
    engines: {node: '>=0.6.19'}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}

  string-width@7.1.0:
    resolution: {integrity: sha512-SEIJCWiX7Kg4c129n48aDRwLbFb2LJmXXFrWBG4NGaRtMQ3myKPKbwrD1BKqQn74oCoNMBVrfDEr5M9YxCsrkw==}
    engines: {node: '>=18'}

  string_decoder@1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}

  string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}

  strip-ansi@3.0.1:
    resolution: {integrity: sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==}
    engines: {node: '>=0.10.0'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}

  strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}

  strip-final-newline@3.0.0:
    resolution: {integrity: sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==}
    engines: {node: '>=12'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  strip-literal@2.0.0:
    resolution: {integrity: sha512-f9vHgsCWBq2ugHAkGMiiYY+AYG0D/cbloKKg0nhaaaSNsujdGIpVXCNsrJpCKr5M0f4aI31mr13UjY6GAuXCKA==}

  strnum@1.0.5:
    resolution: {integrity: sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==}

  supports-color@2.0.0:
    resolution: {integrity: sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==}
    engines: {node: '>=0.8.0'}

  supports-color@3.2.3:
    resolution: {integrity: sha512-Jds2VIYDrlp5ui7t8abHN2bjAu4LV/q4N2KivFPpGH0lrka0BMq/33AmECUXlKPcHigkNaqfXRENFju+rlcy+A==}
    engines: {node: '>=0.8.0'}

  supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  svg-baker@1.7.0:
    resolution: {integrity: sha512-nibslMbkXOIkqKVrfcncwha45f97fGuAOn1G99YwnwTj8kF9YiM6XexPcUso97NxOm6GsP0SIvYVIosBis1xLg==}

  svg-tags@1.0.0:
    resolution: {integrity: sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA==}

  svgo@2.8.0:
    resolution: {integrity: sha512-+N/Q9kV1+F+UeWYoSiULYo4xYSDQlTgb+ayMobAXPwMnLvop7oxKMo9OzIrX5x3eS4L4f2UHhc9axXwY8DpChg==}
    engines: {node: '>=10.13.0'}
    hasBin: true

  svgo@3.2.0:
    resolution: {integrity: sha512-4PP6CMW/V7l/GmKRKzsLR8xxjdHTV4IMvhTnpuHwwBazSIlw5W/5SmPjN8Dwyt7lKbSJrRDgp4t9ph0HgChFBQ==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  symbol-tree@3.2.4:
    resolution: {integrity: sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==}

  text-table@0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==}

  thenify-all@1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==}
    engines: {node: '>=0.8'}

  thenify@3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==}

  through@2.3.8:
    resolution: {integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==}

  tiny-emitter@2.1.0:
    resolution: {integrity: sha512-NB6Dk1A9xgQPMoGqC5CVXn123gWyte215ONT5Pp5a0yt4nlEoO1ZWeCwpncaekPHXO60i47ihFnZPiRPjRMq4Q==}

  tiny-warning@1.0.3:
    resolution: {integrity: sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA==}

  tinybench@2.6.0:
    resolution: {integrity: sha512-N8hW3PG/3aOoZAN5V/NSAEDz0ZixDSSt5b/a05iqtpgfLWMSVuCo7w0k2vVvEjdrIoeGqZzweX2WlyioNIHchA==}

  tinypool@0.8.2:
    resolution: {integrity: sha512-SUszKYe5wgsxnNOVlBYO6IC+8VGWdVGZWAqUxp3UErNBtptZvWbwyUOyzNL59zigz2rCA92QiL3wvG+JDSdJdQ==}
    engines: {node: '>=14.0.0'}

  tinyspy@2.2.1:
    resolution: {integrity: sha512-KYad6Vy5VDWV4GH3fjpseMQ/XU2BhIYP7Vzd0LG44qRWm/Yt2WCOTicFdvmgo6gWaqooMQCawTtILVQJupKu7A==}
    engines: {node: '>=14.0.0'}

  tippy.js@6.3.7:
    resolution: {integrity: sha512-E1d3oP2emgJ9dRQZdf3Kkn0qJgI6ZLpyS5z6ZkY1DF3kaQaBsGZsndEpHwx+eC+tYM41HaSNvNtLx8tU57FzTQ==}

  to-arraybuffer@1.0.1:
    resolution: {integrity: sha512-okFlQcoGTi4LQBG/PgSYblw9VOyptsz2KJZqc6qtgGdes8VktzUQkj4BI2blit072iS8VODNcMA+tvnS9dnuMA==}

  to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}

  to-object-path@0.3.0:
    resolution: {integrity: sha512-9mWHdnGRuh3onocaHzukyvCZhzvr6tiflAy/JRFXcJX0TjgfWA9pk9t8CMbzmBE4Jfw58pXbkngtBtqYxzNEyg==}
    engines: {node: '>=0.10.0'}

  to-regex-range@2.1.1:
    resolution: {integrity: sha512-ZZWNfCjUokXXDGXFpZehJIkZqq91BcULFq/Pi7M5i4JnxXdhMKAK682z8bCW3o8Hj1wuuzoKcW3DfVzaP6VuNg==}
    engines: {node: '>=0.10.0'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  to-regex@3.0.2:
    resolution: {integrity: sha512-FWtleNAtZ/Ki2qtqej2CXTOayOH9bHDQF+Q48VpWyDXjbYxA4Yz8iDB31zXOBUlOHHKidDbqGVrTUvQMPmBGBw==}
    engines: {node: '>=0.10.0'}

  totalist@3.0.1:
    resolution: {integrity: sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==}
    engines: {node: '>=6'}

  tough-cookie@4.1.3:
    resolution: {integrity: sha512-aX/y5pVRkfRnfmuX+OdbSdXvPe6ieKX/G2s7e98f4poJHnqH3281gDPm/metm6E/WRamfx7WC4HUqkWHfQHprw==}
    engines: {node: '>=6'}

  tr46@5.0.0:
    resolution: {integrity: sha512-tk2G5R2KRwBd+ZN0zaEXpmzdKyOYksXwywulIX95MBODjSzMIuQnQ3m8JxgbhnL1LeVo7lqQKsYa1O3Htl7K5g==}
    engines: {node: '>=18'}

  traverse@0.6.8:
    resolution: {integrity: sha512-aXJDbk6SnumuaZSANd21XAo15ucCDE38H4fkqiGsc3MhCK+wOlZvLP9cB/TvpHT0mOyWgC4Z8EwRlzqYSUzdsA==}
    engines: {node: '>= 0.4'}

  ts-api-utils@1.3.0:
    resolution: {integrity: sha512-UQMIo7pb8WRomKR1/+MFVLTroIvDVtMX3K6OUir8ynLyzB8Jeriont2bTAtmNPa1ekAgN7YPDyf6V+ygrdU+eQ==}
    engines: {node: '>=16'}
    peerDependencies:
      typescript: '>=4.2.0'

  tslib@1.14.1:
    resolution: {integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==}

  tslib@2.6.3:
    resolution: {integrity: sha512-xNvxJEOUiWPGhUuUdQgAJPKOOJfGnIyKySOc09XkKsgdUV/3E2zvwZYdejjmRgPCgcym1juLH3226yA7sEFJKQ==}

  type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}

  type-detect@4.0.8:
    resolution: {integrity: sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==}
    engines: {node: '>=4'}

  type-fest@0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    engines: {node: '>=10'}

  type@2.7.3:
    resolution: {integrity: sha512-8j+1QmAbPvLZow5Qpi6NCaN8FB60p/6x8/vfNqOk/hC+HuvFZhL4+WfekuhQLiqFZXOgQdrs3B+XxEmCc6b3FQ==}

  typedarray@0.0.6:
    resolution: {integrity: sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==}

  typescript@5.4.3:
    resolution: {integrity: sha512-KrPd3PKaCLr78MalgiwJnA25Nm8HAmdwN3mYUYZgG/wizIo9EainNVQI9/yDavtVFRN2h3k8uf3GLHuhDMgEHg==}
    engines: {node: '>=14.17'}
    hasBin: true

  uc.micro@2.1.0:
    resolution: {integrity: sha512-ARDJmphmdvUk6Glw7y9DQ2bFkKBHwQHLi2lsaH6PPmz/Ka9sFOBsBluozhDltWmnv9u/cF6Rt87znRTPV+yp/A==}

  ufo@1.5.3:
    resolution: {integrity: sha512-Y7HYmWaFwPUmkoQCUIAYpKqkOf+SbVj/2fJJZ4RJMCfZp0rTGwRbzQD+HghfnhKOjL9E01okqz+ncJskGYfBNw==}

  unconfig@0.3.11:
    resolution: {integrity: sha512-bV/nqePAKv71v3HdVUn6UefbsDKQWRX+bJIkiSm0+twIds6WiD2bJLWWT3i214+J/B4edufZpG2w7Y63Vbwxow==}

  undici-types@5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==}

  unescape@1.0.1:
    resolution: {integrity: sha512-O0+af1Gs50lyH1nUu3ZyYS1cRh01Q/kUKatTOkSs7jukXE6/NebucDVxyiDsA9AQ4JC1V1jUH9EO8JX2nMDgGQ==}
    engines: {node: '>=0.10.0'}

  union-value@1.0.1:
    resolution: {integrity: sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg==}
    engines: {node: '>=0.10.0'}

  universalify@0.2.0:
    resolution: {integrity: sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==}
    engines: {node: '>= 4.0.0'}

  universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}

  unocss@0.58.6:
    resolution: {integrity: sha512-HBstDtC6KKD5yCYh5hHpPdHGZai0B/iLlDwkOIK+xfQYrvl8tNBvKfRz3xgiaI5MJ+fLmEOxbfXQIjleU1A0iA==}
    engines: {node: '>=14'}
    peerDependencies:
      '@unocss/webpack': 0.58.6
      vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0
    peerDependenciesMeta:
      '@unocss/webpack':
        optional: true
      vite:
        optional: true

  unset-value@1.0.0:
    resolution: {integrity: sha512-PcA2tsuGSF9cnySLHTLSh2qrQiJ70mn+r+Glzxv2TWZblxsxCC52BDlZoPCsz7STd9pN7EZetkWZBAvk4cgZdQ==}
    engines: {node: '>=0.10.0'}

  update-browserslist-db@1.0.13:
    resolution: {integrity: sha512-xebP81SNcPuNpPP3uzeW1NYXxI3rxyJzF3pD6sH4jE7o/IX+WtSpwnVU+qIsDPyk0d3hmFQ7mjqc6AtV604hbg==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  urix@0.1.0:
    resolution: {integrity: sha512-Am1ousAhSLBeB9cG/7k7r2R0zj50uDRlZHPGbazid5s9rlF1F/QKYObEKSIunSjIOkJZqwRRLpvewjEkM7pSqg==}
    deprecated: Please see https://github.com/lydell/urix#deprecated

  url-parse@1.5.10:
    resolution: {integrity: sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==}

  urllib@2.41.0:
    resolution: {integrity: sha512-pNXdxEv52L67jahLT+/7QE+Fup1y2Gc6EdmrAhQ6OpQIC2rl14oWwv9hvk1GXOZqEnJNwRXHABuwgPOs1CtL7g==}
    engines: {node: '>= 0.10.0'}
    peerDependencies:
      proxy-agent: ^5.0.0
    peerDependenciesMeta:
      proxy-agent:
        optional: true

  use@3.1.1:
    resolution: {integrity: sha512-cwESVXlO3url9YWlFW/TA9cshCEhtu7IKJ/p5soJ/gGpj7vbvFrAY/eIioQ6Dw23KjZhYgiIo8HOs1nQ2vr/oQ==}
    engines: {node: '>=0.10.0'}

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  utility@1.18.0:
    resolution: {integrity: sha512-PYxZDA+6QtvRvm//++aGdmKG/cI07jNwbROz0Ql+VzFV1+Z0Dy55NI4zZ7RHc9KKpBePNFwoErqIuqQv/cjiTA==}
    engines: {node: '>= 0.12.0'}

  uuid@9.0.1:
    resolution: {integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==}
    hasBin: true

  vanilla-picker@2.12.3:
    resolution: {integrity: sha512-qVkT1E7yMbUsB2mmJNFmaXMWE2hF8ffqzMMwe9zdAikd8u2VfnsVY2HQcOUi2F38bgbxzlJBEdS1UUhOXdF9GQ==}

  vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}

  vite-node@1.4.0:
    resolution: {integrity: sha512-VZDAseqjrHgNd4Kh8icYHWzTKSCZMhia7GyHfhtzLW33fZlG9SwsB6CEhgyVOWkJfJ2pFLrp/Gj1FSfAiqH9Lw==}
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true

  vite-plugin-svg-icons@2.0.1:
    resolution: {integrity: sha512-6ktD+DhV6Rz3VtedYvBKKVA2eXF+sAQVaKkKLDSqGUfnhqXl3bj5PPkVTl3VexfTuZy66PmINi8Q6eFnVfRUmA==}
    peerDependencies:
      vite: '>=2.0.0'

  vite-svg-loader@5.1.0:
    resolution: {integrity: sha512-M/wqwtOEjgb956/+m5ZrYT/Iq6Hax0OakWbokj8+9PXOnB7b/4AxESHieEtnNEy7ZpjsjYW1/5nK8fATQMmRxw==}
    peerDependencies:
      vue: '>=3.2.13'

  vite@5.3.4:
    resolution: {integrity: sha512-Cw+7zL3ZG9/NZBB8C+8QbQZmR54GwqIz+WMI4b3JgdYJvX+ny9AjJXqkGQlDXSXRP9rP0B4tbciRMOVEKulVOA==}
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || >=20.0.0
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true

  vitest@1.4.0:
    resolution: {integrity: sha512-gujzn0g7fmwf83/WzrDTnncZt2UiXP41mHuFYFrdwaLRVQ6JYQEiME2IfEjU3vcFL3VKa75XhI3lFgn+hfVsQw==}
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@edge-runtime/vm': '*'
      '@types/node': ^18.0.0 || >=20.0.0
      '@vitest/browser': 1.4.0
      '@vitest/ui': 1.4.0
      happy-dom: '*'
      jsdom: '*'
    peerDependenciesMeta:
      '@edge-runtime/vm':
        optional: true
      '@types/node':
        optional: true
      '@vitest/browser':
        optional: true
      '@vitest/ui':
        optional: true
      happy-dom:
        optional: true
      jsdom:
        optional: true

  vue-component-type-helpers@2.0.7:
    resolution: {integrity: sha512-7e12Evdll7JcTIocojgnCgwocX4WzIYStGClBQ+QuWPinZo/vQolv2EMq4a3lg16TKfwWafLimG77bxb56UauA==}

  vue-cropperjs@5.0.0:
    resolution: {integrity: sha512-RhnC8O33uRZNkn74aiHZwNHnBJOXWlS4P6gsRI0lw4cZlWjKSCywZI9oSI9POlIPI6OYv30jvnHMXGch85tw7w==}
    peerDependencies:
      vue: '>=3.0.0'

  vue-demi@0.14.7:
    resolution: {integrity: sha512-EOG8KXDQNwkJILkx/gPcoL/7vH+hORoBaKgGe+6W7VFMvCYJfmF2dGbvgDroVnI8LU7/kTu8mbjRZGBU1z9NTA==}
    engines: {node: '>=12'}
    hasBin: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  vue-router@4.3.0:
    resolution: {integrity: sha512-dqUcs8tUeG+ssgWhcPbjHvazML16Oga5w34uCUmsk7i0BcnskoLGwjpa15fqMr2Fa5JgVBrdL2MEgqz6XZ/6IQ==}
    peerDependencies:
      vue: ^3.2.0

  vue-template-compiler@2.7.16:
    resolution: {integrity: sha512-AYbUWAJHLGGQM7+cNTELw+KsOG9nl2CnSv467WobS5Cv9uk3wFcnr1Etsz2sEIHEZvw1U+o9mRlEO6QbZvUPGQ==}

  vue-tsc@2.0.7:
    resolution: {integrity: sha512-LYa0nInkfcDBB7y8jQ9FQ4riJTRNTdh98zK/hzt4gEpBZQmf30dPhP+odzCa+cedGz6B/guvJEd0BavZaRptjg==}
    hasBin: true
    peerDependencies:
      typescript: '*'

  vue@3.4.21:
    resolution: {integrity: sha512-5hjyV/jLEIKD/jYl4cavMcnzKwjMKohureP8ejn3hhEjwhWIhWeuzL2kJAjzl/WyVsgPY56Sy4Z40C3lVshxXA==}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  vuedraggable-es@4.1.1:
    resolution: {integrity: sha512-F35pjSwC8HS/lnaOd+B59nYR4FZmwuhWAzccK9xftRuWds8SU1TZh5myKVM86j5dFOI7S26O64Kwe7LUHnXjlA==}
    peerDependencies:
      vue: ^3.2.31

  w3c-keyname@2.2.8:
    resolution: {integrity: sha512-dpojBhNsCNN7T82Tm7k26A6G9ML3NkhDsnw9n/eoxSRlVBB4CEtIQ/KTCLI2Fwf3ataSXRhYFkQi3SlnFwPvPQ==}

  w3c-xmlserializer@5.0.0:
    resolution: {integrity: sha512-o8qghlI8NZHU1lLPrpi2+Uq7abh4GGPpYANlalzWxyWteJOCsr/P+oPBA49TOLu5FTZO4d3F9MnWJfiMo4BkmA==}
    engines: {node: '>=18'}

  webidl-conversions@7.0.0:
    resolution: {integrity: sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==}
    engines: {node: '>=12'}

  whatwg-encoding@3.1.1:
    resolution: {integrity: sha512-6qN4hJdMwfYBtE3YBTTHhoeuUrDBPZmbQaxWAqSALV/MeEnR5z1xd8UKud2RAkFoPkmB+hli1TZSnyi84xz1vQ==}
    engines: {node: '>=18'}

  whatwg-mimetype@4.0.0:
    resolution: {integrity: sha512-QaKxh0eNIi2mE9p2vEdzfagOKHCcj1pJ56EEHGQOVxp8r9/iszLUUV7v89x9O1p/T+NlTM5W7jW6+cz4Fq1YVg==}
    engines: {node: '>=18'}

  whatwg-url@14.0.0:
    resolution: {integrity: sha512-1lfMEm2IEr7RIV+f4lUNPOqfFL+pO+Xw3fJSqmjX9AbXcXcYOkCe1P6+9VBZB6n94af16NfZf+sSk0JCBZC9aw==}
    engines: {node: '>=18'}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  why-is-node-running@2.2.2:
    resolution: {integrity: sha512-6tSwToZxTOcotxHeA+qGCq1mVzKR3CwcJGmVcY+QE8SHy6TnpFnh8PAvPNHYr7EcuVeG0QSMxtYCuO1ta/G/oA==}
    engines: {node: '>=8'}
    hasBin: true

  wildcard@1.1.2:
    resolution: {integrity: sha512-DXukZJxpHA8LuotRwL0pP1+rS6CS7FF2qStDDE1C7DDg2rLud2PXRMuEDYIPhgEezwnlHNL4c+N6MfMTjCGTng==}

  win-release@1.1.1:
    resolution: {integrity: sha512-iCRnKVvGxOQdsKhcQId2PXV1vV3J/sDPXKA4Oe9+Eti2nb2ESEsYHRYls/UjoUW3bIc5ZDO8dTH50A/5iVN+bw==}
    engines: {node: '>=0.10.0'}

  wmf@1.0.2:
    resolution: {integrity: sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw==}
    engines: {node: '>=0.8'}

  word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}

  word@0.3.0:
    resolution: {integrity: sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA==}
    engines: {node: '>=0.8'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}

  wrap-ansi@9.0.0:
    resolution: {integrity: sha512-G8ura3S+3Z2G+mkgNRq8dqaFZAuxfsxpBB8OCTGRTCtp+l/v9nbFNmCUP1BZMts3G1142MsZfn6eeUKrr4PD1Q==}
    engines: {node: '>=18'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  ws@7.5.9:
    resolution: {integrity: sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q==}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  ws@8.16.0:
    resolution: {integrity: sha512-HS0c//TP7Ina87TfiPUz1rQzMhHrl/SG2guqRcTOIUYD2q8uhUdNHZYJUaQ8aTGPzCh+c6oawMKW35nFl1dxyQ==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xlsx@0.18.5:
    resolution: {integrity: sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ==}
    engines: {node: '>=0.8'}
    hasBin: true

  xml-name-validator@5.0.0:
    resolution: {integrity: sha512-EvGK8EJ3DhaHfbRlETOWAS5pO9MZITeauHKJyb8wyajUfQUenkIg2MvLDTZ4T/TgIcm3HU0TFBgWWboAZ30UHg==}
    engines: {node: '>=18'}

  xml2js@0.6.2:
    resolution: {integrity: sha512-T4rieHaC1EXcES0Kxxj4JWgaUQHDk+qwHcYOCFHfiwKz7tOVPLq7Hjq9dM1WCMhylqMEfP7hMcOIChvotiZegA==}
    engines: {node: '>=4.0.0'}

  xmlbuilder@11.0.1:
    resolution: {integrity: sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==}
    engines: {node: '>=4.0'}

  xmlchars@2.2.0:
    resolution: {integrity: sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==}

  xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}

  yaml@2.3.4:
    resolution: {integrity: sha512-8aAvwVUSHpfEqTQ4w/KMlf3HcRdt50E5ODIQJBw1fQ5RL34xabzxtUlzTXVqc4rkZsPbvrXKWnABCD7kWSmocA==}
    engines: {node: '>= 14'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}

  yocto-queue@1.0.0:
    resolution: {integrity: sha512-9bnSc/HEW2uRy67wc+T8UwauLuPJVn28jb+GtJY16iiKWyvmYJRXVT4UamsAEGQfPohgr2q4Tq0sQbQlxTfi1g==}
    engines: {node: '>=12.20'}

snapshots:

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25

  '@antfu/install-pkg@0.1.1':
    dependencies:
      execa: 5.1.1
      find-up: 5.0.0

  '@antfu/utils@0.7.7': {}

  '@aws-crypto/crc32@3.0.0':
    dependencies:
      '@aws-crypto/util': 3.0.0
      '@aws-sdk/types': 3.577.0
      tslib: 1.14.1

  '@aws-crypto/crc32c@3.0.0':
    dependencies:
      '@aws-crypto/util': 3.0.0
      '@aws-sdk/types': 3.577.0
      tslib: 1.14.1

  '@aws-crypto/ie11-detection@3.0.0':
    dependencies:
      tslib: 1.14.1

  '@aws-crypto/sha1-browser@3.0.0':
    dependencies:
      '@aws-crypto/ie11-detection': 3.0.0
      '@aws-crypto/supports-web-crypto': 3.0.0
      '@aws-crypto/util': 3.0.0
      '@aws-sdk/types': 3.577.0
      '@aws-sdk/util-locate-window': 3.568.0
      '@aws-sdk/util-utf8-browser': 3.259.0
      tslib: 1.14.1

  '@aws-crypto/sha256-browser@3.0.0':
    dependencies:
      '@aws-crypto/ie11-detection': 3.0.0
      '@aws-crypto/sha256-js': 3.0.0
      '@aws-crypto/supports-web-crypto': 3.0.0
      '@aws-crypto/util': 3.0.0
      '@aws-sdk/types': 3.577.0
      '@aws-sdk/util-locate-window': 3.568.0
      '@aws-sdk/util-utf8-browser': 3.259.0
      tslib: 1.14.1

  '@aws-crypto/sha256-js@3.0.0':
    dependencies:
      '@aws-crypto/util': 3.0.0
      '@aws-sdk/types': 3.577.0
      tslib: 1.14.1

  '@aws-crypto/supports-web-crypto@3.0.0':
    dependencies:
      tslib: 1.14.1

  '@aws-crypto/util@3.0.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@aws-sdk/util-utf8-browser': 3.259.0
      tslib: 1.14.1

  '@aws-sdk/client-s3@3.592.0(aws-crt@1.21.2)':
    dependencies:
      '@aws-crypto/sha1-browser': 3.0.0
      '@aws-crypto/sha256-browser': 3.0.0
      '@aws-crypto/sha256-js': 3.0.0
      '@aws-sdk/client-sso-oidc': 3.592.0(@aws-sdk/client-sts@3.592.0(aws-crt@1.21.2))(aws-crt@1.21.2)
      '@aws-sdk/client-sts': 3.592.0(aws-crt@1.21.2)
      '@aws-sdk/core': 3.592.0
      '@aws-sdk/credential-provider-node': 3.592.0(@aws-sdk/client-sso-oidc@3.592.0(aws-crt@1.21.2))(@aws-sdk/client-sts@3.592.0(aws-crt@1.21.2))(aws-crt@1.21.2)
      '@aws-sdk/middleware-bucket-endpoint': 3.587.0
      '@aws-sdk/middleware-expect-continue': 3.577.0
      '@aws-sdk/middleware-flexible-checksums': 3.587.0
      '@aws-sdk/middleware-host-header': 3.577.0
      '@aws-sdk/middleware-location-constraint': 3.577.0
      '@aws-sdk/middleware-logger': 3.577.0
      '@aws-sdk/middleware-recursion-detection': 3.577.0
      '@aws-sdk/middleware-sdk-s3': 3.587.0
      '@aws-sdk/middleware-signing': 3.587.0
      '@aws-sdk/middleware-ssec': 3.577.0
      '@aws-sdk/middleware-user-agent': 3.587.0
      '@aws-sdk/region-config-resolver': 3.587.0
      '@aws-sdk/signature-v4-multi-region': 3.587.0
      '@aws-sdk/types': 3.577.0
      '@aws-sdk/util-endpoints': 3.587.0
      '@aws-sdk/util-user-agent-browser': 3.577.0
      '@aws-sdk/util-user-agent-node': 3.587.0(aws-crt@1.21.2)
      '@aws-sdk/xml-builder': 3.575.0
      '@smithy/config-resolver': 3.0.1
      '@smithy/core': 2.2.0
      '@smithy/eventstream-serde-browser': 3.0.0
      '@smithy/eventstream-serde-config-resolver': 3.0.0
      '@smithy/eventstream-serde-node': 3.0.0
      '@smithy/fetch-http-handler': 3.0.1
      '@smithy/hash-blob-browser': 3.0.0
      '@smithy/hash-node': 3.0.0
      '@smithy/hash-stream-node': 3.0.0
      '@smithy/invalid-dependency': 3.0.0
      '@smithy/md5-js': 3.0.0
      '@smithy/middleware-content-length': 3.0.0
      '@smithy/middleware-endpoint': 3.0.1
      '@smithy/middleware-retry': 3.0.3
      '@smithy/middleware-serde': 3.0.0
      '@smithy/middleware-stack': 3.0.0
      '@smithy/node-config-provider': 3.1.0
      '@smithy/node-http-handler': 3.0.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/smithy-client': 3.1.1
      '@smithy/types': 3.0.0
      '@smithy/url-parser': 3.0.0
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.3
      '@smithy/util-defaults-mode-node': 3.0.3
      '@smithy/util-endpoints': 2.0.1
      '@smithy/util-retry': 3.0.0
      '@smithy/util-stream': 3.0.1
      '@smithy/util-utf8': 3.0.0
      '@smithy/util-waiter': 3.0.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-sso-oidc@3.592.0(@aws-sdk/client-sts@3.592.0(aws-crt@1.21.2))(aws-crt@1.21.2)':
    dependencies:
      '@aws-crypto/sha256-browser': 3.0.0
      '@aws-crypto/sha256-js': 3.0.0
      '@aws-sdk/client-sts': 3.592.0(aws-crt@1.21.2)
      '@aws-sdk/core': 3.592.0
      '@aws-sdk/credential-provider-node': 3.592.0(@aws-sdk/client-sso-oidc@3.592.0(aws-crt@1.21.2))(@aws-sdk/client-sts@3.592.0(aws-crt@1.21.2))(aws-crt@1.21.2)
      '@aws-sdk/middleware-host-header': 3.577.0
      '@aws-sdk/middleware-logger': 3.577.0
      '@aws-sdk/middleware-recursion-detection': 3.577.0
      '@aws-sdk/middleware-user-agent': 3.587.0
      '@aws-sdk/region-config-resolver': 3.587.0
      '@aws-sdk/types': 3.577.0
      '@aws-sdk/util-endpoints': 3.587.0
      '@aws-sdk/util-user-agent-browser': 3.577.0
      '@aws-sdk/util-user-agent-node': 3.587.0(aws-crt@1.21.2)
      '@smithy/config-resolver': 3.0.1
      '@smithy/core': 2.2.0
      '@smithy/fetch-http-handler': 3.0.1
      '@smithy/hash-node': 3.0.0
      '@smithy/invalid-dependency': 3.0.0
      '@smithy/middleware-content-length': 3.0.0
      '@smithy/middleware-endpoint': 3.0.1
      '@smithy/middleware-retry': 3.0.3
      '@smithy/middleware-serde': 3.0.0
      '@smithy/middleware-stack': 3.0.0
      '@smithy/node-config-provider': 3.1.0
      '@smithy/node-http-handler': 3.0.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/smithy-client': 3.1.1
      '@smithy/types': 3.0.0
      '@smithy/url-parser': 3.0.0
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.3
      '@smithy/util-defaults-mode-node': 3.0.3
      '@smithy/util-endpoints': 2.0.1
      '@smithy/util-middleware': 3.0.0
      '@smithy/util-retry': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - '@aws-sdk/client-sts'
      - aws-crt

  '@aws-sdk/client-sso@3.592.0(aws-crt@1.21.2)':
    dependencies:
      '@aws-crypto/sha256-browser': 3.0.0
      '@aws-crypto/sha256-js': 3.0.0
      '@aws-sdk/core': 3.592.0
      '@aws-sdk/middleware-host-header': 3.577.0
      '@aws-sdk/middleware-logger': 3.577.0
      '@aws-sdk/middleware-recursion-detection': 3.577.0
      '@aws-sdk/middleware-user-agent': 3.587.0
      '@aws-sdk/region-config-resolver': 3.587.0
      '@aws-sdk/types': 3.577.0
      '@aws-sdk/util-endpoints': 3.587.0
      '@aws-sdk/util-user-agent-browser': 3.577.0
      '@aws-sdk/util-user-agent-node': 3.587.0(aws-crt@1.21.2)
      '@smithy/config-resolver': 3.0.1
      '@smithy/core': 2.2.0
      '@smithy/fetch-http-handler': 3.0.1
      '@smithy/hash-node': 3.0.0
      '@smithy/invalid-dependency': 3.0.0
      '@smithy/middleware-content-length': 3.0.0
      '@smithy/middleware-endpoint': 3.0.1
      '@smithy/middleware-retry': 3.0.3
      '@smithy/middleware-serde': 3.0.0
      '@smithy/middleware-stack': 3.0.0
      '@smithy/node-config-provider': 3.1.0
      '@smithy/node-http-handler': 3.0.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/smithy-client': 3.1.1
      '@smithy/types': 3.0.0
      '@smithy/url-parser': 3.0.0
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.3
      '@smithy/util-defaults-mode-node': 3.0.3
      '@smithy/util-endpoints': 2.0.1
      '@smithy/util-middleware': 3.0.0
      '@smithy/util-retry': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-sts@3.592.0(aws-crt@1.21.2)':
    dependencies:
      '@aws-crypto/sha256-browser': 3.0.0
      '@aws-crypto/sha256-js': 3.0.0
      '@aws-sdk/client-sso-oidc': 3.592.0(@aws-sdk/client-sts@3.592.0(aws-crt@1.21.2))(aws-crt@1.21.2)
      '@aws-sdk/core': 3.592.0
      '@aws-sdk/credential-provider-node': 3.592.0(@aws-sdk/client-sso-oidc@3.592.0(aws-crt@1.21.2))(@aws-sdk/client-sts@3.592.0(aws-crt@1.21.2))(aws-crt@1.21.2)
      '@aws-sdk/middleware-host-header': 3.577.0
      '@aws-sdk/middleware-logger': 3.577.0
      '@aws-sdk/middleware-recursion-detection': 3.577.0
      '@aws-sdk/middleware-user-agent': 3.587.0
      '@aws-sdk/region-config-resolver': 3.587.0
      '@aws-sdk/types': 3.577.0
      '@aws-sdk/util-endpoints': 3.587.0
      '@aws-sdk/util-user-agent-browser': 3.577.0
      '@aws-sdk/util-user-agent-node': 3.587.0(aws-crt@1.21.2)
      '@smithy/config-resolver': 3.0.1
      '@smithy/core': 2.2.0
      '@smithy/fetch-http-handler': 3.0.1
      '@smithy/hash-node': 3.0.0
      '@smithy/invalid-dependency': 3.0.0
      '@smithy/middleware-content-length': 3.0.0
      '@smithy/middleware-endpoint': 3.0.1
      '@smithy/middleware-retry': 3.0.3
      '@smithy/middleware-serde': 3.0.0
      '@smithy/middleware-stack': 3.0.0
      '@smithy/node-config-provider': 3.1.0
      '@smithy/node-http-handler': 3.0.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/smithy-client': 3.1.1
      '@smithy/types': 3.0.0
      '@smithy/url-parser': 3.0.0
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.3
      '@smithy/util-defaults-mode-node': 3.0.3
      '@smithy/util-endpoints': 2.0.1
      '@smithy/util-middleware': 3.0.0
      '@smithy/util-retry': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/core@3.592.0':
    dependencies:
      '@smithy/core': 2.2.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/signature-v4': 3.0.0
      '@smithy/smithy-client': 3.1.1
      '@smithy/types': 3.0.0
      fast-xml-parser: 4.2.5
      tslib: 2.6.3

  '@aws-sdk/credential-provider-env@3.587.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/property-provider': 3.1.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/credential-provider-http@3.587.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/fetch-http-handler': 3.0.1
      '@smithy/node-http-handler': 3.0.0
      '@smithy/property-provider': 3.1.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/smithy-client': 3.1.1
      '@smithy/types': 3.0.0
      '@smithy/util-stream': 3.0.1
      tslib: 2.6.3

  '@aws-sdk/credential-provider-ini@3.592.0(@aws-sdk/client-sso-oidc@3.592.0(aws-crt@1.21.2))(@aws-sdk/client-sts@3.592.0(aws-crt@1.21.2))(aws-crt@1.21.2)':
    dependencies:
      '@aws-sdk/client-sts': 3.592.0(aws-crt@1.21.2)
      '@aws-sdk/credential-provider-env': 3.587.0
      '@aws-sdk/credential-provider-http': 3.587.0
      '@aws-sdk/credential-provider-process': 3.587.0
      '@aws-sdk/credential-provider-sso': 3.592.0(@aws-sdk/client-sso-oidc@3.592.0(aws-crt@1.21.2))(aws-crt@1.21.2)
      '@aws-sdk/credential-provider-web-identity': 3.587.0(@aws-sdk/client-sts@3.592.0(aws-crt@1.21.2))
      '@aws-sdk/types': 3.577.0
      '@smithy/credential-provider-imds': 3.1.0
      '@smithy/property-provider': 3.1.0
      '@smithy/shared-ini-file-loader': 3.1.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - aws-crt

  '@aws-sdk/credential-provider-node@3.592.0(@aws-sdk/client-sso-oidc@3.592.0(aws-crt@1.21.2))(@aws-sdk/client-sts@3.592.0(aws-crt@1.21.2))(aws-crt@1.21.2)':
    dependencies:
      '@aws-sdk/credential-provider-env': 3.587.0
      '@aws-sdk/credential-provider-http': 3.587.0
      '@aws-sdk/credential-provider-ini': 3.592.0(@aws-sdk/client-sso-oidc@3.592.0(aws-crt@1.21.2))(@aws-sdk/client-sts@3.592.0(aws-crt@1.21.2))(aws-crt@1.21.2)
      '@aws-sdk/credential-provider-process': 3.587.0
      '@aws-sdk/credential-provider-sso': 3.592.0(@aws-sdk/client-sso-oidc@3.592.0(aws-crt@1.21.2))(aws-crt@1.21.2)
      '@aws-sdk/credential-provider-web-identity': 3.587.0(@aws-sdk/client-sts@3.592.0(aws-crt@1.21.2))
      '@aws-sdk/types': 3.577.0
      '@smithy/credential-provider-imds': 3.1.0
      '@smithy/property-provider': 3.1.0
      '@smithy/shared-ini-file-loader': 3.1.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - '@aws-sdk/client-sts'
      - aws-crt

  '@aws-sdk/credential-provider-process@3.587.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/property-provider': 3.1.0
      '@smithy/shared-ini-file-loader': 3.1.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/credential-provider-sso@3.592.0(@aws-sdk/client-sso-oidc@3.592.0(aws-crt@1.21.2))(aws-crt@1.21.2)':
    dependencies:
      '@aws-sdk/client-sso': 3.592.0(aws-crt@1.21.2)
      '@aws-sdk/token-providers': 3.587.0(@aws-sdk/client-sso-oidc@3.592.0(aws-crt@1.21.2))
      '@aws-sdk/types': 3.577.0
      '@smithy/property-provider': 3.1.0
      '@smithy/shared-ini-file-loader': 3.1.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - aws-crt

  '@aws-sdk/credential-provider-web-identity@3.587.0(@aws-sdk/client-sts@3.592.0(aws-crt@1.21.2))':
    dependencies:
      '@aws-sdk/client-sts': 3.592.0(aws-crt@1.21.2)
      '@aws-sdk/types': 3.577.0
      '@smithy/property-provider': 3.1.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/middleware-bucket-endpoint@3.587.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@aws-sdk/util-arn-parser': 3.568.0
      '@smithy/node-config-provider': 3.1.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/types': 3.0.0
      '@smithy/util-config-provider': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/middleware-expect-continue@3.577.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/middleware-flexible-checksums@3.587.0':
    dependencies:
      '@aws-crypto/crc32': 3.0.0
      '@aws-crypto/crc32c': 3.0.0
      '@aws-sdk/types': 3.577.0
      '@smithy/is-array-buffer': 3.0.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/types': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/middleware-host-header@3.577.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/middleware-location-constraint@3.577.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/middleware-logger@3.577.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/middleware-recursion-detection@3.577.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/middleware-sdk-s3@3.587.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@aws-sdk/util-arn-parser': 3.568.0
      '@smithy/node-config-provider': 3.1.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/signature-v4': 3.0.0
      '@smithy/smithy-client': 3.1.1
      '@smithy/types': 3.0.0
      '@smithy/util-config-provider': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/middleware-signing@3.587.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/property-provider': 3.1.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/signature-v4': 3.0.0
      '@smithy/types': 3.0.0
      '@smithy/util-middleware': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/middleware-ssec@3.577.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/middleware-user-agent@3.587.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@aws-sdk/util-endpoints': 3.587.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/region-config-resolver@3.587.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/node-config-provider': 3.1.0
      '@smithy/types': 3.0.0
      '@smithy/util-config-provider': 3.0.0
      '@smithy/util-middleware': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/s3-request-presigner@3.592.0':
    dependencies:
      '@aws-sdk/signature-v4-multi-region': 3.587.0
      '@aws-sdk/types': 3.577.0
      '@aws-sdk/util-format-url': 3.577.0
      '@smithy/middleware-endpoint': 3.0.1
      '@smithy/protocol-http': 4.0.0
      '@smithy/smithy-client': 3.1.1
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/signature-v4-crt@3.587.0':
    dependencies:
      '@aws-sdk/signature-v4-multi-region': 3.587.0
      '@aws-sdk/types': 3.577.0
      '@aws-sdk/util-user-agent-node': 3.587.0(aws-crt@1.21.2)
      '@smithy/querystring-parser': 3.0.0
      '@smithy/signature-v4': 3.0.0
      '@smithy/types': 3.0.0
      '@smithy/util-middleware': 3.0.0
      aws-crt: 1.21.2
      tslib: 2.6.3
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate

  '@aws-sdk/signature-v4-multi-region@3.587.0':
    dependencies:
      '@aws-sdk/middleware-sdk-s3': 3.587.0
      '@aws-sdk/types': 3.577.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/signature-v4': 3.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/token-providers@3.587.0(@aws-sdk/client-sso-oidc@3.592.0(aws-crt@1.21.2))':
    dependencies:
      '@aws-sdk/client-sso-oidc': 3.592.0(@aws-sdk/client-sts@3.592.0(aws-crt@1.21.2))(aws-crt@1.21.2)
      '@aws-sdk/types': 3.577.0
      '@smithy/property-provider': 3.1.0
      '@smithy/shared-ini-file-loader': 3.1.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/types@3.577.0':
    dependencies:
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/util-arn-parser@3.568.0':
    dependencies:
      tslib: 2.6.3

  '@aws-sdk/util-endpoints@3.587.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/types': 3.0.0
      '@smithy/util-endpoints': 2.0.1
      tslib: 2.6.3

  '@aws-sdk/util-format-url@3.577.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/querystring-builder': 3.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@aws-sdk/util-locate-window@3.568.0':
    dependencies:
      tslib: 2.6.3

  '@aws-sdk/util-user-agent-browser@3.577.0':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/types': 3.0.0
      bowser: 2.11.0
      tslib: 2.6.3

  '@aws-sdk/util-user-agent-node@3.587.0(aws-crt@1.21.2)':
    dependencies:
      '@aws-sdk/types': 3.577.0
      '@smithy/node-config-provider': 3.1.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3
    optionalDependencies:
      aws-crt: 1.21.2

  '@aws-sdk/util-utf8-browser@3.259.0':
    dependencies:
      tslib: 2.6.3

  '@aws-sdk/xml-builder@3.575.0':
    dependencies:
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@babel/code-frame@7.24.2':
    dependencies:
      '@babel/highlight': 7.24.2
      picocolors: 1.0.0

  '@babel/compat-data@7.24.1': {}

  '@babel/core@7.24.3':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.24.2
      '@babel/generator': 7.24.1
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.24.3)
      '@babel/helpers': 7.24.1
      '@babel/parser': 7.24.1
      '@babel/template': 7.24.0
      '@babel/traverse': 7.24.1
      '@babel/types': 7.24.0
      convert-source-map: 2.0.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.24.1':
    dependencies:
      '@babel/types': 7.24.0
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 2.5.2

  '@babel/helper-annotate-as-pure@7.22.5':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-compilation-targets@7.23.6':
    dependencies:
      '@babel/compat-data': 7.24.1
      '@babel/helper-validator-option': 7.23.5
      browserslist: 4.23.0
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.24.1(@babel/core@7.24.3)':
    dependencies:
      '@babel/core': 7.24.3
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-member-expression-to-functions': 7.23.0
      '@babel/helper-optimise-call-expression': 7.22.5
      '@babel/helper-replace-supers': 7.24.1(@babel/core@7.24.3)
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      semver: 6.3.1

  '@babel/helper-environment-visitor@7.22.20': {}

  '@babel/helper-function-name@7.23.0':
    dependencies:
      '@babel/template': 7.24.0
      '@babel/types': 7.24.0

  '@babel/helper-hoist-variables@7.22.5':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-member-expression-to-functions@7.23.0':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-module-imports@7.22.15':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-module-imports@7.24.3':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-module-transforms@7.23.3(@babel/core@7.24.3)':
    dependencies:
      '@babel/core': 7.24.3
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-module-imports': 7.24.3
      '@babel/helper-simple-access': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/helper-validator-identifier': 7.22.20

  '@babel/helper-optimise-call-expression@7.22.5':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-plugin-utils@7.24.0': {}

  '@babel/helper-replace-supers@7.24.1(@babel/core@7.24.3)':
    dependencies:
      '@babel/core': 7.24.3
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-member-expression-to-functions': 7.23.0
      '@babel/helper-optimise-call-expression': 7.22.5

  '@babel/helper-simple-access@7.22.5':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-skip-transparent-expression-wrappers@7.22.5':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-split-export-declaration@7.22.6':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/helper-string-parser@7.24.1': {}

  '@babel/helper-validator-identifier@7.22.20': {}

  '@babel/helper-validator-option@7.23.5': {}

  '@babel/helpers@7.24.1':
    dependencies:
      '@babel/template': 7.24.0
      '@babel/traverse': 7.24.1
      '@babel/types': 7.24.0
    transitivePeerDependencies:
      - supports-color

  '@babel/highlight@7.24.2':
    dependencies:
      '@babel/helper-validator-identifier': 7.22.20
      chalk: 2.4.2
      js-tokens: 4.0.0
      picocolors: 1.0.0

  '@babel/parser@7.24.1':
    dependencies:
      '@babel/types': 7.24.0

  '@babel/plugin-syntax-jsx@7.24.1(@babel/core@7.24.3)':
    dependencies:
      '@babel/core': 7.24.3
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-syntax-typescript@7.24.1(@babel/core@7.24.3)':
    dependencies:
      '@babel/core': 7.24.3
      '@babel/helper-plugin-utils': 7.24.0

  '@babel/plugin-transform-modules-commonjs@7.24.1(@babel/core@7.24.3)':
    dependencies:
      '@babel/core': 7.24.3
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.24.3)
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/helper-simple-access': 7.22.5

  '@babel/plugin-transform-typescript@7.24.1(@babel/core@7.24.3)':
    dependencies:
      '@babel/core': 7.24.3
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-create-class-features-plugin': 7.24.1(@babel/core@7.24.3)
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/plugin-syntax-typescript': 7.24.1(@babel/core@7.24.3)

  '@babel/preset-typescript@7.24.1(@babel/core@7.24.3)':
    dependencies:
      '@babel/core': 7.24.3
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/helper-validator-option': 7.23.5
      '@babel/plugin-syntax-jsx': 7.24.1(@babel/core@7.24.3)
      '@babel/plugin-transform-modules-commonjs': 7.24.1(@babel/core@7.24.3)
      '@babel/plugin-transform-typescript': 7.24.1(@babel/core@7.24.3)

  '@babel/runtime-corejs2@7.24.7':
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.14.1

  '@babel/runtime@7.24.7':
    dependencies:
      regenerator-runtime: 0.14.1

  '@babel/template@7.24.0':
    dependencies:
      '@babel/code-frame': 7.24.2
      '@babel/parser': 7.24.1
      '@babel/types': 7.24.0

  '@babel/traverse@7.24.1':
    dependencies:
      '@babel/code-frame': 7.24.2
      '@babel/generator': 7.24.1
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-hoist-variables': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/parser': 7.24.1
      '@babel/types': 7.24.0
      debug: 4.3.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.24.0':
    dependencies:
      '@babel/helper-string-parser': 7.24.1
      '@babel/helper-validator-identifier': 7.22.20
      to-fast-properties: 2.0.0

  '@ctrl/tinycolor@3.6.1': {}

  '@element-plus/icons-vue@2.3.1(vue@3.4.21(typescript@5.4.3))':
    dependencies:
      vue: 3.4.21(typescript@5.4.3)

  '@esbuild/aix-ppc64@0.21.5':
    optional: true

  '@esbuild/android-arm64@0.21.5':
    optional: true

  '@esbuild/android-arm@0.21.5':
    optional: true

  '@esbuild/android-x64@0.21.5':
    optional: true

  '@esbuild/darwin-arm64@0.21.5':
    optional: true

  '@esbuild/darwin-x64@0.21.5':
    optional: true

  '@esbuild/freebsd-arm64@0.21.5':
    optional: true

  '@esbuild/freebsd-x64@0.21.5':
    optional: true

  '@esbuild/linux-arm64@0.21.5':
    optional: true

  '@esbuild/linux-arm@0.21.5':
    optional: true

  '@esbuild/linux-ia32@0.21.5':
    optional: true

  '@esbuild/linux-loong64@0.21.5':
    optional: true

  '@esbuild/linux-mips64el@0.21.5':
    optional: true

  '@esbuild/linux-ppc64@0.21.5':
    optional: true

  '@esbuild/linux-riscv64@0.21.5':
    optional: true

  '@esbuild/linux-s390x@0.21.5':
    optional: true

  '@esbuild/linux-x64@0.21.5':
    optional: true

  '@esbuild/netbsd-x64@0.21.5':
    optional: true

  '@esbuild/openbsd-x64@0.21.5':
    optional: true

  '@esbuild/sunos-x64@0.21.5':
    optional: true

  '@esbuild/win32-arm64@0.21.5':
    optional: true

  '@esbuild/win32-ia32@0.21.5':
    optional: true

  '@esbuild/win32-x64@0.21.5':
    optional: true

  '@eslint-community/eslint-utils@4.4.0(eslint@8.57.0)':
    dependencies:
      eslint: 8.57.0
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.10.0': {}

  '@eslint-community/regexpp@4.10.1': {}

  '@eslint/eslintrc@2.1.4':
    dependencies:
      ajv: 6.12.6
      debug: 4.3.5
      espree: 9.6.1
      globals: 13.24.0
      ignore: 5.3.1
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@8.57.0': {}

  '@fast-crud/fast-crud@1.21.2(vue@3.4.21(typescript@5.4.3))':
    dependencies:
      file-saver: 2.0.5
      lru-cache: 10.2.0
      vuedraggable-es: 4.1.1(vue@3.4.21(typescript@5.4.3))
      xlsx: 0.18.5
    transitivePeerDependencies:
      - vue

  '@fast-crud/fast-extends@1.21.2(aws-crt@1.21.2)(vue@3.4.21(typescript@5.4.3))':
    dependencies:
      '@aws-sdk/client-s3': 3.592.0(aws-crt@1.21.2)
      '@aws-sdk/s3-request-presigner': 3.592.0
      '@aws-sdk/signature-v4-crt': 3.587.0
      '@soerenmartius/vue3-clipboard': 0.1.2
      '@wangeditor/editor': 5.1.23
      '@wangeditor/editor-for-vue': 5.1.12(@wangeditor/editor@5.1.23)(vue@3.4.21(typescript@5.4.3))
      ali-oss: 6.20.0
      cos-js-sdk-v5: 1.8.0
      cropperjs: 1.6.2
      dayjs: 1.11.10
      glob: 10.3.10
      humanize-duration: 3.32.1
      jsoneditor: 9.10.5
      lodash-es: 4.17.21
      object-assign: 4.1.1
      qiniu-js: 3.4.1
      quill: 1.3.7
      vue-cropperjs: 5.0.0(vue@3.4.21(typescript@5.4.3))
    transitivePeerDependencies:
      - aws-crt
      - bufferutil
      - debug
      - proxy-agent
      - supports-color
      - utf-8-validate
      - vue

  '@fast-crud/ui-element@1.21.2': {}

  '@fast-crud/ui-interface@1.21.2':
    dependencies:
      lodash-es: 4.17.21

  '@floating-ui/core@1.6.0':
    dependencies:
      '@floating-ui/utils': 0.2.1

  '@floating-ui/dom@1.6.3':
    dependencies:
      '@floating-ui/core': 1.6.0
      '@floating-ui/utils': 0.2.1

  '@floating-ui/utils@0.2.1': {}

  '@httptoolkit/websocket-stream@6.0.1':
    dependencies:
      '@types/ws': 8.5.10
      duplexify: 3.7.1
      inherits: 2.0.4
      isomorphic-ws: 4.0.1(ws@8.16.0)
      readable-stream: 2.3.8
      safe-buffer: 5.2.1
      ws: 8.16.0
      xtend: 4.0.2
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  '@humanwhocodes/config-array@0.11.14':
    dependencies:
      '@humanwhocodes/object-schema': 2.0.3
      debug: 4.3.5
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/object-schema@2.0.3': {}

  '@iconify/types@2.0.0': {}

  '@iconify/utils@2.1.22':
    dependencies:
      '@antfu/install-pkg': 0.1.1
      '@antfu/utils': 0.7.7
      '@iconify/types': 2.0.0
      debug: 4.3.5
      kolorist: 1.8.0
      local-pkg: 0.5.0
      mlly: 1.6.1
    transitivePeerDependencies:
      - supports-color

  '@iconify/vue@4.1.2(vue@3.4.21(typescript@5.4.3))':
    dependencies:
      '@iconify/types': 2.0.0
      vue: 3.4.21(typescript@5.4.3)

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@jest/schemas@29.6.3':
    dependencies:
      '@sinclair/typebox': 0.27.8

  '@jridgewell/gen-mapping@0.3.5':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.4.15
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.4.15': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.4.15

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.17.1

  '@one-ini/wasm@0.1.1': {}

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@polka/url@1.0.0-next.25': {}

  '@popperjs/core@2.11.8': {}

  '@remirror/core-constants@3.0.0': {}

  '@rollup/pluginutils@5.1.0(rollup@4.13.0)':
    dependencies:
      '@types/estree': 1.0.5
      estree-walker: 2.0.2
      picomatch: 2.3.1
    optionalDependencies:
      rollup: 4.13.0

  '@rollup/rollup-android-arm-eabi@4.13.0':
    optional: true

  '@rollup/rollup-android-arm64@4.13.0':
    optional: true

  '@rollup/rollup-darwin-arm64@4.13.0':
    optional: true

  '@rollup/rollup-darwin-x64@4.13.0':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.13.0':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.13.0':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.13.0':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.13.0':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.13.0':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.13.0':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.13.0':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.13.0':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.13.0':
    optional: true

  '@sinclair/typebox@0.27.8': {}

  '@smithy/abort-controller@3.0.0':
    dependencies:
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/chunked-blob-reader-native@3.0.0':
    dependencies:
      '@smithy/util-base64': 3.0.0
      tslib: 2.6.3

  '@smithy/chunked-blob-reader@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/config-resolver@3.0.1':
    dependencies:
      '@smithy/node-config-provider': 3.1.0
      '@smithy/types': 3.0.0
      '@smithy/util-config-provider': 3.0.0
      '@smithy/util-middleware': 3.0.0
      tslib: 2.6.3

  '@smithy/core@2.2.0':
    dependencies:
      '@smithy/middleware-endpoint': 3.0.1
      '@smithy/middleware-retry': 3.0.3
      '@smithy/middleware-serde': 3.0.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/smithy-client': 3.1.1
      '@smithy/types': 3.0.0
      '@smithy/util-middleware': 3.0.0
      tslib: 2.6.3

  '@smithy/credential-provider-imds@3.1.0':
    dependencies:
      '@smithy/node-config-provider': 3.1.0
      '@smithy/property-provider': 3.1.0
      '@smithy/types': 3.0.0
      '@smithy/url-parser': 3.0.0
      tslib: 2.6.3

  '@smithy/eventstream-codec@3.0.0':
    dependencies:
      '@aws-crypto/crc32': 3.0.0
      '@smithy/types': 3.0.0
      '@smithy/util-hex-encoding': 3.0.0
      tslib: 2.6.3

  '@smithy/eventstream-serde-browser@3.0.0':
    dependencies:
      '@smithy/eventstream-serde-universal': 3.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/eventstream-serde-config-resolver@3.0.0':
    dependencies:
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/eventstream-serde-node@3.0.0':
    dependencies:
      '@smithy/eventstream-serde-universal': 3.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/eventstream-serde-universal@3.0.0':
    dependencies:
      '@smithy/eventstream-codec': 3.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/fetch-http-handler@3.0.1':
    dependencies:
      '@smithy/protocol-http': 4.0.0
      '@smithy/querystring-builder': 3.0.0
      '@smithy/types': 3.0.0
      '@smithy/util-base64': 3.0.0
      tslib: 2.6.3

  '@smithy/hash-blob-browser@3.0.0':
    dependencies:
      '@smithy/chunked-blob-reader': 3.0.0
      '@smithy/chunked-blob-reader-native': 3.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/hash-node@3.0.0':
    dependencies:
      '@smithy/types': 3.0.0
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3

  '@smithy/hash-stream-node@3.0.0':
    dependencies:
      '@smithy/types': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3

  '@smithy/invalid-dependency@3.0.0':
    dependencies:
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/is-array-buffer@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/md5-js@3.0.0':
    dependencies:
      '@smithy/types': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3

  '@smithy/middleware-content-length@3.0.0':
    dependencies:
      '@smithy/protocol-http': 4.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/middleware-endpoint@3.0.1':
    dependencies:
      '@smithy/middleware-serde': 3.0.0
      '@smithy/node-config-provider': 3.1.0
      '@smithy/shared-ini-file-loader': 3.1.0
      '@smithy/types': 3.0.0
      '@smithy/url-parser': 3.0.0
      '@smithy/util-middleware': 3.0.0
      tslib: 2.6.3

  '@smithy/middleware-retry@3.0.3':
    dependencies:
      '@smithy/node-config-provider': 3.1.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/service-error-classification': 3.0.0
      '@smithy/smithy-client': 3.1.1
      '@smithy/types': 3.0.0
      '@smithy/util-middleware': 3.0.0
      '@smithy/util-retry': 3.0.0
      tslib: 2.6.3
      uuid: 9.0.1

  '@smithy/middleware-serde@3.0.0':
    dependencies:
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/middleware-stack@3.0.0':
    dependencies:
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/node-config-provider@3.1.0':
    dependencies:
      '@smithy/property-provider': 3.1.0
      '@smithy/shared-ini-file-loader': 3.1.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/node-http-handler@3.0.0':
    dependencies:
      '@smithy/abort-controller': 3.0.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/querystring-builder': 3.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/property-provider@3.1.0':
    dependencies:
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/protocol-http@4.0.0':
    dependencies:
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/querystring-builder@3.0.0':
    dependencies:
      '@smithy/types': 3.0.0
      '@smithy/util-uri-escape': 3.0.0
      tslib: 2.6.3

  '@smithy/querystring-parser@3.0.0':
    dependencies:
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/service-error-classification@3.0.0':
    dependencies:
      '@smithy/types': 3.0.0

  '@smithy/shared-ini-file-loader@3.1.0':
    dependencies:
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/signature-v4@3.0.0':
    dependencies:
      '@smithy/is-array-buffer': 3.0.0
      '@smithy/types': 3.0.0
      '@smithy/util-hex-encoding': 3.0.0
      '@smithy/util-middleware': 3.0.0
      '@smithy/util-uri-escape': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3

  '@smithy/smithy-client@3.1.1':
    dependencies:
      '@smithy/middleware-endpoint': 3.0.1
      '@smithy/middleware-stack': 3.0.0
      '@smithy/protocol-http': 4.0.0
      '@smithy/types': 3.0.0
      '@smithy/util-stream': 3.0.1
      tslib: 2.6.3

  '@smithy/types@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/url-parser@3.0.0':
    dependencies:
      '@smithy/querystring-parser': 3.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/util-base64@3.0.0':
    dependencies:
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3

  '@smithy/util-body-length-browser@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/util-body-length-node@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/util-buffer-from@3.0.0':
    dependencies:
      '@smithy/is-array-buffer': 3.0.0
      tslib: 2.6.3

  '@smithy/util-config-provider@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/util-defaults-mode-browser@3.0.3':
    dependencies:
      '@smithy/property-provider': 3.1.0
      '@smithy/smithy-client': 3.1.1
      '@smithy/types': 3.0.0
      bowser: 2.11.0
      tslib: 2.6.3

  '@smithy/util-defaults-mode-node@3.0.3':
    dependencies:
      '@smithy/config-resolver': 3.0.1
      '@smithy/credential-provider-imds': 3.1.0
      '@smithy/node-config-provider': 3.1.0
      '@smithy/property-provider': 3.1.0
      '@smithy/smithy-client': 3.1.1
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/util-endpoints@2.0.1':
    dependencies:
      '@smithy/node-config-provider': 3.1.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/util-hex-encoding@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/util-middleware@3.0.0':
    dependencies:
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/util-retry@3.0.0':
    dependencies:
      '@smithy/service-error-classification': 3.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@smithy/util-stream@3.0.1':
    dependencies:
      '@smithy/fetch-http-handler': 3.0.1
      '@smithy/node-http-handler': 3.0.0
      '@smithy/types': 3.0.0
      '@smithy/util-base64': 3.0.0
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-hex-encoding': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.6.3

  '@smithy/util-uri-escape@3.0.0':
    dependencies:
      tslib: 2.6.3

  '@smithy/util-utf8@3.0.0':
    dependencies:
      '@smithy/util-buffer-from': 3.0.0
      tslib: 2.6.3

  '@smithy/util-waiter@3.0.0':
    dependencies:
      '@smithy/abort-controller': 3.0.0
      '@smithy/types': 3.0.0
      tslib: 2.6.3

  '@soerenmartius/vue3-clipboard@0.1.2':
    dependencies:
      clipboard: 2.0.11

  '@sphinxxxx/color-conversion@2.2.2': {}

  '@sxzz/popperjs-es@2.11.7': {}

  '@tiptap/core@2.11.5(@tiptap/pm@2.11.5)':
    dependencies:
      '@tiptap/pm': 2.11.5

  '@tiptap/extension-blockquote@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)

  '@tiptap/extension-bold@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)

  '@tiptap/extension-bubble-menu@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)
      '@tiptap/pm': 2.11.5
      tippy.js: 6.3.7

  '@tiptap/extension-bullet-list@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)

  '@tiptap/extension-code-block@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)
      '@tiptap/pm': 2.11.5

  '@tiptap/extension-code@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)

  '@tiptap/extension-document@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)

  '@tiptap/extension-dropcursor@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)
      '@tiptap/pm': 2.11.5

  '@tiptap/extension-floating-menu@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)
      '@tiptap/pm': 2.11.5
      tippy.js: 6.3.7

  '@tiptap/extension-gapcursor@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)
      '@tiptap/pm': 2.11.5

  '@tiptap/extension-hard-break@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)

  '@tiptap/extension-heading@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)

  '@tiptap/extension-history@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)
      '@tiptap/pm': 2.11.5

  '@tiptap/extension-horizontal-rule@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)
      '@tiptap/pm': 2.11.5

  '@tiptap/extension-italic@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)

  '@tiptap/extension-list-item@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)

  '@tiptap/extension-ordered-list@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)

  '@tiptap/extension-paragraph@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)

  '@tiptap/extension-strike@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)

  '@tiptap/extension-text-style@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)

  '@tiptap/extension-text@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)

  '@tiptap/pm@2.11.5':
    dependencies:
      prosemirror-changeset: 2.2.1
      prosemirror-collab: 1.3.1
      prosemirror-commands: 1.7.0
      prosemirror-dropcursor: 1.8.1
      prosemirror-gapcursor: 1.3.2
      prosemirror-history: 1.4.1
      prosemirror-inputrules: 1.4.0
      prosemirror-keymap: 1.2.2
      prosemirror-markdown: 1.13.1
      prosemirror-menu: 1.2.4
      prosemirror-model: 1.24.1
      prosemirror-schema-basic: 1.2.3
      prosemirror-schema-list: 1.5.0
      prosemirror-state: 1.4.3
      prosemirror-tables: 1.6.4
      prosemirror-trailing-node: 3.0.0(prosemirror-model@1.24.1)(prosemirror-state@1.4.3)(prosemirror-view@1.38.0)
      prosemirror-transform: 1.10.2
      prosemirror-view: 1.38.0

  '@tiptap/starter-kit@2.11.5':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)
      '@tiptap/extension-blockquote': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))
      '@tiptap/extension-bold': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))
      '@tiptap/extension-bullet-list': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))
      '@tiptap/extension-code': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))
      '@tiptap/extension-code-block': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)
      '@tiptap/extension-document': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))
      '@tiptap/extension-dropcursor': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)
      '@tiptap/extension-gapcursor': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)
      '@tiptap/extension-hard-break': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))
      '@tiptap/extension-heading': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))
      '@tiptap/extension-history': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)
      '@tiptap/extension-horizontal-rule': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)
      '@tiptap/extension-italic': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))
      '@tiptap/extension-list-item': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))
      '@tiptap/extension-ordered-list': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))
      '@tiptap/extension-paragraph': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))
      '@tiptap/extension-strike': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))
      '@tiptap/extension-text': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))
      '@tiptap/extension-text-style': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))
      '@tiptap/pm': 2.11.5

  '@tiptap/vue-3@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)(vue@3.4.21(typescript@5.4.3))':
    dependencies:
      '@tiptap/core': 2.11.5(@tiptap/pm@2.11.5)
      '@tiptap/extension-bubble-menu': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)
      '@tiptap/extension-floating-menu': 2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)
      '@tiptap/pm': 2.11.5
      vue: 3.4.21(typescript@5.4.3)

  '@transloadit/prettier-bytes@0.0.7': {}

  '@trysound/sax@0.2.0': {}

  '@types/estree@1.0.5': {}

  '@types/event-emitter@0.3.5': {}

  '@types/js-cookie@3.0.6': {}

  '@types/json-schema@7.0.15': {}

  '@types/linkify-it@5.0.0': {}

  '@types/lodash-es@4.17.12':
    dependencies:
      '@types/lodash': 4.17.0

  '@types/lodash@4.17.0': {}

  '@types/markdown-it@14.1.2':
    dependencies:
      '@types/linkify-it': 5.0.0
      '@types/mdurl': 2.0.0

  '@types/mdurl@2.0.0': {}

  '@types/node@20.11.30':
    dependencies:
      undici-types: 5.26.5

  '@types/nprogress@0.2.3': {}

  '@types/path-browserify@1.0.2': {}

  '@types/semver@7.5.8': {}

  '@types/svgo@2.6.4':
    dependencies:
      '@types/node': 20.11.30

  '@types/web-bluetooth@0.0.16': {}

  '@types/web-bluetooth@0.0.20': {}

  '@types/ws@8.5.10':
    dependencies:
      '@types/node': 20.11.30

  '@typescript-eslint/eslint-plugin@7.3.1(@typescript-eslint/parser@7.3.1(eslint@8.57.0)(typescript@5.4.3))(eslint@8.57.0)(typescript@5.4.3)':
    dependencies:
      '@eslint-community/regexpp': 4.10.0
      '@typescript-eslint/parser': 7.3.1(eslint@8.57.0)(typescript@5.4.3)
      '@typescript-eslint/scope-manager': 7.3.1
      '@typescript-eslint/type-utils': 7.3.1(eslint@8.57.0)(typescript@5.4.3)
      '@typescript-eslint/utils': 7.3.1(eslint@8.57.0)(typescript@5.4.3)
      '@typescript-eslint/visitor-keys': 7.3.1
      debug: 4.3.4
      eslint: 8.57.0
      graphemer: 1.4.0
      ignore: 5.3.1
      natural-compare: 1.4.0
      semver: 7.6.0
      ts-api-utils: 1.3.0(typescript@5.4.3)
    optionalDependencies:
      typescript: 5.4.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@7.3.1(eslint@8.57.0)(typescript@5.4.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 7.3.1
      '@typescript-eslint/types': 7.3.1
      '@typescript-eslint/typescript-estree': 7.3.1(typescript@5.4.3)
      '@typescript-eslint/visitor-keys': 7.3.1
      debug: 4.3.4
      eslint: 8.57.0
    optionalDependencies:
      typescript: 5.4.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@7.3.1':
    dependencies:
      '@typescript-eslint/types': 7.3.1
      '@typescript-eslint/visitor-keys': 7.3.1

  '@typescript-eslint/type-utils@7.3.1(eslint@8.57.0)(typescript@5.4.3)':
    dependencies:
      '@typescript-eslint/typescript-estree': 7.3.1(typescript@5.4.3)
      '@typescript-eslint/utils': 7.3.1(eslint@8.57.0)(typescript@5.4.3)
      debug: 4.3.4
      eslint: 8.57.0
      ts-api-utils: 1.3.0(typescript@5.4.3)
    optionalDependencies:
      typescript: 5.4.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@7.3.1': {}

  '@typescript-eslint/typescript-estree@7.3.1(typescript@5.4.3)':
    dependencies:
      '@typescript-eslint/types': 7.3.1
      '@typescript-eslint/visitor-keys': 7.3.1
      debug: 4.3.4
      globby: 11.1.0
      is-glob: 4.0.3
      minimatch: 9.0.3
      semver: 7.6.0
      ts-api-utils: 1.3.0(typescript@5.4.3)
    optionalDependencies:
      typescript: 5.4.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@7.3.1(eslint@8.57.0)(typescript@5.4.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@8.57.0)
      '@types/json-schema': 7.0.15
      '@types/semver': 7.5.8
      '@typescript-eslint/scope-manager': 7.3.1
      '@typescript-eslint/types': 7.3.1
      '@typescript-eslint/typescript-estree': 7.3.1(typescript@5.4.3)
      eslint: 8.57.0
      semver: 7.6.0
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@typescript-eslint/visitor-keys@7.3.1':
    dependencies:
      '@typescript-eslint/types': 7.3.1
      eslint-visitor-keys: 3.4.3

  '@ungap/structured-clone@1.2.0': {}

  '@unocss/astro@0.58.6(rollup@4.13.0)(vite@5.3.4(@types/node@20.11.30)(sass@1.72.0))':
    dependencies:
      '@unocss/core': 0.58.6
      '@unocss/reset': 0.58.6
      '@unocss/vite': 0.58.6(rollup@4.13.0)(vite@5.3.4(@types/node@20.11.30)(sass@1.72.0))
    optionalDependencies:
      vite: 5.3.4(@types/node@20.11.30)(sass@1.72.0)
    transitivePeerDependencies:
      - rollup

  '@unocss/cli@0.58.6(rollup@4.13.0)':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@rollup/pluginutils': 5.1.0(rollup@4.13.0)
      '@unocss/config': 0.58.6
      '@unocss/core': 0.58.6
      '@unocss/preset-uno': 0.58.6
      cac: 6.7.14
      chokidar: 3.6.0
      colorette: 2.0.20
      consola: 3.2.3
      fast-glob: 3.3.2
      magic-string: 0.30.8
      pathe: 1.1.2
      perfect-debounce: 1.0.0
    transitivePeerDependencies:
      - rollup

  '@unocss/config@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6
      unconfig: 0.3.11

  '@unocss/core@0.58.6': {}

  '@unocss/extractor-arbitrary-variants@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6

  '@unocss/inspector@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6
      '@unocss/rule-utils': 0.58.6
      gzip-size: 6.0.0
      sirv: 2.0.4

  '@unocss/postcss@0.58.6(postcss@5.2.18)':
    dependencies:
      '@unocss/config': 0.58.6
      '@unocss/core': 0.58.6
      '@unocss/rule-utils': 0.58.6
      css-tree: 2.3.1
      fast-glob: 3.3.2
      magic-string: 0.30.8
      postcss: 5.2.18

  '@unocss/preset-attributify@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6

  '@unocss/preset-icons@0.58.6':
    dependencies:
      '@iconify/utils': 2.1.22
      '@unocss/core': 0.58.6
      ofetch: 1.3.4
    transitivePeerDependencies:
      - supports-color

  '@unocss/preset-mini@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6
      '@unocss/extractor-arbitrary-variants': 0.58.6
      '@unocss/rule-utils': 0.58.6

  '@unocss/preset-tagify@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6

  '@unocss/preset-typography@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6
      '@unocss/preset-mini': 0.58.6

  '@unocss/preset-uno@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6
      '@unocss/preset-mini': 0.58.6
      '@unocss/preset-wind': 0.58.6
      '@unocss/rule-utils': 0.58.6

  '@unocss/preset-web-fonts@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6
      ofetch: 1.3.4

  '@unocss/preset-wind@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6
      '@unocss/preset-mini': 0.58.6
      '@unocss/rule-utils': 0.58.6

  '@unocss/reset@0.58.6': {}

  '@unocss/rule-utils@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6
      magic-string: 0.30.8

  '@unocss/scope@0.58.6': {}

  '@unocss/transformer-attributify-jsx-babel@0.58.6':
    dependencies:
      '@babel/core': 7.24.3
      '@babel/plugin-syntax-jsx': 7.24.1(@babel/core@7.24.3)
      '@babel/preset-typescript': 7.24.1(@babel/core@7.24.3)
      '@unocss/core': 0.58.6
    transitivePeerDependencies:
      - supports-color

  '@unocss/transformer-attributify-jsx@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6

  '@unocss/transformer-compile-class@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6

  '@unocss/transformer-directives@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6
      '@unocss/rule-utils': 0.58.6
      css-tree: 2.3.1

  '@unocss/transformer-variant-group@0.58.6':
    dependencies:
      '@unocss/core': 0.58.6

  '@unocss/vite@0.58.6(rollup@4.13.0)(vite@5.3.4(@types/node@20.11.30)(sass@1.72.0))':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@rollup/pluginutils': 5.1.0(rollup@4.13.0)
      '@unocss/config': 0.58.6
      '@unocss/core': 0.58.6
      '@unocss/inspector': 0.58.6
      '@unocss/scope': 0.58.6
      '@unocss/transformer-directives': 0.58.6
      chokidar: 3.6.0
      fast-glob: 3.3.2
      magic-string: 0.30.8
      vite: 5.3.4(@types/node@20.11.30)(sass@1.72.0)
    transitivePeerDependencies:
      - rollup

  '@uppy/companion-client@2.2.2':
    dependencies:
      '@uppy/utils': 4.1.3
      namespace-emitter: 2.0.1

  '@uppy/core@2.3.4':
    dependencies:
      '@transloadit/prettier-bytes': 0.0.7
      '@uppy/store-default': 2.1.1
      '@uppy/utils': 4.1.3
      lodash.throttle: 4.1.1
      mime-match: 1.0.2
      namespace-emitter: 2.0.1
      nanoid: 3.3.7
      preact: 10.22.0

  '@uppy/store-default@2.1.1': {}

  '@uppy/utils@4.1.3':
    dependencies:
      lodash.throttle: 4.1.1

  '@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4)':
    dependencies:
      '@uppy/companion-client': 2.2.2
      '@uppy/core': 2.3.4
      '@uppy/utils': 4.1.3
      nanoid: 3.3.7

  '@vitejs/plugin-vue-jsx@3.1.0(vite@5.3.4(@types/node@20.11.30)(sass@1.72.0))(vue@3.4.21(typescript@5.4.3))':
    dependencies:
      '@babel/core': 7.24.3
      '@babel/plugin-transform-typescript': 7.24.1(@babel/core@7.24.3)
      '@vue/babel-plugin-jsx': 1.2.2(@babel/core@7.24.3)
      vite: 5.3.4(@types/node@20.11.30)(sass@1.72.0)
      vue: 3.4.21(typescript@5.4.3)
    transitivePeerDependencies:
      - supports-color

  '@vitejs/plugin-vue@5.0.4(vite@5.3.4(@types/node@20.11.30)(sass@1.72.0))(vue@3.4.21(typescript@5.4.3))':
    dependencies:
      vite: 5.3.4(@types/node@20.11.30)(sass@1.72.0)
      vue: 3.4.21(typescript@5.4.3)

  '@vitest/expect@1.4.0':
    dependencies:
      '@vitest/spy': 1.4.0
      '@vitest/utils': 1.4.0
      chai: 4.4.1

  '@vitest/runner@1.4.0':
    dependencies:
      '@vitest/utils': 1.4.0
      p-limit: 5.0.0
      pathe: 1.1.2

  '@vitest/snapshot@1.4.0':
    dependencies:
      magic-string: 0.30.8
      pathe: 1.1.2
      pretty-format: 29.7.0

  '@vitest/spy@1.4.0':
    dependencies:
      tinyspy: 2.2.1

  '@vitest/utils@1.4.0':
    dependencies:
      diff-sequences: 29.6.3
      estree-walker: 3.0.3
      loupe: 2.3.7
      pretty-format: 29.7.0

  '@volar/language-core@2.1.5':
    dependencies:
      '@volar/source-map': 2.1.5

  '@volar/source-map@2.1.5':
    dependencies:
      muggle-string: 0.4.1

  '@volar/typescript@2.1.5':
    dependencies:
      '@volar/language-core': 2.1.5
      path-browserify: 1.0.1

  '@vue/babel-helper-vue-transform-on@1.2.2': {}

  '@vue/babel-plugin-jsx@1.2.2(@babel/core@7.24.3)':
    dependencies:
      '@babel/helper-module-imports': 7.22.15
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/plugin-syntax-jsx': 7.24.1(@babel/core@7.24.3)
      '@babel/template': 7.24.0
      '@babel/traverse': 7.24.1
      '@babel/types': 7.24.0
      '@vue/babel-helper-vue-transform-on': 1.2.2
      '@vue/babel-plugin-resolve-type': 1.2.2(@babel/core@7.24.3)
      camelcase: 6.3.0
      html-tags: 3.3.1
      svg-tags: 1.0.0
    optionalDependencies:
      '@babel/core': 7.24.3
    transitivePeerDependencies:
      - supports-color

  '@vue/babel-plugin-resolve-type@1.2.2(@babel/core@7.24.3)':
    dependencies:
      '@babel/code-frame': 7.24.2
      '@babel/core': 7.24.3
      '@babel/helper-module-imports': 7.22.15
      '@babel/helper-plugin-utils': 7.24.0
      '@babel/parser': 7.24.1
      '@vue/compiler-sfc': 3.4.21

  '@vue/compiler-core@3.4.21':
    dependencies:
      '@babel/parser': 7.24.1
      '@vue/shared': 3.4.21
      entities: 4.5.0
      estree-walker: 2.0.2
      source-map-js: 1.2.0

  '@vue/compiler-dom@3.4.21':
    dependencies:
      '@vue/compiler-core': 3.4.21
      '@vue/shared': 3.4.21

  '@vue/compiler-sfc@3.4.21':
    dependencies:
      '@babel/parser': 7.24.1
      '@vue/compiler-core': 3.4.21
      '@vue/compiler-dom': 3.4.21
      '@vue/compiler-ssr': 3.4.21
      '@vue/shared': 3.4.21
      estree-walker: 2.0.2
      magic-string: 0.30.8
      postcss: 8.4.38
      source-map-js: 1.2.0

  '@vue/compiler-ssr@3.4.21':
    dependencies:
      '@vue/compiler-dom': 3.4.21
      '@vue/shared': 3.4.21

  '@vue/devtools-api@6.6.1': {}

  '@vue/language-core@2.0.7(typescript@5.4.3)':
    dependencies:
      '@volar/language-core': 2.1.5
      '@vue/compiler-dom': 3.4.21
      '@vue/shared': 3.4.21
      computeds: 0.0.1
      minimatch: 9.0.3
      path-browserify: 1.0.1
      vue-template-compiler: 2.7.16
    optionalDependencies:
      typescript: 5.4.3

  '@vue/reactivity@3.4.21':
    dependencies:
      '@vue/shared': 3.4.21

  '@vue/runtime-core@3.4.21':
    dependencies:
      '@vue/reactivity': 3.4.21
      '@vue/shared': 3.4.21

  '@vue/runtime-dom@3.4.21':
    dependencies:
      '@vue/runtime-core': 3.4.21
      '@vue/shared': 3.4.21
      csstype: 3.1.3

  '@vue/server-renderer@3.4.21(vue@3.4.21(typescript@5.4.3))':
    dependencies:
      '@vue/compiler-ssr': 3.4.21
      '@vue/shared': 3.4.21
      vue: 3.4.21(typescript@5.4.3)

  '@vue/shared@3.4.21': {}

  '@vue/test-utils@2.4.5':
    dependencies:
      js-beautify: 1.15.1
      vue-component-type-helpers: 2.0.7

  '@vueuse/core@10.9.0(vue@3.4.21(typescript@5.4.3))':
    dependencies:
      '@types/web-bluetooth': 0.0.20
      '@vueuse/metadata': 10.9.0
      '@vueuse/shared': 10.9.0(vue@3.4.21(typescript@5.4.3))
      vue-demi: 0.14.7(vue@3.4.21(typescript@5.4.3))
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  '@vueuse/core@9.13.0(vue@3.4.21(typescript@5.4.3))':
    dependencies:
      '@types/web-bluetooth': 0.0.16
      '@vueuse/metadata': 9.13.0
      '@vueuse/shared': 9.13.0(vue@3.4.21(typescript@5.4.3))
      vue-demi: 0.14.7(vue@3.4.21(typescript@5.4.3))
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  '@vueuse/metadata@10.9.0': {}

  '@vueuse/metadata@9.13.0': {}

  '@vueuse/shared@10.9.0(vue@3.4.21(typescript@5.4.3))':
    dependencies:
      vue-demi: 0.14.7(vue@3.4.21(typescript@5.4.3))
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  '@vueuse/shared@9.13.0(vue@3.4.21(typescript@5.4.3))':
    dependencies:
      vue-demi: 0.14.7(vue@3.4.21(typescript@5.4.3))
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  '@wangeditor-next/basic-modules@1.5.29(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.throttle@4.1.1)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)':
    dependencies:
      '@wangeditor-next/core': 1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      dom7: 3.0.0
      is-url: 1.2.4
      lodash.throttle: 4.1.1
      nanoid: 3.3.7
      slate: 0.72.8
      snabbdom: 3.6.2

  '@wangeditor-next/code-highlight@1.3.26(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@4.0.6)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2))(dom7@4.0.6)(slate@0.72.8)(snabbdom@3.6.2)':
    dependencies:
      '@wangeditor-next/core': 1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      dom7: 4.0.6
      prismjs: 1.29.0
      slate: 0.72.8
      snabbdom: 3.6.2

  '@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)':
    dependencies:
      '@types/event-emitter': 0.3.5
      '@uppy/core': 2.3.4
      '@uppy/xhr-upload': 2.1.3(@uppy/core@2.3.4)
      dom7: 3.0.0
      event-emitter: 0.3.5
      html-void-elements: 3.0.0
      i18next: 23.16.8
      is-hotkey: 0.2.0
      lodash.camelcase: 4.3.0
      lodash.clonedeep: 4.5.0
      lodash.debounce: 4.0.8
      lodash.foreach: 4.5.0
      lodash.isequal: 4.5.0
      lodash.throttle: 4.1.1
      lodash.toarray: 4.4.0
      nanoid: 3.3.7
      scroll-into-view-if-needed: 3.1.0
      slate: 0.72.8
      slate-history: 0.109.0(slate@0.72.8)
      snabbdom: 3.6.2

  '@wangeditor-next/editor-for-vue@5.1.14(@wangeditor-next/editor@5.6.27)(vue@3.4.21(typescript@5.4.3))':
    dependencies:
      '@wangeditor-next/editor': 5.6.27
      vue: 3.4.21(typescript@5.4.3)

  '@wangeditor-next/editor@5.6.27':
    dependencies:
      '@uppy/core': 2.3.4
      '@uppy/xhr-upload': 2.1.3(@uppy/core@2.3.4)
      '@wangeditor-next/basic-modules': 1.5.29(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.throttle@4.1.1)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      '@wangeditor-next/code-highlight': 1.3.26(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@4.0.6)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2))(dom7@4.0.6)(slate@0.72.8)(snabbdom@3.6.2)
      '@wangeditor-next/core': 1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      '@wangeditor-next/list-module': 1.1.35(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@4.0.6)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2))(dom7@4.0.6)(slate@0.72.8)(snabbdom@3.6.2)
      '@wangeditor-next/table-module': 1.6.38(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@4.0.6)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2))(dom7@4.0.6)(lodash.debounce@4.0.8)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2)
      '@wangeditor-next/upload-image-module': 1.1.32(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(@wangeditor-next/basic-modules@1.5.29(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@4.0.6)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2))(dom7@4.0.6)(lodash.throttle@4.1.1)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2))(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@4.0.6)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2))(dom7@4.0.6)(lodash.foreach@4.5.0)(slate@0.72.8)(snabbdom@3.6.2)
      '@wangeditor-next/video-module': 1.3.35(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@4.0.6)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2))(dom7@4.0.6)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2)
      dom7: 4.0.6
      is-hotkey: 0.2.0
      lodash.camelcase: 4.3.0
      lodash.clonedeep: 4.5.0
      lodash.debounce: 4.0.8
      lodash.foreach: 4.5.0
      lodash.isequal: 4.5.0
      lodash.throttle: 4.1.1
      lodash.toarray: 4.4.0
      nanoid: 5.0.9
      slate: 0.72.8
      snabbdom: 3.6.2

  '@wangeditor-next/list-module@1.1.35(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@4.0.6)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2))(dom7@4.0.6)(slate@0.72.8)(snabbdom@3.6.2)':
    dependencies:
      '@wangeditor-next/core': 1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      dom7: 4.0.6
      slate: 0.72.8
      snabbdom: 3.6.2

  '@wangeditor-next/table-module@1.6.38(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@4.0.6)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2))(dom7@4.0.6)(lodash.debounce@4.0.8)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2)':
    dependencies:
      '@wangeditor-next/core': 1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      dom7: 4.0.6
      lodash.debounce: 4.0.8
      lodash.isequal: 4.5.0
      lodash.throttle: 4.1.1
      nanoid: 5.0.9
      slate: 0.72.8
      snabbdom: 3.6.2

  '@wangeditor-next/upload-image-module@1.1.32(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(@wangeditor-next/basic-modules@1.5.29(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@4.0.6)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2))(dom7@4.0.6)(lodash.throttle@4.1.1)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2))(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@4.0.6)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2))(dom7@4.0.6)(lodash.foreach@4.5.0)(slate@0.72.8)(snabbdom@3.6.2)':
    dependencies:
      '@uppy/core': 2.3.4
      '@uppy/xhr-upload': 2.1.3(@uppy/core@2.3.4)
      '@wangeditor-next/basic-modules': 1.5.29(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.throttle@4.1.1)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      '@wangeditor-next/core': 1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      dom7: 4.0.6
      lodash.foreach: 4.5.0
      slate: 0.72.8
      snabbdom: 3.6.2

  '@wangeditor-next/video-module@1.3.35(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(@wangeditor-next/core@1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@4.0.6)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2))(dom7@4.0.6)(nanoid@5.0.9)(slate@0.72.8)(snabbdom@3.6.2)':
    dependencies:
      '@uppy/core': 2.3.4
      '@uppy/xhr-upload': 2.1.3(@uppy/core@2.3.4)
      '@wangeditor-next/core': 1.7.29(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      dom7: 4.0.6
      nanoid: 5.0.9
      slate: 0.72.8
      snabbdom: 3.6.2

  '@wangeditor/basic-modules@1.1.7(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.throttle@4.1.1)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)':
    dependencies:
      '@wangeditor/core': 1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      dom7: 3.0.0
      is-url: 1.2.4
      lodash.throttle: 4.1.1
      nanoid: 3.3.7
      slate: 0.72.8
      snabbdom: 3.6.2

  '@wangeditor/code-highlight@1.0.3(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(slate@0.72.8)(snabbdom@3.6.2)':
    dependencies:
      '@wangeditor/core': 1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      dom7: 3.0.0
      prismjs: 1.29.0
      slate: 0.72.8
      snabbdom: 3.6.2

  '@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)':
    dependencies:
      '@types/event-emitter': 0.3.5
      '@uppy/core': 2.3.4
      '@uppy/xhr-upload': 2.1.3(@uppy/core@2.3.4)
      dom7: 3.0.0
      event-emitter: 0.3.5
      html-void-elements: 2.0.1
      i18next: 20.6.1
      is-hotkey: 0.2.0
      lodash.camelcase: 4.3.0
      lodash.clonedeep: 4.5.0
      lodash.debounce: 4.0.8
      lodash.foreach: 4.5.0
      lodash.isequal: 4.5.0
      lodash.throttle: 4.1.1
      lodash.toarray: 4.4.0
      nanoid: 3.3.7
      scroll-into-view-if-needed: 2.2.31
      slate: 0.72.8
      slate-history: 0.66.0(slate@0.72.8)
      snabbdom: 3.6.2

  '@wangeditor/editor-for-vue@5.1.12(@wangeditor/editor@5.1.23)(vue@3.4.21(typescript@5.4.3))':
    dependencies:
      '@wangeditor/editor': 5.1.23
      vue: 3.4.21(typescript@5.4.3)

  '@wangeditor/editor@5.1.23':
    dependencies:
      '@uppy/core': 2.3.4
      '@uppy/xhr-upload': 2.1.3(@uppy/core@2.3.4)
      '@wangeditor/basic-modules': 1.1.7(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.throttle@4.1.1)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      '@wangeditor/code-highlight': 1.0.3(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(slate@0.72.8)(snabbdom@3.6.2)
      '@wangeditor/core': 1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      '@wangeditor/list-module': 1.0.5(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(slate@0.72.8)(snabbdom@3.6.2)
      '@wangeditor/table-module': 1.1.4(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      '@wangeditor/upload-image-module': 1.0.2(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(@wangeditor/basic-modules@1.1.7(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.throttle@4.1.1)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.foreach@4.5.0)(slate@0.72.8)(snabbdom@3.6.2)
      '@wangeditor/video-module': 1.1.4(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      dom7: 3.0.0
      is-hotkey: 0.2.0
      lodash.camelcase: 4.3.0
      lodash.clonedeep: 4.5.0
      lodash.debounce: 4.0.8
      lodash.foreach: 4.5.0
      lodash.isequal: 4.5.0
      lodash.throttle: 4.1.1
      lodash.toarray: 4.4.0
      nanoid: 3.3.7
      slate: 0.72.8
      snabbdom: 3.6.2

  '@wangeditor/list-module@1.0.5(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(slate@0.72.8)(snabbdom@3.6.2)':
    dependencies:
      '@wangeditor/core': 1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      dom7: 3.0.0
      slate: 0.72.8
      snabbdom: 3.6.2

  '@wangeditor/table-module@1.1.4(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)':
    dependencies:
      '@wangeditor/core': 1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      dom7: 3.0.0
      lodash.isequal: 4.5.0
      lodash.throttle: 4.1.1
      nanoid: 3.3.7
      slate: 0.72.8
      snabbdom: 3.6.2

  '@wangeditor/upload-image-module@1.0.2(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(@wangeditor/basic-modules@1.1.7(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.throttle@4.1.1)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.foreach@4.5.0)(slate@0.72.8)(snabbdom@3.6.2)':
    dependencies:
      '@uppy/core': 2.3.4
      '@uppy/xhr-upload': 2.1.3(@uppy/core@2.3.4)
      '@wangeditor/basic-modules': 1.1.7(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.throttle@4.1.1)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      '@wangeditor/core': 1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      dom7: 3.0.0
      lodash.foreach: 4.5.0
      slate: 0.72.8
      snabbdom: 3.6.2

  '@wangeditor/video-module@1.1.4(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)':
    dependencies:
      '@uppy/core': 2.3.4
      '@uppy/xhr-upload': 2.1.3(@uppy/core@2.3.4)
      '@wangeditor/core': 1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)
      dom7: 3.0.0
      nanoid: 3.3.7
      slate: 0.72.8
      snabbdom: 3.6.2

  '@xmldom/xmldom@0.8.10': {}

  abbrev@2.0.0: {}

  ace-builds@1.34.2: {}

  acorn-jsx@5.3.2(acorn@8.11.3):
    dependencies:
      acorn: 8.11.3

  acorn-walk@8.3.2: {}

  acorn@8.11.3: {}

  add@2.0.6: {}

  address@1.2.2: {}

  adler-32@1.3.1: {}

  agent-base@7.1.0:
    dependencies:
      debug: 4.3.5
    transitivePeerDependencies:
      - supports-color

  agentkeepalive@3.5.3:
    dependencies:
      humanize-ms: 1.2.1

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ali-oss@6.20.0:
    dependencies:
      address: 1.2.2
      agentkeepalive: 3.5.3
      bowser: 1.9.4
      copy-to: 2.0.1
      dateformat: 2.2.0
      debug: 4.3.5
      destroy: 1.2.0
      end-or-error: 1.0.1
      get-ready: 1.0.0
      humanize-ms: 1.2.1
      is-type-of: 1.4.0
      js-base64: 2.6.4
      jstoxml: 2.2.9
      lodash: 4.17.21
      merge-descriptors: 1.0.3
      mime: 2.6.0
      platform: 1.3.6
      pump: 3.0.0
      qs: 6.12.1
      sdk-base: 2.0.1
      stream-http: 2.8.2
      stream-wormhole: 1.1.0
      urllib: 2.41.0
      utility: 1.18.0
      xml2js: 0.6.2
    transitivePeerDependencies:
      - proxy-agent
      - supports-color

  ansi-escapes@6.2.1: {}

  ansi-regex@2.1.1: {}

  ansi-regex@5.0.1: {}

  ansi-regex@6.0.1: {}

  ansi-styles@2.2.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  ansi-styles@6.2.1: {}

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  argparse@2.0.1: {}

  arr-diff@4.0.0: {}

  arr-flatten@1.1.0: {}

  arr-union@3.1.0: {}

  array-union@2.1.0: {}

  array-unique@0.3.2: {}

  assertion-error@1.1.0: {}

  assign-symbols@1.0.0: {}

  async-validator@4.2.5: {}

  asynckit@0.4.0: {}

  atob@2.1.2: {}

  aws-crt@1.21.2:
    dependencies:
      '@aws-sdk/util-utf8-browser': 3.259.0
      '@httptoolkit/websocket-stream': 6.0.1
      axios: 1.6.8
      buffer: 6.0.3
      crypto-js: 4.2.0
      mqtt: 4.3.8
      process: 0.11.10
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate

  axios@1.6.8:
    dependencies:
      follow-redirects: 1.15.6
      form-data: 4.0.0
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  balanced-match@1.0.2: {}

  base64-js@1.5.1: {}

  base@0.11.2:
    dependencies:
      cache-base: 1.0.1
      class-utils: 0.3.6
      component-emitter: 1.3.1
      define-property: 1.0.0
      isobject: 3.0.1
      mixin-deep: 1.3.2
      pascalcase: 0.1.1

  big.js@5.2.2: {}

  binary-extensions@2.3.0: {}

  bl@4.1.0:
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2

  bluebird@3.7.2: {}

  boolbase@1.0.0: {}

  bowser@1.9.4: {}

  bowser@2.11.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@2.3.2:
    dependencies:
      arr-flatten: 1.1.0
      array-unique: 0.3.2
      extend-shallow: 2.0.1
      fill-range: 4.0.0
      isobject: 3.0.1
      repeat-element: 1.1.4
      snapdragon: 0.8.2
      snapdragon-node: 2.1.1
      split-string: 3.1.0
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  braces@3.0.2:
    dependencies:
      fill-range: 7.0.1

  browserslist@4.23.0:
    dependencies:
      caniuse-lite: 1.0.30001600
      electron-to-chromium: 1.4.715
      node-releases: 2.0.14
      update-browserslist-db: 1.0.13(browserslist@4.23.0)

  buffer-from@1.1.2: {}

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  builtin-status-codes@3.0.0: {}

  cac@6.7.14: {}

  cache-base@1.0.1:
    dependencies:
      collection-visit: 1.0.0
      component-emitter: 1.3.1
      get-value: 2.0.6
      has-value: 1.0.0
      isobject: 3.0.1
      set-value: 2.0.1
      to-object-path: 0.3.0
      union-value: 1.0.1
      unset-value: 1.0.0

  call-bind@1.0.7:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      set-function-length: 1.2.2

  callsites@3.1.0: {}

  camelcase@6.3.0: {}

  caniuse-lite@1.0.30001600: {}

  cfb@1.2.2:
    dependencies:
      adler-32: 1.3.1
      crc-32: 1.2.2

  chai@4.4.1:
    dependencies:
      assertion-error: 1.1.0
      check-error: 1.0.3
      deep-eql: 4.1.3
      get-func-name: 2.0.2
      loupe: 2.3.7
      pathval: 1.1.1
      type-detect: 4.0.8

  chalk@1.1.3:
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@5.3.0: {}

  check-error@1.0.3:
    dependencies:
      get-func-name: 2.0.2

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  class-utils@0.3.6:
    dependencies:
      arr-union: 3.1.0
      define-property: 0.2.5
      isobject: 3.0.1
      static-extend: 0.1.2

  cli-cursor@4.0.0:
    dependencies:
      restore-cursor: 4.0.0

  cli-truncate@4.0.0:
    dependencies:
      slice-ansi: 5.0.0
      string-width: 7.1.0

  clipboard@2.0.11:
    dependencies:
      good-listener: 1.2.2
      select: 1.1.2
      tiny-emitter: 2.1.0

  clone@2.1.2: {}

  codepage@1.15.0: {}

  collection-visit@1.0.0:
    dependencies:
      map-visit: 1.0.0
      object-visit: 1.0.1

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  colorette@2.0.20: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@10.0.1: {}

  commander@11.1.0: {}

  commander@7.2.0: {}

  commist@1.1.0:
    dependencies:
      leven: 2.1.0
      minimist: 1.2.8

  component-emitter@1.3.1: {}

  compute-scroll-into-view@1.0.20: {}

  compute-scroll-into-view@3.1.0: {}

  computeds@0.0.1: {}

  concat-map@0.0.1: {}

  concat-stream@2.0.0:
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 3.6.2
      typedarray: 0.0.6

  config-chain@1.1.13:
    dependencies:
      ini: 1.3.8
      proto-list: 1.2.4

  consola@3.2.3: {}

  content-type@1.0.5: {}

  convert-source-map@2.0.0: {}

  copy-descriptor@0.1.1: {}

  copy-to@2.0.1: {}

  core-js@2.6.12: {}

  core-util-is@1.0.3: {}

  cors@2.8.5:
    dependencies:
      object-assign: 4.1.1
      vary: 1.1.2

  cos-js-sdk-v5@1.8.0:
    dependencies:
      '@xmldom/xmldom': 0.8.10

  crc-32@1.2.2: {}

  crelt@1.0.6: {}

  cropperjs@1.6.2: {}

  cross-spawn@7.0.3:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crypto-js@4.2.0: {}

  css-select@4.3.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1

  css-select@5.1.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 5.0.3
      domutils: 3.1.0
      nth-check: 2.1.1

  css-tree@1.1.3:
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1

  css-tree@2.2.1:
    dependencies:
      mdn-data: 2.0.28
      source-map-js: 1.2.0

  css-tree@2.3.1:
    dependencies:
      mdn-data: 2.0.30
      source-map-js: 1.2.0

  css-what@6.1.0: {}

  csso@4.2.0:
    dependencies:
      css-tree: 1.1.3

  csso@5.0.5:
    dependencies:
      css-tree: 2.2.1

  cssstyle@4.0.1:
    dependencies:
      rrweb-cssom: 0.6.0

  csstype@3.1.3: {}

  d@1.0.2:
    dependencies:
      es5-ext: 0.10.64
      type: 2.7.3

  data-urls@5.0.0:
    dependencies:
      whatwg-mimetype: 4.0.0
      whatwg-url: 14.0.0

  date-fns-tz@3.2.0(date-fns@4.1.0):
    dependencies:
      date-fns: 4.1.0

  date-fns@4.1.0: {}

  dateformat@2.2.0: {}

  dayjs@1.11.10: {}

  de-indent@1.0.2: {}

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@4.3.4:
    dependencies:
      ms: 2.1.2

  debug@4.3.5:
    dependencies:
      ms: 2.1.2

  decimal.js@10.4.3: {}

  decode-uri-component@0.2.2: {}

  deep-eql@4.1.3:
    dependencies:
      type-detect: 4.0.8

  deep-equal@1.1.2:
    dependencies:
      is-arguments: 1.1.1
      is-date-object: 1.0.5
      is-regex: 1.1.4
      object-is: 1.1.6
      object-keys: 1.1.1
      regexp.prototype.flags: 1.5.2

  deep-is@0.1.4: {}

  default-user-agent@1.0.0:
    dependencies:
      os-name: 1.0.3

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      gopd: 1.0.1

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  define-property@0.2.5:
    dependencies:
      is-descriptor: 0.1.7

  define-property@1.0.0:
    dependencies:
      is-descriptor: 1.0.3

  define-property@2.0.2:
    dependencies:
      is-descriptor: 1.0.3
      isobject: 3.0.1

  defu@6.1.4: {}

  delayed-stream@1.0.0: {}

  delegate@3.2.0: {}

  destr@2.0.3: {}

  destroy@1.2.0: {}

  diff-sequences@29.6.3: {}

  digest-header@1.1.0: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  doctrine@3.0.0:
    dependencies:
      esutils: 2.0.3

  dom-serializer@0.2.2:
    dependencies:
      domelementtype: 2.3.0
      entities: 2.2.0

  dom-serializer@1.4.1:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0

  dom-serializer@2.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0

  dom7@3.0.0:
    dependencies:
      ssr-window: 3.0.0

  dom7@4.0.6:
    dependencies:
      ssr-window: 4.0.2

  domelementtype@1.3.1: {}

  domelementtype@2.3.0: {}

  domhandler@2.4.2:
    dependencies:
      domelementtype: 1.3.1

  domhandler@4.3.1:
    dependencies:
      domelementtype: 2.3.0

  domhandler@5.0.3:
    dependencies:
      domelementtype: 2.3.0

  domutils@1.7.0:
    dependencies:
      dom-serializer: 0.2.2
      domelementtype: 1.3.1

  domutils@2.8.0:
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1

  domutils@3.1.0:
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3

  duplexer@0.1.2: {}

  duplexify@3.7.1:
    dependencies:
      end-of-stream: 1.4.4
      inherits: 2.0.4
      readable-stream: 2.3.8
      stream-shift: 1.0.3

  duplexify@4.1.3:
    dependencies:
      end-of-stream: 1.4.4
      inherits: 2.0.4
      readable-stream: 3.6.2
      stream-shift: 1.0.3

  eastasianwidth@0.2.0: {}

  editorconfig@1.0.4:
    dependencies:
      '@one-ini/wasm': 0.1.1
      commander: 10.0.1
      minimatch: 9.0.1
      semver: 7.6.0

  ee-first@1.1.1: {}

  electron-to-chromium@1.4.715: {}

  element-plus@2.6.2(vue@3.4.21(typescript@5.4.3)):
    dependencies:
      '@ctrl/tinycolor': 3.6.1
      '@element-plus/icons-vue': 2.3.1(vue@3.4.21(typescript@5.4.3))
      '@floating-ui/dom': 1.6.3
      '@popperjs/core': '@sxzz/popperjs-es@2.11.7'
      '@types/lodash': 4.17.0
      '@types/lodash-es': 4.17.12
      '@vueuse/core': 9.13.0(vue@3.4.21(typescript@5.4.3))
      async-validator: 4.2.5
      dayjs: 1.11.10
      escape-html: 1.0.3
      lodash: 4.17.21
      lodash-es: 4.17.21
      lodash-unified: 1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21)
      memoize-one: 6.0.0
      normalize-wheel-es: 1.2.0
      vue: 3.4.21(typescript@5.4.3)
    transitivePeerDependencies:
      - '@vue/composition-api'

  emoji-regex@10.3.0: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  emojis-list@3.0.0: {}

  end-of-stream@1.4.4:
    dependencies:
      once: 1.4.0

  end-or-error@1.0.1: {}

  entities@1.1.2: {}

  entities@2.2.0: {}

  entities@4.5.0: {}

  es-define-property@1.0.0:
    dependencies:
      get-intrinsic: 1.2.4

  es-errors@1.3.0: {}

  es5-ext@0.10.64:
    dependencies:
      es6-iterator: 2.0.3
      es6-symbol: 3.1.4
      esniff: 2.0.1
      next-tick: 1.1.0

  es6-iterator@2.0.3:
    dependencies:
      d: 1.0.2
      es5-ext: 0.10.64
      es6-symbol: 3.1.4

  es6-symbol@3.1.4:
    dependencies:
      d: 1.0.2
      ext: 1.7.0

  esbuild@0.21.5:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.21.5
      '@esbuild/android-arm': 0.21.5
      '@esbuild/android-arm64': 0.21.5
      '@esbuild/android-x64': 0.21.5
      '@esbuild/darwin-arm64': 0.21.5
      '@esbuild/darwin-x64': 0.21.5
      '@esbuild/freebsd-arm64': 0.21.5
      '@esbuild/freebsd-x64': 0.21.5
      '@esbuild/linux-arm': 0.21.5
      '@esbuild/linux-arm64': 0.21.5
      '@esbuild/linux-ia32': 0.21.5
      '@esbuild/linux-loong64': 0.21.5
      '@esbuild/linux-mips64el': 0.21.5
      '@esbuild/linux-ppc64': 0.21.5
      '@esbuild/linux-riscv64': 0.21.5
      '@esbuild/linux-s390x': 0.21.5
      '@esbuild/linux-x64': 0.21.5
      '@esbuild/netbsd-x64': 0.21.5
      '@esbuild/openbsd-x64': 0.21.5
      '@esbuild/sunos-x64': 0.21.5
      '@esbuild/win32-arm64': 0.21.5
      '@esbuild/win32-ia32': 0.21.5
      '@esbuild/win32-x64': 0.21.5

  escalade@3.1.2: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@4.0.0: {}

  eslint-scope@7.2.2:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint@8.57.0:
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@8.57.0)
      '@eslint-community/regexpp': 4.10.1
      '@eslint/eslintrc': 2.1.4
      '@eslint/js': 8.57.0
      '@humanwhocodes/config-array': 0.11.14
      '@humanwhocodes/module-importer': 1.0.1
      '@nodelib/fs.walk': 1.2.8
      '@ungap/structured-clone': 1.2.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.3
      debug: 4.3.5
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.5.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      find-up: 5.0.0
      glob-parent: 6.0.2
      globals: 13.24.0
      graphemer: 1.4.0
      ignore: 5.3.1
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      is-path-inside: 3.0.3
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      strip-ansi: 6.0.1
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  esniff@2.0.1:
    dependencies:
      d: 1.0.2
      es5-ext: 0.10.64
      event-emitter: 0.3.5
      type: 2.7.3

  espree@9.6.1:
    dependencies:
      acorn: 8.11.3
      acorn-jsx: 5.3.2(acorn@8.11.3)
      eslint-visitor-keys: 3.4.3

  esquery@1.5.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@5.3.0: {}

  estree-walker@2.0.2: {}

  estree-walker@3.0.3:
    dependencies:
      '@types/estree': 1.0.5

  esutils@2.0.3: {}

  etag@1.8.1: {}

  event-emitter@0.3.5:
    dependencies:
      d: 1.0.2
      es5-ext: 0.10.64

  eventemitter3@2.0.3: {}

  eventemitter3@5.0.1: {}

  execa@5.1.1:
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  execa@8.0.1:
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 8.0.1
      human-signals: 5.0.0
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.3.0
      onetime: 6.0.0
      signal-exit: 4.1.0
      strip-final-newline: 3.0.0

  expand-brackets@2.1.4:
    dependencies:
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      posix-character-classes: 0.1.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  ext@1.7.0:
    dependencies:
      type: 2.7.3

  extend-shallow@2.0.1:
    dependencies:
      is-extendable: 0.1.1

  extend-shallow@3.0.2:
    dependencies:
      assign-symbols: 1.0.0
      is-extendable: 1.0.1

  extend@3.0.2: {}

  extglob@2.0.4:
    dependencies:
      array-unique: 0.3.2
      define-property: 1.0.0
      expand-brackets: 2.1.4
      extend-shallow: 2.0.1
      fragment-cache: 0.2.1
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  fast-deep-equal@3.1.3: {}

  fast-diff@1.1.2: {}

  fast-glob@3.3.2:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.5

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-xml-parser@4.2.5:
    dependencies:
      strnum: 1.0.5

  fastq@1.17.1:
    dependencies:
      reusify: 1.0.4

  file-entry-cache@6.0.1:
    dependencies:
      flat-cache: 3.2.0

  file-saver@2.0.5: {}

  fill-range@4.0.0:
    dependencies:
      extend-shallow: 2.0.1
      is-number: 3.0.0
      repeat-string: 1.6.1
      to-regex-range: 2.1.1

  fill-range@7.0.1:
    dependencies:
      to-regex-range: 5.0.1

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@3.2.0:
    dependencies:
      flatted: 3.3.1
      keyv: 4.5.4
      rimraf: 3.0.2

  flatted@3.3.1: {}

  follow-redirects@1.15.6: {}

  for-in@1.0.2: {}

  foreground-child@3.1.1:
    dependencies:
      cross-spawn: 7.0.3
      signal-exit: 4.1.0

  form-data@4.0.0:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  formstream@1.5.1:
    dependencies:
      destroy: 1.2.0
      mime: 2.6.0
      node-hex: 1.0.1
      pause-stream: 0.0.11

  frac@1.1.2: {}

  fragment-cache@0.2.1:
    dependencies:
      map-cache: 0.2.2

  fs-extra@10.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  functions-have-names@1.2.3: {}

  gensync@1.0.0-beta.2: {}

  get-east-asian-width@1.2.0: {}

  get-func-name@2.0.2: {}

  get-intrinsic@1.2.4:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2

  get-ready@1.0.0: {}

  get-stream@6.0.1: {}

  get-stream@8.0.1: {}

  get-value@2.0.6: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob@10.3.10:
    dependencies:
      foreground-child: 3.1.1
      jackspeak: 2.3.6
      minimatch: 9.0.3
      minipass: 7.0.4
      path-scurry: 1.10.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@11.12.0: {}

  globals@13.24.0:
    dependencies:
      type-fest: 0.20.2

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.2
      ignore: 5.3.1
      merge2: 1.4.1
      slash: 3.0.0

  good-listener@1.2.2:
    dependencies:
      delegate: 3.2.0

  gopd@1.0.1:
    dependencies:
      get-intrinsic: 1.2.4

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  gzip-size@6.0.0:
    dependencies:
      duplexer: 0.1.2

  has-ansi@2.0.0:
    dependencies:
      ansi-regex: 2.1.1

  has-flag@1.0.0: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.0

  has-proto@1.0.3: {}

  has-symbols@1.0.3: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.0.3

  has-value@0.3.1:
    dependencies:
      get-value: 2.0.6
      has-values: 0.1.4
      isobject: 2.1.0

  has-value@1.0.0:
    dependencies:
      get-value: 2.0.6
      has-values: 1.0.0
      isobject: 3.0.1

  has-values@0.1.4: {}

  has-values@1.0.0:
    dependencies:
      is-number: 3.0.0
      kind-of: 4.0.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  help-me@3.0.0:
    dependencies:
      glob: 7.2.3
      readable-stream: 3.6.2

  html-encoding-sniffer@4.0.0:
    dependencies:
      whatwg-encoding: 3.1.1

  html-tags@3.3.1: {}

  html-void-elements@2.0.1: {}

  html-void-elements@3.0.0: {}

  htmlparser2@3.10.1:
    dependencies:
      domelementtype: 1.3.1
      domhandler: 2.4.2
      domutils: 1.7.0
      entities: 1.1.2
      inherits: 2.0.4
      readable-stream: 3.6.2

  http-proxy-agent@7.0.2:
    dependencies:
      agent-base: 7.1.0
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@7.0.4:
    dependencies:
      agent-base: 7.1.0
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  human-signals@2.1.0: {}

  human-signals@5.0.0: {}

  humanize-duration@3.32.1: {}

  humanize-ms@1.2.1:
    dependencies:
      ms: 2.1.2

  i18next@20.6.1:
    dependencies:
      '@babel/runtime': 7.24.7

  i18next@23.16.8:
    dependencies:
      '@babel/runtime': 7.24.7

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  ieee754@1.2.1: {}

  ignore@5.3.1: {}

  image-size@0.5.5: {}

  immer@9.0.21: {}

  immutable@4.3.5: {}

  import-fresh@3.3.0:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  imurmurhash@0.1.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  ini@1.3.8: {}

  ip@1.1.9: {}

  is-accessor-descriptor@1.0.1:
    dependencies:
      hasown: 2.0.2

  is-arguments@1.1.1:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-buffer@1.1.6: {}

  is-class-hotfix@0.0.6: {}

  is-data-descriptor@1.0.1:
    dependencies:
      hasown: 2.0.2

  is-date-object@1.0.5:
    dependencies:
      has-tostringtag: 1.0.2

  is-descriptor@0.1.7:
    dependencies:
      is-accessor-descriptor: 1.0.1
      is-data-descriptor: 1.0.1

  is-descriptor@1.0.3:
    dependencies:
      is-accessor-descriptor: 1.0.1
      is-data-descriptor: 1.0.1

  is-extendable@0.1.1: {}

  is-extendable@1.0.1:
    dependencies:
      is-plain-object: 2.0.4

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-fullwidth-code-point@4.0.0: {}

  is-fullwidth-code-point@5.0.0:
    dependencies:
      get-east-asian-width: 1.2.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-hotkey@0.2.0: {}

  is-number@3.0.0:
    dependencies:
      kind-of: 3.2.2

  is-number@7.0.0: {}

  is-path-inside@3.0.3: {}

  is-plain-obj@1.1.0: {}

  is-plain-object@2.0.4:
    dependencies:
      isobject: 3.0.1

  is-plain-object@5.0.0: {}

  is-potential-custom-element-name@1.0.1: {}

  is-regex@1.1.4:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-stream@2.0.1: {}

  is-stream@3.0.0: {}

  is-type-of@1.4.0:
    dependencies:
      core-util-is: 1.0.3
      is-class-hotfix: 0.0.6
      isstream: 0.1.2

  is-url@1.2.4: {}

  is-windows@1.0.2: {}

  isarray@1.0.0: {}

  isexe@2.0.0: {}

  isobject@2.1.0:
    dependencies:
      isarray: 1.0.0

  isobject@3.0.1: {}

  isomorphic-ws@4.0.1(ws@8.16.0):
    dependencies:
      ws: 8.16.0

  isstream@0.1.2: {}

  jackspeak@2.3.6:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  javascript-natural-sort@0.7.1: {}

  jiti@1.21.0: {}

  jmespath@0.16.0: {}

  js-base64@2.6.4: {}

  js-beautify@1.15.1:
    dependencies:
      config-chain: 1.1.13
      editorconfig: 1.0.4
      glob: 10.3.10
      js-cookie: 3.0.5
      nopt: 7.2.0

  js-cookie@3.0.5: {}

  js-sdsl@4.3.0: {}

  js-tokens@4.0.0: {}

  js-tokens@8.0.3: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsdom@24.0.0:
    dependencies:
      cssstyle: 4.0.1
      data-urls: 5.0.0
      decimal.js: 10.4.3
      form-data: 4.0.0
      html-encoding-sniffer: 4.0.0
      http-proxy-agent: 7.0.2
      https-proxy-agent: 7.0.4
      is-potential-custom-element-name: 1.0.1
      nwsapi: 2.2.7
      parse5: 7.1.2
      rrweb-cssom: 0.6.0
      saxes: 6.0.0
      symbol-tree: 3.2.4
      tough-cookie: 4.1.3
      w3c-xmlserializer: 5.0.0
      webidl-conversions: 7.0.0
      whatwg-encoding: 3.1.1
      whatwg-mimetype: 4.0.0
      whatwg-url: 14.0.0
      ws: 8.16.0
      xml-name-validator: 5.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jsesc@2.5.2: {}

  json-buffer@3.0.1: {}

  json-schema-traverse@0.4.1: {}

  json-source-map@0.6.1: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@1.0.2:
    dependencies:
      minimist: 1.2.8

  json5@2.2.3: {}

  jsonc-parser@3.2.1: {}

  jsoneditor@9.10.5:
    dependencies:
      ace-builds: 1.34.2
      ajv: 6.12.6
      javascript-natural-sort: 0.7.1
      jmespath: 0.16.0
      json-source-map: 0.6.1
      jsonrepair: 3.1.0
      mobius1-selectr: 2.4.13
      picomodal: 3.0.0
      vanilla-picker: 2.12.3

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonrepair@3.1.0: {}

  jstoxml@2.2.9: {}

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  kind-of@3.2.2:
    dependencies:
      is-buffer: 1.1.6

  kind-of@4.0.0:
    dependencies:
      is-buffer: 1.1.6

  kind-of@5.1.0: {}

  kind-of@6.0.3: {}

  kolorist@1.8.0: {}

  leven@2.1.0: {}

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lilconfig@3.0.0: {}

  linkify-it@5.0.0:
    dependencies:
      uc.micro: 2.1.0

  lint-staged@15.2.2:
    dependencies:
      chalk: 5.3.0
      commander: 11.1.0
      debug: 4.3.4
      execa: 8.0.1
      lilconfig: 3.0.0
      listr2: 8.0.1
      micromatch: 4.0.5
      pidtree: 0.6.0
      string-argv: 0.3.2
      yaml: 2.3.4
    transitivePeerDependencies:
      - supports-color

  listr2@8.0.1:
    dependencies:
      cli-truncate: 4.0.0
      colorette: 2.0.20
      eventemitter3: 5.0.1
      log-update: 6.0.0
      rfdc: 1.3.1
      wrap-ansi: 9.0.0

  loader-utils@1.4.2:
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 1.0.2

  local-pkg@0.5.0:
    dependencies:
      mlly: 1.6.1
      pkg-types: 1.0.3

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash-es@4.17.21: {}

  lodash-unified@1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21):
    dependencies:
      '@types/lodash-es': 4.17.12
      lodash: 4.17.21
      lodash-es: 4.17.21

  lodash.camelcase@4.3.0: {}

  lodash.clonedeep@4.5.0: {}

  lodash.debounce@4.0.8: {}

  lodash.foreach@4.5.0: {}

  lodash.isequal@4.5.0: {}

  lodash.merge@4.6.2: {}

  lodash.throttle@4.1.1: {}

  lodash.toarray@4.4.0: {}

  lodash@4.17.21: {}

  log-update@6.0.0:
    dependencies:
      ansi-escapes: 6.2.1
      cli-cursor: 4.0.0
      slice-ansi: 7.1.0
      strip-ansi: 7.1.0
      wrap-ansi: 9.0.0

  loupe@2.3.7:
    dependencies:
      get-func-name: 2.0.2

  lru-cache@10.2.0: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lru-cache@6.0.0:
    dependencies:
      yallist: 4.0.0

  magic-string@0.30.8:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.4.15

  map-cache@0.2.2: {}

  map-visit@1.0.0:
    dependencies:
      object-visit: 1.0.1

  markdown-it@14.1.0:
    dependencies:
      argparse: 2.0.1
      entities: 4.5.0
      linkify-it: 5.0.0
      mdurl: 2.0.0
      punycode.js: 2.3.1
      uc.micro: 2.1.0

  mdn-data@2.0.14: {}

  mdn-data@2.0.28: {}

  mdn-data@2.0.30: {}

  mdurl@2.0.0: {}

  memoize-one@6.0.0: {}

  merge-descriptors@1.0.3: {}

  merge-options@1.0.1:
    dependencies:
      is-plain-obj: 1.1.0

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  micromatch@3.1.0:
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      braces: 2.3.2
      define-property: 1.0.0
      extend-shallow: 2.0.1
      extglob: 2.0.4
      fragment-cache: 0.2.1
      kind-of: 5.1.0
      nanomatch: 1.2.13
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  micromatch@4.0.5:
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-match@1.0.2:
    dependencies:
      wildcard: 1.1.2

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@2.6.0: {}

  mimic-fn@2.1.0: {}

  mimic-fn@4.0.0: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@9.0.1:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@9.0.3:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass@7.0.4: {}

  mitt@3.0.1: {}

  mixin-deep@1.3.2:
    dependencies:
      for-in: 1.0.2
      is-extendable: 1.0.1

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  mlly@1.6.1:
    dependencies:
      acorn: 8.11.3
      pathe: 1.1.2
      pkg-types: 1.0.3
      ufo: 1.5.3

  mobius1-selectr@2.4.13: {}

  mqtt-packet@6.10.0:
    dependencies:
      bl: 4.1.0
      debug: 4.3.5
      process-nextick-args: 2.0.1
    transitivePeerDependencies:
      - supports-color

  mqtt@4.3.8:
    dependencies:
      commist: 1.1.0
      concat-stream: 2.0.0
      debug: 4.3.5
      duplexify: 4.1.3
      help-me: 3.0.0
      inherits: 2.0.4
      lru-cache: 6.0.0
      minimist: 1.2.8
      mqtt-packet: 6.10.0
      number-allocator: 1.0.14
      pump: 3.0.0
      readable-stream: 3.6.2
      reinterval: 1.1.0
      rfdc: 1.3.1
      split2: 3.2.2
      ws: 7.5.9
      xtend: 4.0.2
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  mrmime@2.0.0: {}

  ms@2.0.0: {}

  ms@2.1.2: {}

  muggle-string@0.4.1: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  namespace-emitter@2.0.1: {}

  nanoid@3.3.7: {}

  nanoid@5.0.9: {}

  nanomatch@1.2.13:
    dependencies:
      arr-diff: 4.0.0
      array-unique: 0.3.2
      define-property: 2.0.2
      extend-shallow: 3.0.2
      fragment-cache: 0.2.1
      is-windows: 1.0.2
      kind-of: 6.0.3
      object.pick: 1.3.0
      regex-not: 1.0.2
      snapdragon: 0.8.2
      to-regex: 3.0.2
    transitivePeerDependencies:
      - supports-color

  natural-compare@1.4.0: {}

  next-tick@1.1.0: {}

  node-fetch-native@1.6.4: {}

  node-hex@1.0.1: {}

  node-releases@2.0.14: {}

  nopt@7.2.0:
    dependencies:
      abbrev: 2.0.0

  normalize-path@3.0.0: {}

  normalize-wheel-es@1.2.0: {}

  normalize.css@8.0.1: {}

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  npm-run-path@5.3.0:
    dependencies:
      path-key: 4.0.0

  nprogress@0.2.0: {}

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  number-allocator@1.0.14:
    dependencies:
      debug: 4.3.5
      js-sdsl: 4.3.0
    transitivePeerDependencies:
      - supports-color

  nwsapi@2.2.7: {}

  object-assign@4.1.1: {}

  object-copy@0.1.0:
    dependencies:
      copy-descriptor: 0.1.1
      define-property: 0.2.5
      kind-of: 3.2.2

  object-inspect@1.13.1: {}

  object-is@1.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1

  object-keys@1.1.1: {}

  object-visit@1.0.1:
    dependencies:
      isobject: 3.0.1

  object.pick@1.3.0:
    dependencies:
      isobject: 3.0.1

  ofetch@1.3.4:
    dependencies:
      destr: 2.0.3
      node-fetch-native: 1.6.4
      ufo: 1.5.3

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  onetime@6.0.0:
    dependencies:
      mimic-fn: 4.0.0

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  orderedmap@2.1.1: {}

  os-name@1.0.3:
    dependencies:
      osx-release: 1.1.0
      win-release: 1.1.1

  osx-release@1.1.0:
    dependencies:
      minimist: 1.2.8

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-limit@5.0.0:
    dependencies:
      yocto-queue: 1.0.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  parchment@1.1.4: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse5@7.1.2:
    dependencies:
      entities: 4.5.0

  pascalcase@0.1.1: {}

  path-browserify@1.0.1: {}

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-key@4.0.0: {}

  path-scurry@1.10.1:
    dependencies:
      lru-cache: 10.2.0
      minipass: 7.0.4

  path-to-regexp@6.2.1: {}

  path-type@4.0.0: {}

  pathe@0.2.0: {}

  pathe@1.1.2: {}

  pathval@1.1.1: {}

  pause-stream@0.0.11:
    dependencies:
      through: 2.3.8

  perfect-debounce@1.0.0: {}

  picocolors@1.0.0: {}

  picocolors@1.0.1: {}

  picomatch@2.3.1: {}

  picomodal@3.0.0: {}

  pidtree@0.6.0: {}

  pinia@2.1.7(typescript@5.4.3)(vue@3.4.21(typescript@5.4.3)):
    dependencies:
      '@vue/devtools-api': 6.6.1
      vue: 3.4.21(typescript@5.4.3)
      vue-demi: 0.14.7(vue@3.4.21(typescript@5.4.3))
    optionalDependencies:
      typescript: 5.4.3

  pinyin-pro@3.26.0: {}

  pkg-types@1.0.3:
    dependencies:
      jsonc-parser: 3.2.1
      mlly: 1.6.1
      pathe: 1.1.2

  platform@1.3.6: {}

  posix-character-classes@0.1.1: {}

  postcss-prefix-selector@1.16.0(postcss@5.2.18):
    dependencies:
      postcss: 5.2.18

  postcss@5.2.18:
    dependencies:
      chalk: 1.1.3
      js-base64: 2.6.4
      source-map: 0.5.7
      supports-color: 3.2.3

  postcss@8.4.38:
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.0
      source-map-js: 1.2.0

  postcss@8.4.39:
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.1
      source-map-js: 1.2.0

  posthtml-parser@0.2.1:
    dependencies:
      htmlparser2: 3.10.1
      isobject: 2.1.0

  posthtml-rename-id@1.0.12:
    dependencies:
      escape-string-regexp: 1.0.5

  posthtml-render@1.4.0: {}

  posthtml-svg-mode@1.0.3:
    dependencies:
      merge-options: 1.0.1
      posthtml: 0.9.2
      posthtml-parser: 0.2.1
      posthtml-render: 1.4.0

  posthtml@0.9.2:
    dependencies:
      posthtml-parser: 0.2.1
      posthtml-render: 1.4.0

  preact@10.22.0: {}

  prelude-ls@1.2.1: {}

  prettier@3.2.5: {}

  pretty-format@29.7.0:
    dependencies:
      '@jest/schemas': 29.6.3
      ansi-styles: 5.2.0
      react-is: 18.2.0

  prismjs@1.29.0: {}

  process-nextick-args@2.0.1: {}

  process@0.11.10: {}

  prosemirror-changeset@2.2.1:
    dependencies:
      prosemirror-transform: 1.10.2

  prosemirror-collab@1.3.1:
    dependencies:
      prosemirror-state: 1.4.3

  prosemirror-commands@1.7.0:
    dependencies:
      prosemirror-model: 1.24.1
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.2

  prosemirror-dropcursor@1.8.1:
    dependencies:
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.2
      prosemirror-view: 1.38.0

  prosemirror-gapcursor@1.3.2:
    dependencies:
      prosemirror-keymap: 1.2.2
      prosemirror-model: 1.24.1
      prosemirror-state: 1.4.3
      prosemirror-view: 1.38.0

  prosemirror-history@1.4.1:
    dependencies:
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.2
      prosemirror-view: 1.38.0
      rope-sequence: 1.3.4

  prosemirror-inputrules@1.4.0:
    dependencies:
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.2

  prosemirror-keymap@1.2.2:
    dependencies:
      prosemirror-state: 1.4.3
      w3c-keyname: 2.2.8

  prosemirror-markdown@1.13.1:
    dependencies:
      '@types/markdown-it': 14.1.2
      markdown-it: 14.1.0
      prosemirror-model: 1.24.1

  prosemirror-menu@1.2.4:
    dependencies:
      crelt: 1.0.6
      prosemirror-commands: 1.7.0
      prosemirror-history: 1.4.1
      prosemirror-state: 1.4.3

  prosemirror-model@1.24.1:
    dependencies:
      orderedmap: 2.1.1

  prosemirror-schema-basic@1.2.3:
    dependencies:
      prosemirror-model: 1.24.1

  prosemirror-schema-list@1.5.0:
    dependencies:
      prosemirror-model: 1.24.1
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.2

  prosemirror-state@1.4.3:
    dependencies:
      prosemirror-model: 1.24.1
      prosemirror-transform: 1.10.2
      prosemirror-view: 1.38.0

  prosemirror-tables@1.6.4:
    dependencies:
      prosemirror-keymap: 1.2.2
      prosemirror-model: 1.24.1
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.2
      prosemirror-view: 1.38.0

  prosemirror-trailing-node@3.0.0(prosemirror-model@1.24.1)(prosemirror-state@1.4.3)(prosemirror-view@1.38.0):
    dependencies:
      '@remirror/core-constants': 3.0.0
      escape-string-regexp: 4.0.0
      prosemirror-model: 1.24.1
      prosemirror-state: 1.4.3
      prosemirror-view: 1.38.0

  prosemirror-transform@1.10.2:
    dependencies:
      prosemirror-model: 1.24.1

  prosemirror-view@1.38.0:
    dependencies:
      prosemirror-model: 1.24.1
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.2

  proto-list@1.2.4: {}

  proxy-from-env@1.1.0: {}

  psl@1.9.0: {}

  pump@3.0.0:
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  punycode.js@2.3.1: {}

  punycode@2.3.1: {}

  qiniu-js@3.4.1:
    dependencies:
      '@babel/runtime-corejs2': 7.24.7
      querystring: 0.2.1
      spark-md5: 3.0.2

  qs@6.12.1:
    dependencies:
      side-channel: 1.0.6

  query-string@4.3.4:
    dependencies:
      object-assign: 4.1.1
      strict-uri-encode: 1.1.0

  querystring@0.2.1: {}

  querystringify@2.2.0: {}

  queue-microtask@1.2.3: {}

  quill-delta@3.6.3:
    dependencies:
      deep-equal: 1.1.2
      extend: 3.0.2
      fast-diff: 1.1.2

  quill@1.3.7:
    dependencies:
      clone: 2.1.2
      deep-equal: 1.1.2
      eventemitter3: 2.0.3
      extend: 3.0.2
      parchment: 1.1.4
      quill-delta: 3.6.3

  react-is@18.2.0: {}

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  regenerator-runtime@0.14.1: {}

  regex-not@1.0.2:
    dependencies:
      extend-shallow: 3.0.2
      safe-regex: 1.1.0

  regexp.prototype.flags@1.5.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-errors: 1.3.0
      set-function-name: 2.0.2

  reinterval@1.1.0: {}

  repeat-element@1.1.4: {}

  repeat-string@1.6.1: {}

  requires-port@1.0.0: {}

  resolve-from@4.0.0: {}

  resolve-url@0.2.1: {}

  restore-cursor@4.0.0:
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7

  ret@0.1.15: {}

  reusify@1.0.4: {}

  rfdc@1.3.1: {}

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  rollup@4.13.0:
    dependencies:
      '@types/estree': 1.0.5
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.13.0
      '@rollup/rollup-android-arm64': 4.13.0
      '@rollup/rollup-darwin-arm64': 4.13.0
      '@rollup/rollup-darwin-x64': 4.13.0
      '@rollup/rollup-linux-arm-gnueabihf': 4.13.0
      '@rollup/rollup-linux-arm64-gnu': 4.13.0
      '@rollup/rollup-linux-arm64-musl': 4.13.0
      '@rollup/rollup-linux-riscv64-gnu': 4.13.0
      '@rollup/rollup-linux-x64-gnu': 4.13.0
      '@rollup/rollup-linux-x64-musl': 4.13.0
      '@rollup/rollup-win32-arm64-msvc': 4.13.0
      '@rollup/rollup-win32-ia32-msvc': 4.13.0
      '@rollup/rollup-win32-x64-msvc': 4.13.0
      fsevents: 2.3.3

  rope-sequence@1.3.4: {}

  rrweb-cssom@0.6.0: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safe-regex@1.1.0:
    dependencies:
      ret: 0.1.15

  safer-buffer@2.1.2: {}

  sass@1.72.0:
    dependencies:
      chokidar: 3.6.0
      immutable: 4.3.5
      source-map-js: 1.2.0

  sax@1.4.1: {}

  saxes@6.0.0:
    dependencies:
      xmlchars: 2.2.0

  screenfull@6.0.2: {}

  scroll-into-view-if-needed@2.2.31:
    dependencies:
      compute-scroll-into-view: 1.0.20

  scroll-into-view-if-needed@3.1.0:
    dependencies:
      compute-scroll-into-view: 3.1.0

  sdk-base@2.0.1:
    dependencies:
      get-ready: 1.0.0

  select@1.1.2: {}

  semver@5.7.2: {}

  semver@6.3.1: {}

  semver@7.6.0:
    dependencies:
      lru-cache: 6.0.0

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-value@2.0.1:
    dependencies:
      extend-shallow: 2.0.1
      is-extendable: 0.1.1
      is-plain-object: 2.0.4
      split-string: 3.1.0

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  side-channel@1.0.6:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      object-inspect: 1.13.1

  siginfo@2.0.0: {}

  signal-exit@3.0.7: {}

  signal-exit@4.1.0: {}

  sirv@2.0.4:
    dependencies:
      '@polka/url': 1.0.0-next.25
      mrmime: 2.0.0
      totalist: 3.0.1

  slash@3.0.0: {}

  slate-history@0.109.0(slate@0.72.8):
    dependencies:
      is-plain-object: 5.0.0
      slate: 0.72.8

  slate-history@0.66.0(slate@0.72.8):
    dependencies:
      is-plain-object: 5.0.0
      slate: 0.72.8

  slate@0.72.8:
    dependencies:
      immer: 9.0.21
      is-plain-object: 5.0.0
      tiny-warning: 1.0.3

  slice-ansi@5.0.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 4.0.0

  slice-ansi@7.1.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 5.0.0

  snabbdom@3.6.2: {}

  snapdragon-node@2.1.1:
    dependencies:
      define-property: 1.0.0
      isobject: 3.0.1
      snapdragon-util: 3.0.1

  snapdragon-util@3.0.1:
    dependencies:
      kind-of: 3.2.2

  snapdragon@0.8.2:
    dependencies:
      base: 0.11.2
      debug: 2.6.9
      define-property: 0.2.5
      extend-shallow: 2.0.1
      map-cache: 0.2.2
      source-map: 0.5.7
      source-map-resolve: 0.5.3
      use: 3.1.1
    transitivePeerDependencies:
      - supports-color

  sortablejs@1.14.0: {}

  source-map-js@1.2.0: {}

  source-map-resolve@0.5.3:
    dependencies:
      atob: 2.1.2
      decode-uri-component: 0.2.2
      resolve-url: 0.2.1
      source-map-url: 0.4.1
      urix: 0.1.0

  source-map-url@0.4.1: {}

  source-map@0.5.7: {}

  source-map@0.6.1: {}

  spark-md5@3.0.2: {}

  split-string@3.1.0:
    dependencies:
      extend-shallow: 3.0.2

  split2@3.2.2:
    dependencies:
      readable-stream: 3.6.2

  ssf@0.11.2:
    dependencies:
      frac: 1.1.2

  ssr-window@3.0.0: {}

  ssr-window@4.0.2: {}

  stable@0.1.8: {}

  stackback@0.0.2: {}

  static-extend@0.1.2:
    dependencies:
      define-property: 0.2.5
      object-copy: 0.1.0

  statuses@1.5.0: {}

  std-env@3.7.0: {}

  stream-http@2.8.2:
    dependencies:
      builtin-status-codes: 3.0.0
      inherits: 2.0.4
      readable-stream: 2.3.8
      to-arraybuffer: 1.0.1
      xtend: 4.0.2

  stream-shift@1.0.3: {}

  stream-wormhole@1.1.0: {}

  strict-uri-encode@1.1.0: {}

  string-argv@0.3.2: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string-width@7.1.0:
    dependencies:
      emoji-regex: 10.3.0
      get-east-asian-width: 1.2.0
      strip-ansi: 7.1.0

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strip-ansi@3.0.1:
    dependencies:
      ansi-regex: 2.1.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.0.1

  strip-final-newline@2.0.0: {}

  strip-final-newline@3.0.0: {}

  strip-json-comments@3.1.1: {}

  strip-literal@2.0.0:
    dependencies:
      js-tokens: 8.0.3

  strnum@1.0.5: {}

  supports-color@2.0.0: {}

  supports-color@3.2.3:
    dependencies:
      has-flag: 1.0.0

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  svg-baker@1.7.0:
    dependencies:
      bluebird: 3.7.2
      clone: 2.1.2
      he: 1.2.0
      image-size: 0.5.5
      loader-utils: 1.4.2
      merge-options: 1.0.1
      micromatch: 3.1.0
      postcss: 5.2.18
      postcss-prefix-selector: 1.16.0(postcss@5.2.18)
      posthtml-rename-id: 1.0.12
      posthtml-svg-mode: 1.0.3
      query-string: 4.3.4
      traverse: 0.6.8
    transitivePeerDependencies:
      - supports-color

  svg-tags@1.0.0: {}

  svgo@2.8.0:
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 4.3.0
      css-tree: 1.1.3
      csso: 4.2.0
      picocolors: 1.0.0
      stable: 0.1.8

  svgo@3.2.0:
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 5.1.0
      css-tree: 2.3.1
      css-what: 6.1.0
      csso: 5.0.5
      picocolors: 1.0.0

  symbol-tree@3.2.4: {}

  text-table@0.2.0: {}

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  through@2.3.8: {}

  tiny-emitter@2.1.0: {}

  tiny-warning@1.0.3: {}

  tinybench@2.6.0: {}

  tinypool@0.8.2: {}

  tinyspy@2.2.1: {}

  tippy.js@6.3.7:
    dependencies:
      '@popperjs/core': 2.11.8

  to-arraybuffer@1.0.1: {}

  to-fast-properties@2.0.0: {}

  to-object-path@0.3.0:
    dependencies:
      kind-of: 3.2.2

  to-regex-range@2.1.1:
    dependencies:
      is-number: 3.0.0
      repeat-string: 1.6.1

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  to-regex@3.0.2:
    dependencies:
      define-property: 2.0.2
      extend-shallow: 3.0.2
      regex-not: 1.0.2
      safe-regex: 1.1.0

  totalist@3.0.1: {}

  tough-cookie@4.1.3:
    dependencies:
      psl: 1.9.0
      punycode: 2.3.1
      universalify: 0.2.0
      url-parse: 1.5.10

  tr46@5.0.0:
    dependencies:
      punycode: 2.3.1

  traverse@0.6.8: {}

  ts-api-utils@1.3.0(typescript@5.4.3):
    dependencies:
      typescript: 5.4.3

  tslib@1.14.1: {}

  tslib@2.6.3: {}

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-detect@4.0.8: {}

  type-fest@0.20.2: {}

  type@2.7.3: {}

  typedarray@0.0.6: {}

  typescript@5.4.3: {}

  uc.micro@2.1.0: {}

  ufo@1.5.3: {}

  unconfig@0.3.11:
    dependencies:
      '@antfu/utils': 0.7.7
      defu: 6.1.4
      jiti: 1.21.0
      mlly: 1.6.1

  undici-types@5.26.5: {}

  unescape@1.0.1:
    dependencies:
      extend-shallow: 2.0.1

  union-value@1.0.1:
    dependencies:
      arr-union: 3.1.0
      get-value: 2.0.6
      is-extendable: 0.1.1
      set-value: 2.0.1

  universalify@0.2.0: {}

  universalify@2.0.1: {}

  unocss@0.58.6(postcss@5.2.18)(rollup@4.13.0)(vite@5.3.4(@types/node@20.11.30)(sass@1.72.0)):
    dependencies:
      '@unocss/astro': 0.58.6(rollup@4.13.0)(vite@5.3.4(@types/node@20.11.30)(sass@1.72.0))
      '@unocss/cli': 0.58.6(rollup@4.13.0)
      '@unocss/core': 0.58.6
      '@unocss/extractor-arbitrary-variants': 0.58.6
      '@unocss/postcss': 0.58.6(postcss@5.2.18)
      '@unocss/preset-attributify': 0.58.6
      '@unocss/preset-icons': 0.58.6
      '@unocss/preset-mini': 0.58.6
      '@unocss/preset-tagify': 0.58.6
      '@unocss/preset-typography': 0.58.6
      '@unocss/preset-uno': 0.58.6
      '@unocss/preset-web-fonts': 0.58.6
      '@unocss/preset-wind': 0.58.6
      '@unocss/reset': 0.58.6
      '@unocss/transformer-attributify-jsx': 0.58.6
      '@unocss/transformer-attributify-jsx-babel': 0.58.6
      '@unocss/transformer-compile-class': 0.58.6
      '@unocss/transformer-directives': 0.58.6
      '@unocss/transformer-variant-group': 0.58.6
      '@unocss/vite': 0.58.6(rollup@4.13.0)(vite@5.3.4(@types/node@20.11.30)(sass@1.72.0))
    optionalDependencies:
      vite: 5.3.4(@types/node@20.11.30)(sass@1.72.0)
    transitivePeerDependencies:
      - postcss
      - rollup
      - supports-color

  unset-value@1.0.0:
    dependencies:
      has-value: 0.3.1
      isobject: 3.0.1

  update-browserslist-db@1.0.13(browserslist@4.23.0):
    dependencies:
      browserslist: 4.23.0
      escalade: 3.1.2
      picocolors: 1.0.0

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  urix@0.1.0: {}

  url-parse@1.5.10:
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0

  urllib@2.41.0:
    dependencies:
      any-promise: 1.3.0
      content-type: 1.0.5
      debug: 2.6.9
      default-user-agent: 1.0.0
      digest-header: 1.1.0
      ee-first: 1.1.1
      formstream: 1.5.1
      humanize-ms: 1.2.1
      iconv-lite: 0.4.24
      ip: 1.1.9
      pump: 3.0.0
      qs: 6.12.1
      statuses: 1.5.0
      utility: 1.18.0
    transitivePeerDependencies:
      - supports-color

  use@3.1.1: {}

  util-deprecate@1.0.2: {}

  utility@1.18.0:
    dependencies:
      copy-to: 2.0.1
      escape-html: 1.0.3
      mkdirp: 0.5.6
      mz: 2.7.0
      unescape: 1.0.1

  uuid@9.0.1: {}

  vanilla-picker@2.12.3:
    dependencies:
      '@sphinxxxx/color-conversion': 2.2.2

  vary@1.1.2: {}

  vite-node@1.4.0(@types/node@20.11.30)(sass@1.72.0):
    dependencies:
      cac: 6.7.14
      debug: 4.3.4
      pathe: 1.1.2
      picocolors: 1.0.0
      vite: 5.3.4(@types/node@20.11.30)(sass@1.72.0)
    transitivePeerDependencies:
      - '@types/node'
      - less
      - lightningcss
      - sass
      - stylus
      - sugarss
      - supports-color
      - terser

  vite-plugin-svg-icons@2.0.1(vite@5.3.4(@types/node@20.11.30)(sass@1.72.0)):
    dependencies:
      '@types/svgo': 2.6.4
      cors: 2.8.5
      debug: 4.3.4
      etag: 1.8.1
      fs-extra: 10.1.0
      pathe: 0.2.0
      svg-baker: 1.7.0
      svgo: 2.8.0
      vite: 5.3.4(@types/node@20.11.30)(sass@1.72.0)
    transitivePeerDependencies:
      - supports-color

  vite-svg-loader@5.1.0(vue@3.4.21(typescript@5.4.3)):
    dependencies:
      svgo: 3.2.0
      vue: 3.4.21(typescript@5.4.3)

  vite@5.3.4(@types/node@20.11.30)(sass@1.72.0):
    dependencies:
      esbuild: 0.21.5
      postcss: 8.4.39
      rollup: 4.13.0
    optionalDependencies:
      '@types/node': 20.11.30
      fsevents: 2.3.3
      sass: 1.72.0

  vitest@1.4.0(@types/node@20.11.30)(jsdom@24.0.0)(sass@1.72.0):
    dependencies:
      '@vitest/expect': 1.4.0
      '@vitest/runner': 1.4.0
      '@vitest/snapshot': 1.4.0
      '@vitest/spy': 1.4.0
      '@vitest/utils': 1.4.0
      acorn-walk: 8.3.2
      chai: 4.4.1
      debug: 4.3.4
      execa: 8.0.1
      local-pkg: 0.5.0
      magic-string: 0.30.8
      pathe: 1.1.2
      picocolors: 1.0.0
      std-env: 3.7.0
      strip-literal: 2.0.0
      tinybench: 2.6.0
      tinypool: 0.8.2
      vite: 5.3.4(@types/node@20.11.30)(sass@1.72.0)
      vite-node: 1.4.0(@types/node@20.11.30)(sass@1.72.0)
      why-is-node-running: 2.2.2
    optionalDependencies:
      '@types/node': 20.11.30
      jsdom: 24.0.0
    transitivePeerDependencies:
      - less
      - lightningcss
      - sass
      - stylus
      - sugarss
      - supports-color
      - terser

  vue-component-type-helpers@2.0.7: {}

  vue-cropperjs@5.0.0(vue@3.4.21(typescript@5.4.3)):
    dependencies:
      cropperjs: 1.6.2
      vue: 3.4.21(typescript@5.4.3)

  vue-demi@0.14.7(vue@3.4.21(typescript@5.4.3)):
    dependencies:
      vue: 3.4.21(typescript@5.4.3)

  vue-router@4.3.0(vue@3.4.21(typescript@5.4.3)):
    dependencies:
      '@vue/devtools-api': 6.6.1
      vue: 3.4.21(typescript@5.4.3)

  vue-template-compiler@2.7.16:
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0

  vue-tsc@2.0.7(typescript@5.4.3):
    dependencies:
      '@volar/typescript': 2.1.5
      '@vue/language-core': 2.0.7(typescript@5.4.3)
      semver: 7.6.0
      typescript: 5.4.3

  vue@3.4.21(typescript@5.4.3):
    dependencies:
      '@vue/compiler-dom': 3.4.21
      '@vue/compiler-sfc': 3.4.21
      '@vue/runtime-dom': 3.4.21
      '@vue/server-renderer': 3.4.21(vue@3.4.21(typescript@5.4.3))
      '@vue/shared': 3.4.21
    optionalDependencies:
      typescript: 5.4.3

  vuedraggable-es@4.1.1(vue@3.4.21(typescript@5.4.3)):
    dependencies:
      sortablejs: 1.14.0
      vue: 3.4.21(typescript@5.4.3)

  w3c-keyname@2.2.8: {}

  w3c-xmlserializer@5.0.0:
    dependencies:
      xml-name-validator: 5.0.0

  webidl-conversions@7.0.0: {}

  whatwg-encoding@3.1.1:
    dependencies:
      iconv-lite: 0.6.3

  whatwg-mimetype@4.0.0: {}

  whatwg-url@14.0.0:
    dependencies:
      tr46: 5.0.0
      webidl-conversions: 7.0.0

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  why-is-node-running@2.2.2:
    dependencies:
      siginfo: 2.0.0
      stackback: 0.0.2

  wildcard@1.1.2: {}

  win-release@1.1.1:
    dependencies:
      semver: 5.7.2

  wmf@1.0.2: {}

  word-wrap@1.2.5: {}

  word@0.3.0: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrap-ansi@9.0.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 7.1.0
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  ws@7.5.9: {}

  ws@8.16.0: {}

  xlsx@0.18.5:
    dependencies:
      adler-32: 1.3.1
      cfb: 1.2.2
      codepage: 1.15.0
      crc-32: 1.2.2
      ssf: 0.11.2
      wmf: 1.0.2
      word: 0.3.0

  xml-name-validator@5.0.0: {}

  xml2js@0.6.2:
    dependencies:
      sax: 1.4.1
      xmlbuilder: 11.0.1

  xmlbuilder@11.0.1: {}

  xmlchars@2.2.0: {}

  xtend@4.0.2: {}

  yallist@3.1.1: {}

  yallist@4.0.0: {}

  yaml@2.3.4: {}

  yocto-queue@0.1.0: {}

  yocto-queue@1.0.0: {}
