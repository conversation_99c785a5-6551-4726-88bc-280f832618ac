{"name": "v3-admin-vite", "version": "4.4.0", "description": "一个免费开源的中后台管理系统基础解决方案，基于 Vue3、TypeScript、Element Plus、Pinia 和 Vite 等主流技术", "author": {"name": "pany", "email": "<EMAIL>", "url": "https://github.com/pany-ang"}, "repository": {"type": "git", "url": "https://github.com/un-pany/v3-admin-vite.git"}, "type": "module", "scripts": {"dev": "vite", "build:stage": "vue-tsc --noEmit && vite build --mode staging", "build": "vite build", "preview:stage": "pnpm build:stage && vite preview", "preview:prod": "pnpm build:prod && vite preview", "lint:eslint": "eslint --cache --max-warnings 0 \"{src,tests,types}/**/*.{vue,js,jsx,ts,tsx}\" --fix", "lint:prettier": "prettier --write \"{src,tests,types}/**/*.{vue,js,jsx,ts,tsx,json,css,less,scss,html,md}\"", "lint": "pnpm lint:eslint && pnpm lint:prettier", "test": "vitest"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@fast-crud/fast-crud": "^1.21.2", "@fast-crud/fast-extends": "^1.21.2", "@fast-crud/ui-element": "^1.21.2", "@fast-crud/ui-interface": "^1.21.2", "@tiptap/core": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@tiptap/vue-3": "^2.11.5", "@vueuse/core": "^10.9.0", "@wangeditor-next/editor-for-vue": "^5.1.14", "add": "^2.0.6", "axios": "1.6.8", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dayjs": "1.11.10", "element-plus": "2.6.2", "js-cookie": "3.0.5", "lodash-es": "4.17.21", "mitt": "3.0.1", "normalize.css": "8.0.1", "nprogress": "0.2.0", "path-browserify": "1.0.1", "path-to-regexp": "6.2.1", "pinia": "2.1.7", "pinyin-pro": "^3.26.0", "screenfull": "6.0.2", "vue": "3.4.21", "vue-router": "4.3.0"}, "devDependencies": {"@iconify/vue": "^4.1.2", "@types/js-cookie": "3.0.6", "@types/lodash-es": "4.17.12", "@types/node": "20.11.30", "@types/nprogress": "0.2.3", "@types/path-browserify": "1.0.2", "@typescript-eslint/eslint-plugin": "7.3.1", "@typescript-eslint/parser": "7.3.1", "@vitejs/plugin-vue": "5.0.4", "@vitejs/plugin-vue-jsx": "3.1.0", "@vue/test-utils": "2.4.5", "jsdom": "24.0.0", "lint-staged": "15.2.2", "prettier": "3.2.5", "sass": "1.72.0", "typescript": "5.4.3", "unocss": "0.58.6", "vite": "5.3.4", "vite-plugin-svg-icons": "2.0.1", "vite-svg-loader": "5.1.0", "vitest": "1.4.0", "vue-tsc": "2.0.7"}, "lint-staged": {"*.{vue,js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{css,less,scss,html,md}": ["prettier --write"], "package.json": ["prettier --write"]}, "keywords": ["vue", "vue3", "admin", "vue-admin", "vue3-admin", "vite", "vite-admin", "element-plus", "element-plus-admin", "ts", "typescript"], "license": "MIT"}