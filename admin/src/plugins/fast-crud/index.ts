// 引入fast-crud
import { CrudOptions, FastCrud, setLogger } from "@fast-crud/fast-crud"
import "@fast-crud/fast-crud/dist/style.css"
import { FsExtendsEditor, FsExtendsTime, FsExtendsUploader } from "@fast-crud/fast-extends"
import "@fast-crud/fast-extends/dist/style.css"
// 请选择ui: element/ antdv /naive。三选一，不支持动态切换
// element
import ui from "@fast-crud/ui-element"
import { App } from "vue"
// @ts-ignore
import { registerPlugins } from "./plugins"
import { request } from "@/utils/service"

export function useFastCrud(app: App) {
  // 先安装ui
  app.use(ui)
  // 设置日志级别
  setLogger({ level: "error" })

  // 然后安装FastCrud
  app.use(FastCrud, {
    logger: { off: { tableColumns: false } },
    // 此处配置公共的dictRequest（字典请求）
    async dictRequest(config) {
      // @ts-ignore
      return await request(config) //根据dict的url，异步返回一个字典数组
    },
    //公共crud配置
    commonOptions(): CrudOptions {
      return {
        request: {
          //接口请求配置
          //你项目后台接口大概率与fast-crud所需要的返回结构不一致，所以需要配置此项
          //请参考文档http://fast-crud.docmirror.cn/api/crud-options/request.html
          transformQuery: ({ page, form, sort }) => {
            //转换为你pageRequest所需要的请求参数结构
            const query = {
              params: {
                page: page?.currentPage,
                size: page?.pageSize
              },
              filters: form
            }
            // debugger;
            if (typeof sort?.asc === "boolean") {
              // @ts-ignore
              query.params["order_by"] = sort.asc ? sort.prop : `-${sort.prop}`
            }
            // 修改form中status为
            if (form?.status === "") {
              form.status = null
            }
            return query
          },
          transformRes: ({ res }) => {
            //将pageRequest的返回数据，转换为fast-crud所需要的格式
            //return {records,currentPage,pageSize,total};
            return {
              records: res.items,
              currentPage: res.page,
              pageSize: res.size,
              total: res.total
            }
            // return { ...res };
          }
        },
        actionbar: {
          buttons: {
            add: {
              icon: "gg:add"
            }
          }
        },
        //你可以在此处配置你的其他crudOptions公共配置
        table: {
          editable: {
            enabled: false,
            mode: "cell",
            activeTrigger: "onClick"
          }
        },
        rowHandle: {
          // fixed: "right",
          align: "center",
          width: 260,
          buttons: {
            edit: {
              icon: "edit",
              text: ""
            },
            view: {
              icon: "view",
              text: ""
            },
            copy: { show: true, text: null, icon: "ion:copy-outline", size: "default" },
            remove: {
              icon: "delete",
              text: ""
            }
          }
        },
        pagination: {
          pageSize: 10,
          background: true,
          // "hide-on-single-page": true,
          "page-sizes": ["5", "10", "20", "50", "100"]
        },
        form: {
          col: {
            span: 24
          }
        },
        columns: {
          id: {
            title: "id",
            form: { show: false }, // 表单配置
            column: {
              width: 70,
              order: -10
              // show: false,
            }
          }
        }
      }
    }
  })
  app.use(FsExtendsTime)
  //安装uploader 公共参数
  app.use(FsExtendsUploader, {
    defaultType: "form",
    form: {
      keepName: true,
      action: `/tiku/sync_cdn_file`,
      name: "file",
      withCredentials: false,
      uploadRequest: async ({ action, file, onProgress }) => {
        // @ts-ignore
        const data = new FormData()
        data.append("file", file)
        return await request({
          url: action,
          method: "post",
          timeout: 60000,
          headers: {
            "Content-Type": "multipart/form-data"
          },
          data,
          onUploadProgress: (p) => {
            // @ts-ignore
            onProgress({ percent: Math.round((p.loaded / p.total) * 100) })
          }
        })
      }
    }
  })
  //安装editor
  app.use(FsExtendsEditor, {
    //编辑器的公共配置
    // @ts-ignore
    wangEditor5: {
      editorConfig: {
        MENU_CONF: {
          fontSize: {
            fontSizeList: [
              "12px",
              "14px",
              "16px",
              "24px",
              "30px",
              "36px",
              "48px",
              "58px",
              "64px",
              "72px",
              "80px",
              "88px",
              "96px",
              "104px",
              "112px",
              "120px",
              "128px",
              "364px"
            ]
          },
          lineHeight: {
            lineHeightList: ["1", "1.2", "1.3", "1.5", "2", "2.5", "3", "4", "5"]
          }
        }
      },
      toolbarConfig: {
        // excludeKeys: ["video", "redo", "fullscreen"],
      }
    }
  })
  // 注册自定义组件
  registerPlugins()
}
