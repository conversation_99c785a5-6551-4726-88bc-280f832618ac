import { type App } from "vue"
import * as ElementPlusIconsVue from "@element-plus/icons-vue"

// 记录已注册的图标，避免重复注册
const registeredIcons = new Set<string>()

export function loadElementPlusIcon(app: App) {
  /** 注册所有 Element Plus Icon */
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    if (!registeredIcons.has(key)) {
      try {
        app.component(key, component)
        registeredIcons.add(key)
      } catch (error) {
        // 忽略重复注册的警告
        if (!error.message?.includes('already been registered')) {
          console.warn(`注册图标 ${key} 失败:`, error)
        }
      }
    }
  }
}
