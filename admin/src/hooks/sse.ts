import { ref } from "vue"
import { fetchEventSource } from "@microsoft/fetch-event-source"
import { ElMessage } from "element-plus"

export const useEventSource = (url: string, data: any) => {
  const messageList = ref<string[]>([])
  const ctrl = new AbortController()
  const eventSource = fetchEventSource(url, {
    method: "post",
    body: JSON.stringify(data),
    headers: {
      "Content-Type": "application/json"
    },
    onmessage: (event: any) => {
      // 如果传递过来的数据为空
      messageList.value.push(event.data)
    },
    onerror: (event: any) => {
      ElMessage.error("请求失败")
      throw event
    },
    signal: ctrl.signal
  })
  return { messageList, eventSource }
}
