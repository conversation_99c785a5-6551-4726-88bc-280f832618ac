// core
import { createApp } from "vue"
import App from "@/App.vue"

import router, { setupRouter } from "@/router"
// load
import { loadSvg } from "@/icons"
import { loadPlugins } from "@/plugins"
import { loadDirectives } from "@/directives"
// css
import "uno.css"
import "normalize.css"
import "element-plus/dist/index.css"
import "element-plus/theme-chalk/dark/css-vars.css"
import "@/styles/index.scss"

async function setupApp() {
  const app = createApp(App)
  await setupRouter(app)
  /** 加载插件 */
  loadPlugins(app)
  /** 加载全局 SVG */
  loadSvg(app)
  /** 加载自定义指令 */
  loadDirectives(app)
  router.isReady().then(() => {
    app.mount("#app")
  })
}

setupApp()
