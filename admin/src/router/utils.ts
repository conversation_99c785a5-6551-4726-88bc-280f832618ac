import type { RouteRecordRaw } from "vue-router"
import { get } from "lodash-es"

/**
 * 获取route属性列表（包含children）
 * @param route RouteRecordRaw
 * @param attr
 */
export function getRouteAttr(route: RouteRecordRaw, attr: string = "name") {
  const attrs: string[] = [get(route, attr, "name")]
  if (route.children && route.children.length) {
    attrs.push(...route.children.map((item) => getRouteAttr(item, attr)).flat(1))
  }
  return attrs
}

export function getRouteNames(routes: RouteRecordRaw[]) {
  return routes.map((route) => getRouteAttr(route, "name")).flat(1)
}

export function getRoutePaths(routes: RouteRecordRaw[]) {
  return routes.map((route) => getRouteAttr(route, "path")).flat(1)
}

/**
 * 过滤掉routes中path属性与filterPath相同的元素及其子元素
 * @param routes - 路由记录数组
 * @param filterPath - 需要过滤的路径
 * @returns 过滤后的路由记录数组
 */
export function filterRoutes(routes: RouteRecordRaw[], filterPath: string): RouteRecordRaw[] {
  return routes.reduce((acc: RouteRecordRaw[], route: RouteRecordRaw) => {
    if (route.path !== filterPath) {
      if (route.children && route.children.length) {
        route.children = filterRoutes(route.children, filterPath)
      }
      acc.push(route)
    }
    return acc
  }, [])
}
