import type { RouteRecordRaw } from "vue-router"

const Layouts = () => import("@/layouts/index.vue")
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/redirect",
    component: Layouts,
    meta: {
      hidden: true
    },
    children: [
      {
        path: ":path(.*)",
        component: () => import("@/views/base/redirect/index.vue")
      }
    ]
  },
  {
    path: "/403",
    component: () => import("@/views/base/error-page/403.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/404",
    component: () => import("@/views/base/error-page/404.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/login",
    component: () => import("@/views/base/login/index.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/",
    component: Layouts,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        component: () => import("@/views/base/dashboard/index.vue"),
        name: "Dashboard",
        meta: {
          title: "首页",
          svgIcon: "dashboard",
          affix: true
        }
      }
    ]
  }
]

export const NOT_FOUND_ROUTE = {
  name: "NotFound",
  path: "/:pathMatch(.*)*",
  redirect: "/404",
  isHidden: true
}

export const EMPTY_ROUTE = {
  name: "Empty",
  path: "/:pathMatch(.*)*",
  component: null
}

// 加载 views 下每个模块的 index.vue 文件
export const vueModules = import.meta.glob(["@/views/**/index.vue", "!@/views/base/**/index.vue"])
// 定义正则表达式
const regex = new RegExp(`^\\/src\\/views(.*?)\\/index\\.vue$`)

// 执行匹配
export const vueComponents = Object.keys(vueModules).map((item) => {
  // 执行匹配
  const match = item.match(regex)
  item.match(regex)
  // 如果匹配成功，则输出捕获的中间部分
  if (match) {
    return match[1]
  } else {
  }
})
