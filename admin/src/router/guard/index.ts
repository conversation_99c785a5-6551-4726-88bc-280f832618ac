import { Router } from "vue-router"
import { getToken } from "@/utils/cache/cookies"
import { useTitle } from "@/hooks/useTitle"
import NProgress from "nprogress"
import { setRouteChange } from "@/hooks/useRouteListener"

const { setTitle } = useTitle()
NProgress.configure({ showSpinner: false })

const WHITE_LIST = ["/login", "/404"]

export function setupRouterGuard(router: Router) {
  router.beforeEach(async (to) => {
    NProgress.start()
    const token = getToken()

    /** 没有token的情况 */
    if (!token) {
      if (WHITE_LIST.includes(to.path)) return true
      return { path: "login", query: { ...to.query, redirect: to.path } }
    }

    /** 有token的情况 */
    if (to.path === "/login") return { path: "/" }
    return true
  })

  router.afterEach((to) => {
    setRouteChange(to)
    setTitle(to.meta.title)
    NProgress.done()
  })
}
