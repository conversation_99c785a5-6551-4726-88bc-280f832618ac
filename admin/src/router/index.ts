import { type RouteRecordRaw, createRouter, createWebHashHist<PERSON>, create<PERSON>ebHistory, RouteRecordName } from "vue-router"

import { App } from "vue"
import { constantRoutes, EMPTY_ROUTE, NOT_FOUND_ROUTE } from "./routes"
import { setupRouterGuard } from "./guard"
import { getToken } from "@/utils/cache/cookies"
import { useUserStore } from "@/store/modules/user"
import { usePermissionStore } from "@/store/modules/permission"
import { getRouteNames } from "./utils"

export const history =
  import.meta.env.VITE_ROUTER_HISTORY === "hash"
    ? createWebHashHistory(import.meta.env.VITE_PUBLIC_PATH)
    : createWebHistory(import.meta.env.VITE_PUBLIC_PATH)
export const router = createRouter({
  history: history,
  routes: constantRoutes,
  scrollBehavior: () => ({ left: 0, top: 0 })
})

export async function setupRouter(app: App) {
  await addDynamicRoutes()
  setupRouterGuard(router)
  app.use(router)
}

export async function resetRouter() {
  const basicRouteNames = getRouteNames(constantRoutes)
  router.getRoutes().forEach((route) => {
    const { name } = route
    // 删除不包含在基础路由中的路由
    // @ts-ignore
    if (!basicRouteNames.includes(name)) {
      router.removeRoute(<string | symbol>name)
    }
  })
}

export async function addDynamicRoutes() {
  const token = getToken()

  // 没有token情况
  if (!token) {
    // @ts-ignore
    router.addRoute(EMPTY_ROUTE)
    return
  }
  // 有token的情况
  const userStore = useUserStore()
  const permissionStore = usePermissionStore()
  !userStore.userId && (await userStore.getUserInfo())
  try {
    const accessRoutes = await permissionStore.generateRoutes()
    await permissionStore.getAccessApis()
    accessRoutes.forEach((route) => {
      !router.hasRoute(<RouteRecordName>route.name) && router.addRoute(route)
    })
    router.hasRoute(EMPTY_ROUTE.name) && router.removeRoute(EMPTY_ROUTE.name)
    router.addRoute(NOT_FOUND_ROUTE)
  } catch (error) {
    console.error("error", error)
    await userStore.logout()
  }
}

export default router
