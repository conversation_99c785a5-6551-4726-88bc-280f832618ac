<script setup lang="ts">
import { reactive, ref } from "vue"
import { ElMessage, FormInstance } from "element-plus"
import { isMobile } from "@/utils/windows"
import { changePassword } from "@/api/system"

const isShow = defineModel("show", { default: true })
const formRef = ref<FormInstance>()

const formData = reactive({
  old_password: "",
  new_password: ""
})

const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      changePassword(formData).then(() => {
        ElMessage.success("修改成功")
        isShow.value = false
      })
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}
</script>

<template>
  <el-dialog v-model="isShow" title="修改密码" align-center center :width="isMobile ? '90%' : '30%'">
    <el-form ref="formRef" :model="formData" label-width="auto" class="demo-ruleForm">
      <el-form-item
        label="旧密码"
        prop="old_password"
        :rules="[
          { required: true, message: '旧密码必填' },
          { min: 5, message: '密码不能少于5位' }
        ]"
      >
        <el-input show-password v-model="formData.old_password" type="password" autocomplete="off" />
      </el-form-item>
      <el-form-item
        label="新密码"
        prop="new_password"
        :rules="[
          { required: true, message: '新密码必填' },
          { min: 6, message: '新密码不能少于6位' }
        ]"
      >
        <el-input show-password v-model="formData.new_password" type="password" autocomplete="off" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm(formRef)">重置</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<style scoped lang="scss"></style>
