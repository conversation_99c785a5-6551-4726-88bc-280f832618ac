<template>
  <div class="rich-editor">
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button size="small" @click="toggleBold" :class="{ active: isActive.bold }">
            <strong>B</strong>
          </el-button>
          <el-button size="small" @click="toggleItalic" :class="{ active: isActive.italic }">
            <em>I</em>
          </el-button>
          <el-button size="small" @click="toggleUnderline" :class="{ active: isActive.underline }">
            <u>U</u>
          </el-button>
        </el-button-group>
        
        <el-button-group style="margin-left: 12px;">
          <el-button size="small" @click="addPinyinToSelection" :icon="Reading">添加拼音</el-button>
          <el-button size="small" @click="removePinyinFromSelection" :icon="Delete">移除拼音</el-button>
        </el-button-group>
        
        <el-upload
          :action="uploadUrl"
          :headers="uploadHeaders"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :show-file-list="false"
          accept="image/*"
          style="margin-left: 12px; display: inline-block;"
        >
          <el-button size="small" :icon="Upload">上传图片</el-button>
        </el-upload>
      </div>
      
      <div class="toolbar-right">
        <el-button size="small" @click="togglePreview" :icon="View" :type="showPreview ? 'primary' : 'default'">
          {{ showPreview ? '编辑' : '预览' }}
        </el-button>
        <el-button size="small" @click="clearContent" :icon="RefreshLeft">清空</el-button>
      </div>
    </div>

    <div class="editor-container" :class="{ 'split-view': showPreview }">
      <!-- 编辑区域 -->
      <div v-show="!showPreview || showPreview" class="editor-area" :class="{ 'half-width': showPreview }">
        <div 
          ref="editorRef"
          class="editor-content"
          contenteditable="true"
          v-html="modelValue"
          @input="handleInput"
          @mouseup="updateSelection"
          @keyup="updateSelection"
          @focus="updateSelection"
          @paste="handlePaste"
          placeholder="请输入题目内容..."
        ></div>
      </div>
      
      <!-- 预览区域 -->
      <div v-show="showPreview" class="preview-area" :class="{ 'half-width': showPreview }">
        <div class="preview-content" v-html="modelValue"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Reading, Delete, Upload, View, RefreshLeft } from '@element-plus/icons-vue'
import { pinyin } from 'pinyin-pro'

defineOptions({
  name: 'RichEditor'
})

interface Props {
  modelValue: string
  placeholder?: string
  height?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  height: '300px'
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string]
}>()

const editorRef = ref<HTMLElement>()
const showPreview = ref(false)
const currentSelection = ref<Selection | null>(null)

// 格式化状态
const isActive = ref({
  bold: false,
  italic: false,
  underline: false
})

// 上传配置
const uploadUrl = computed(() => '/api/tiku/sync_cdn_file')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${localStorage.getItem('access_token') || ''}`
}))

// 监听内容变化
watch(() => props.modelValue, (newValue) => {
  if (editorRef.value && editorRef.value.innerHTML !== newValue) {
    editorRef.value.innerHTML = newValue
  }
}, { immediate: true })

// 处理输入
const handleInput = () => {
  if (editorRef.value) {
    const content = editorRef.value.innerHTML
    emit('update:modelValue', content)
    emit('change', content)
  }
  updateFormatState()
}

// 处理粘贴
const handlePaste = (e: ClipboardEvent) => {
  e.preventDefault()
  const text = e.clipboardData?.getData('text/plain') || ''
  document.execCommand('insertText', false, text)
}

// 更新选择
const updateSelection = () => {
  currentSelection.value = window.getSelection()
  updateFormatState()
}

// 更新格式状态
const updateFormatState = () => {
  isActive.value.bold = document.queryCommandState('bold')
  isActive.value.italic = document.queryCommandState('italic')
  isActive.value.underline = document.queryCommandState('underline')
}

// 格式化操作
const toggleBold = () => {
  document.execCommand('bold')
  handleInput()
}

const toggleItalic = () => {
  document.execCommand('italic')
  handleInput()
}

const toggleUnderline = () => {
  document.execCommand('underline')
  handleInput()
}

// 拼音功能
const addPinyinToSelection = () => {
  const selection = window.getSelection()
  if (!selection || !selection.toString().trim()) {
    ElMessage.warning('请先选择要添加拼音的文字')
    return
  }
  
  const selectedText = selection.toString().trim()
  if (!/[\u4e00-\u9fa5]/.test(selectedText)) {
    ElMessage.warning('请选择包含中文的文字')
    return
  }
  
  try {
    const pinyinResult = pinyin(selectedText, { type: 'array', toneType: 'symbol' })
    let rubyHTML = ''
    
    for (let i = 0; i < selectedText.length; i++) {
      const char = selectedText[i]
      const pinyinChar = pinyinResult[i] || ''
      
      if (/[\u4e00-\u9fa5]/.test(char)) {
        rubyHTML += `<ruby>${char}<rt>${pinyinChar}</rt></ruby>`
      } else {
        rubyHTML += char
      }
    }
    
    const range = selection.getRangeAt(0)
    const rubyElement = document.createElement('span')
    rubyElement.innerHTML = rubyHTML
    range.deleteContents()
    range.insertNode(rubyElement)
    
    selection.removeAllRanges()
    handleInput()
    
    ElMessage.success('拼音添加成功')
  } catch (error) {
    console.error('添加拼音失败:', error)
    ElMessage.error('添加拼音失败')
  }
}

const removePinyinFromSelection = () => {
  const selection = window.getSelection()
  if (!selection || !selection.toString().trim()) {
    ElMessage.warning('请先选择要移除拼音的文字')
    return
  }
  
  try {
    const range = selection.getRangeAt(0)
    const container = range.commonAncestorContainer
    
    let rubyElements: Element[] = []
    if (container.nodeType === Node.ELEMENT_NODE) {
      rubyElements = Array.from((container as Element).querySelectorAll('ruby'))
    } else if (container.parentElement) {
      rubyElements = Array.from(container.parentElement.querySelectorAll('ruby'))
    }
    
    rubyElements.forEach(ruby => {
      const textContent = ruby.textContent || ''
      const textNode = document.createTextNode(textContent)
      ruby.parentNode?.replaceChild(textNode, ruby)
    })
    
    handleInput()
    ElMessage.success('拼音移除成功')
  } catch (error) {
    console.error('移除拼音失败:', error)
    ElMessage.error('移除拼音失败')
  }
}

// 切换预览
const togglePreview = () => {
  showPreview.value = !showPreview.value
}

// 清空内容
const clearContent = () => {
  if (editorRef.value) {
    editorRef.value.innerHTML = ''
    handleInput()
  }
}

// 上传相关
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  
  return true
}

const handleUploadSuccess = (response: any) => {
  if (response && response.url) {
    const img = `<img src="${response.url}" alt="上传图片" style="max-width: 100%; height: auto; margin: 8px 0;" />`
    if (editorRef.value) {
      const selection = window.getSelection()
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const imgElement = document.createElement('div')
        imgElement.innerHTML = img
        range.insertNode(imgElement.firstChild!)
        range.collapse(false)
      } else {
        editorRef.value.innerHTML += img
      }
      handleInput()
    }
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败')
  }
}

// 暴露方法
defineExpose({
  focus: () => editorRef.value?.focus(),
  blur: () => editorRef.value?.blur(),
  getContent: () => editorRef.value?.innerHTML || '',
  setContent: (content: string) => {
    if (editorRef.value) {
      editorRef.value.innerHTML = content
      handleInput()
    }
  }
})
</script>

<style scoped>
.rich-editor {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  flex-wrap: wrap;
  gap: 8px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-container {
  display: flex;
  min-height: v-bind(height);
}

.split-view {
  border-top: 1px solid #e4e7ed;
}

.editor-area,
.preview-area {
  flex: 1;
  min-height: v-bind(height);
}

.half-width {
  width: 50%;
  flex: none;
}

.editor-area {
  border-right: 1px solid #e4e7ed;
}

.editor-content {
  min-height: v-bind(height);
  padding: 16px;
  outline: none;
  line-height: 1.6;
  font-size: 14px;
  overflow-y: auto;
}

.editor-content:focus {
  background-color: #fafafa;
}

.editor-content[contenteditable]:empty::before {
  content: attr(placeholder);
  color: #c0c4cc;
}

.preview-area {
  background-color: #fafbfc;
}

.preview-content {
  padding: 16px;
  line-height: 1.6;
  font-size: 14px;
  min-height: v-bind(height);
  overflow-y: auto;
}

/* 拼音样式 */
.preview-content :deep(ruby) {
  ruby-align: center;
  margin: 0 1px;
}

.preview-content :deep(rt) {
  font-size: 0.7em;
  text-align: center;
  color: #666;
  line-height: 1;
}

.editor-content :deep(ruby) {
  ruby-align: center;
  margin: 0 1px;
}

.editor-content :deep(rt) {
  font-size: 0.7em;
  text-align: center;
  color: #666;
  line-height: 1;
}

/* 按钮激活状态 */
.el-button.active {
  background-color: #409eff;
  color: white;
  border-color: #409eff;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .editor-toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
  
  .split-view .editor-container {
    flex-direction: column;
  }
  
  .half-width {
    width: 100%;
  }
  
  .editor-area {
    border-right: none;
    border-bottom: 1px solid #e4e7ed;
  }
}
</style>
