import type { CrudExpose } from "@fast-crud/fast-crud/dist/d/d/expose"
import Crud from "@/api/crud"
import { computed, Ref } from "vue"
import { ActionbarClickEvent, ButtonsProps } from "@fast-crud/fast-crud/dist/d/d/crud"
import { hasPermission } from "@/directives"
import { ElMessage, ElMessageBox } from "element-plus"

export function useActionButtons(
  crudExpose: CrudExpose,
  model: Crud,
  selectedRowKeys: Ref
): ButtonsProps<ActionbarClickEvent> {
  const disableSelected = computed(() => selectedRowKeys.value.length === 0)
  return {
    deleteAll: {
      text: "批量删除",
      icon: "material-symbols-light:delete",
      type: "danger",
      // @ts-ignore
      show: hasPermission(`delete${model.realUrl}/deleteall`),
      disabled: disableSelected,
      click: () => {
        ElMessageBox.confirm("确定要批量删除吗？").then(async () => {
          selectedRowKeys.value.map((item: { id: any }) => item.id)
          if (selectedRowKeys.value.length > 0) {
            model.delete(selectedRowKeys.value.join(",")).then(async () => {
              await crudExpose.doRefresh()
              ElMessage.success("删除成功")
            })
          } else {
            ElMessage.error("请选择要删除的数据")
          }
        })
      }
    },
    enableStatus: {
      text: "批量启用",
      type: "success",
      icon: "bxs:lock-open",
      disabled: disableSelected,
      show: hasPermission(`put${model.realUrl}/update`),
      click(context) {
        ElMessageBox.confirm("确定要启用吗？").then(async () => {
          if (selectedRowKeys.value.length > 0) {
            // @ts-ignore
            const updatePromises = selectedRowKeys.value.map(async (id) => {
              await model.update(id, { status: true })
            })
            // 等待所有更新操作完成
            await Promise.all(updatePromises)
            ElMessage.success("操作成功")
            await crudExpose.doRefresh()
            // setData({ 0: {id:1} }); //设置data
          } else {
            ElMessage.warning("没有数据需要操作")
          }
        })
      }
    },
    disableStatus: {
      text: "批量禁用",
      type: "warning",
      icon: "pepicons-print:lock-closed-circle-off",
      disabled: disableSelected,
      show: hasPermission(`put${model.realUrl}/update`),
      click() {
        ElMessageBox.confirm("确定要禁用吗？").then(async () => {
          if (selectedRowKeys.value.length > 0) {
            // @ts-ignore
            const updatePromises = selectedRowKeys.value.map(async (id) => {
              await model.update(id, { status: false })
            })
            // 等待所有更新操作完成
            await Promise.all(updatePromises)
            ElMessage.success("操作成功")
            await crudExpose.doRefresh()
            // setData({ 0: {id:1} }); //设置data
          } else {
            ElMessage.warning("没有数据需要操作")
          }
        })
      }
    },
    add: {
      show: hasPermission(`post${model.realUrl}/create`)
      // text:'aa'
    }
  }
}
