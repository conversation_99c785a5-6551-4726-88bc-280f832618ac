import Crud from "@/api/crud"
import { ButtonsProps, ScopeContext } from "@fast-crud/fast-crud/dist/d/d/crud"
import { hasPermission } from "@/directives"

export function useRowHandle(model: Crud): ButtonsProps<ScopeContext> {
  return {
    edit: {
      show: hasPermission(`put${model.realUrl}/update`)
    },
    view: {
      show: hasPermission(`get${model.realUrl}/read`)
    },
    remove: {
      show: hasPermission(`delete${model.realUrl}/delete`)
    },
    copy: {
      show: hasPermission(`post${model.realUrl}/create`)
    }
  }
}
