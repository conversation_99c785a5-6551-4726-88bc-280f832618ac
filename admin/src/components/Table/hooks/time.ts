import { startOfDay, subDays, startOfMonth, endOfMonth, subMonths, startOfYear } from "date-fns"

// 类型定义
type TimeRange = [Date, Date]
type TimestampRange = [number, number]
type TimeRangeResult = TimeRange | TimestampRange

interface ShortcutOptions {
  useTimestamp?: boolean
  performanceMode?: boolean
}

interface TimeShortcut {
  text: string
  value: () => TimeRangeResult
}

interface TimeShortcuts extends Array<TimeShortcut> {
  getRange: (type: string) => TimeRangeResult | null
  getAllRanges: () => Record<string, TimeRangeResult>
}

// 常量
const MS_PER_DAY = 86400000 // 24 * 60 * 60 * 1000
const MS_PER_WEEK = MS_PER_DAY * 7
const CHINA_TIMEZONE_OFFSET = 8 * 60 * 60 * 1000 // 8小时的毫秒数

// 工具函数
const createTimeHelpers = () => {
  // 获取中国时间的Date对象
  const getChinaDate = (date: Date = new Date()): Date => {
    const localTime = date.getTime()
    const localOffset = date.getTimezoneOffset() * 60000 // 本地时区偏移的毫秒数
    const utc = localTime + localOffset // UTC时间
    const chinaTime = utc + CHINA_TIMEZONE_OFFSET // 中国时间
    return new Date(chinaTime)
  }

  const getNow = (): Date => {
    return getChinaDate()
  }

  const getStartOfDay = (date: Date): Date => {
    const chinaDate = getChinaDate(date)
    chinaDate.setHours(0, 0, 0, 0)
    return chinaDate
  }

  const getEndOfDay = (date: Date): Date => {
    const chinaDate = getChinaDate(date)
    chinaDate.setHours(23, 59, 59, 999)
    return chinaDate
  }

  const getTimestampRange = (startDate: Date, endDate: Date): TimestampRange => {
    return [startDate.getTime(), endDate.getTime()]
  }

  return {
    getNow,
    getStartOfDay,
    getEndOfDay,
    getTimestampRange,
    getChinaDate
  }
}

// 创建时间快捷方式
export function useTimeShortcuts(options: ShortcutOptions = {}): TimeShortcuts {
  const { useTimestamp = false, performanceMode = false } = options
  const { getNow, getStartOfDay, getEndOfDay, getTimestampRange } = createTimeHelpers()

  // 性能模式下的缓存
  let cachedNow: Date | null = null
  let cacheTimeout: number | null = null

  // 在性能模式下获取当前时间，缓存100ms
  const getCurrentTime = (): Date => {
    if (performanceMode) {
      const currentTime = Date.now()
      if (!cachedNow || !cacheTimeout || currentTime > cacheTimeout) {
        cachedNow = getNow()
        cacheTimeout = currentTime + 100 // 缓存100ms
      }
      return cachedNow
    }
    return getNow()
  }

  const shortcuts: TimeShortcut[] = [
    {
      text: "今天",
      value: () => {
        const now = getCurrentTime()
        const todayStart = getStartOfDay(now)
        return useTimestamp ? getTimestampRange(todayStart, now) : [todayStart, now]
      }
    },
    {
      text: "昨天",
      value: () => {
        const now = getCurrentTime()
        const yesterdayStart = getStartOfDay(subDays(now, 1))
        const yesterdayEnd = getEndOfDay(yesterdayStart)
        return useTimestamp ? getTimestampRange(yesterdayStart, yesterdayEnd) : [yesterdayStart, yesterdayEnd]
      }
    },
    {
      text: "近7天",
      value: () => {
        const now = getCurrentTime()
        if (useTimestamp) {
          const nowTs = now.getTime()
          const sevenDaysAgoTs = nowTs - MS_PER_WEEK
          return [sevenDaysAgoTs, nowTs] as TimestampRange
        }
        const sevenDaysAgo = subDays(now, 7)
        return [getStartOfDay(sevenDaysAgo), now] as TimeRange
      }
    },
    {
      text: "上月",
      value: () => {
        const now = getCurrentTime()
        const lastMonth = subMonths(now, 1)
        const lastMonthStart = startOfMonth(lastMonth)
        const lastMonthEnd = endOfMonth(lastMonth)
        return useTimestamp ? getTimestampRange(lastMonthStart, lastMonthEnd) : [lastMonthStart, lastMonthEnd]
      }
    },
    {
      text: "本月",
      value: () => {
        const now = getCurrentTime()
        const monthStart = startOfMonth(now)
        return useTimestamp ? getTimestampRange(monthStart, now) : [monthStart, now]
      }
    },
    {
      text: "本季度",
      value: () => {
       const now = getCurrentTime()
        const currentQuarter = Math.floor(now.getMonth() / 3) + 1;
        const quarterStartMonth = (currentQuarter - 1) * 3;
        const quarterStartDate = new Date(now.getFullYear(), quarterStartMonth, 1);
        const quarterEndDate = now; // 修改：结尾时间为当前时间
        return useTimestamp ? getTimestampRange(quarterStartDate, quarterEndDate) : [quarterStartDate, quarterEndDate];

      }
    },
    {
      text: "今年",
      value: () => {
        const now = getCurrentTime()
        const yearStart = startOfYear(now)
        return useTimestamp ? getTimestampRange(yearStart, now) : [yearStart, now]
      }
    },
    {
      text: "去年",
      value: () => {
        const now = getCurrentTime()
        const lastYear = new Date(now.getFullYear() - 1, 0, 1)
        const lastYearEnd = new Date(now.getFullYear() - 1, 11, 31, 23, 59, 59, 999)
        return useTimestamp ? getTimestampRange(lastYear, lastYearEnd) : [lastYear, lastYearEnd]
      }
    }
  ]

  // 扩展 shortcuts 数组，添加工具方法
  const extendedShortcuts = shortcuts as TimeShortcuts

  extendedShortcuts.getRange = (type: string): TimeRangeResult | null => {
    const shortcut = shortcuts.find((s) => s.text === type)
    return shortcut ? shortcut.value() : null
  }

  extendedShortcuts.getAllRanges = (): Record<string, TimeRangeResult> => {
    return shortcuts.reduce<Record<string, TimeRangeResult>>((acc, cur) => {
      acc[cur.text] = cur.value()
      return acc
    }, {})
  }

  return extendedShortcuts
}
