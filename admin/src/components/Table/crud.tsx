import { CreateCrudOptionsRet, dict } from "@fast-crud/fast-crud"
import Crud, { useRequest } from "@/api/crud"
import { useActionButtons, useRowHandle, useTimeShortcuts } from "./hooks"
import { type CrudExpose } from "@fast-crud/fast-crud/dist/d/d/expose"
import { ref } from "vue"
import { isArray } from "lodash-es"

export default function createCrudOptions(crudExpose: CrudExpose, model: Crud): CreateCrudOptionsRet {
  const selectedRowKeys = ref([])

  return {
    crudOptions: {
      request: useRequest(model),
      actionbar: {
        buttons: useActionButtons(crudExpose, model, selectedRowKeys)
      },
      rowHandle: {
        buttons: useRowHandle(model)
      },
      toolbar: {
        buttons: {
          export: {
            show: false
          }
        }
      },
      columns: {
        status: {
          title: "状态",
          type: "dict-switch",
          column: {
            width: 100,
            order: 100
          },
          search: {
            show: true,
            order: 100,
            col: {
              span: 2
            }
          },
          form: {
            rules: [{ required: true, message: "请选择状态" }],
            col: { span: 6 },
            order: 2,
            value: true
          },
          dict: dict({
            data: [
              { value: true, label: "启用" },
              { value: false, label: "禁用" }
            ]
          })
        },
        update_time: {
          title: "更新时间",
          type: ["datetime", "time-humanize"],
          readonly: true,
          column: {
            width: 180,
            order: 1001,
            sortable: "custom",
            component: {
              options: {
                largest: 2
              }
            }
            // show: false,
          },
          search: {
            show: true,
            col: {
              span: 6
            },
            order: 1002,
            component: {
              type: "daterange",
              shortcuts: useTimeShortcuts()
            },
            // valueResolve({ key, value, form }) {
            //   debugger
            //   //  <------注意这里是form，不是row
            //   if (isArray(value)) {
            //     // 设置为获取到的时间的23:59:59s
            //     value[1] = new Date(value[1].setHours(23, 59, 59, 999))
            //     form[key] = value
            //   }
            // }
          }
        },
        create_time: {
          title: "创建时间",
          type: "datetime",
          readonly: true,
          column: {
            width: 180,
            order: 1000,
            sortable: "custom",
            show: true
          }
        }
      },
      settings: {
        plugins: {
          rowSelection: {
            enabled: true,
            order: -10,
            before: true,
            props: {
              multiple: true,
              crossPage: true,
              selectedRowKeys
            }
          }
        }
      },
      form: {
        saveRemind: true,
        col: { span: 12 }
      },
      table: {
        "cell-style": {
          "text-align": "center"
        },
        "header-cell-style": {
          "text-align": "center"
        }
      }
    }
  }
}
