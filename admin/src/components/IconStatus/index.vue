<template>
  <div class="icon-status">
    <el-alert
      v-if="hasErrors"
      title="图标加载状态"
      type="warning"
      :description="`发现 ${errorCount} 个图标加载问题，请检查控制台`"
      show-icon
      :closable="false"
    />
    
    <el-alert
      v-else
      title="图标加载正常"
      type="success"
      description="所有图标都已正确加载"
      show-icon
      :closable="false"
    />
    
    <!-- 开发环境下显示详细信息 -->
    <div v-if="isDev && showDetails" class="icon-details">
      <h4>图标加载详情</h4>
      <div class="icon-grid">
        <div 
          v-for="(icon, name) in testIcons" 
          :key="name"
          class="icon-test-item"
          :class="{ error: !icon.loaded }"
        >
          <div class="icon-display">
            <el-icon v-if="icon.loaded" size="20">
              <component :is="icon.component" />
            </el-icon>
            <span v-else class="icon-error">❌</span>
          </div>
          <span class="icon-name">{{ name }}</span>
          <span class="icon-status" :class="icon.loaded ? 'success' : 'error'">
            {{ icon.loaded ? '✓' : '✗' }}
          </span>
        </div>
      </div>
    </div>
    
    <div v-if="isDev" class="toggle-details">
      <el-button size="small" text @click="showDetails = !showDetails">
        {{ showDetails ? '隐藏详情' : '显示详情' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

defineOptions({
  name: 'IconStatus'
})

const showDetails = ref(false)
const isDev = ref(import.meta.env.DEV)

// 需要测试的图标列表
const iconNames = [
  'Plus', 'Delete', 'Edit', 'View', 'Search', 'Refresh', 'Close',
  'Document', 'DocumentAdd', 'Folder', 'FolderOpened',
  'Calendar', 'Clock', 'Reading', 'Upload', 'Download', 'Setting', 'Tools',
  'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight',
  'PieChart', 'DataLine', 'Lightning', 'RefreshLeft', 'Expand', 'Fold'
]

const testIcons = ref<Record<string, { loaded: boolean; component?: any }>>({})

// 计算错误数量
const errorCount = computed(() => {
  return Object.values(testIcons.value).filter(icon => !icon.loaded).length
})

const hasErrors = computed(() => errorCount.value > 0)

// 测试图标是否存在
const testIconExists = (iconName: string) => {
  try {
    const iconComponent = ElementPlusIconsVue[iconName as keyof typeof ElementPlusIconsVue]
    return {
      loaded: !!iconComponent,
      component: iconComponent
    }
  } catch (error) {
    console.warn(`图标 ${iconName} 不存在:`, error)
    return {
      loaded: false,
      component: null
    }
  }
}

// 初始化图标测试
onMounted(() => {
  iconNames.forEach(iconName => {
    testIcons.value[iconName] = testIconExists(iconName)
  })
  
  // 在控制台输出结果
  if (isDev.value) {
    const errors = Object.entries(testIcons.value)
      .filter(([, icon]) => !icon.loaded)
      .map(([name]) => name)
    
    if (errors.length > 0) {
      console.group('🔍 图标加载检查')
      console.warn('以下图标加载失败:', errors)
      console.groupEnd()
    } else {
      console.log('✅ 所有图标加载正常')
    }
  }
})
</script>

<style scoped>
.icon-status {
  margin-bottom: 16px;
}

.icon-details {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.icon-details h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.icon-test-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background-color: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.icon-test-item.error {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.icon-test-item:not(.error):hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.icon-display {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.icon-error {
  font-size: 20px;
}

.icon-name {
  font-size: 12px;
  color: #606266;
  text-align: center;
  margin-bottom: 4px;
}

.icon-status {
  font-size: 12px;
  font-weight: 600;
}

.icon-status.success {
  color: #67c23a;
}

.icon-status.error {
  color: #f56c6c;
}

.toggle-details {
  margin-top: 12px;
  text-align: center;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .icon-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
  }
  
  .icon-test-item {
    padding: 8px;
  }
  
  .icon-display {
    width: 24px;
    height: 24px;
  }
}
</style>
