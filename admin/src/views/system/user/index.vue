<template>
  <m-table :crud-options="crudOptions" :model="userModel" ref="tableRef" />
</template>

<script lang="ts" setup>
import userModel from "@/api/system/user"
import MTable from "@/components/Table/index.vue"
import { compute, CrudOptions, dict } from "@fast-crud/fast-crud"
import { ref } from "vue"
import { roleModel } from "@/api/system"
import { useUserStore } from "@/store"
import { ElMessage } from "element-plus"

const userStore = useUserStore()
const tableRef = ref()
const crudOptions: CrudOptions = {
  columns: {
    username: {
      title: "用户名",
      type: "text",
      search: { show: true, component: { placeholder: "请输入用户名" } }, // 开启查询
      form: {
        rules: [
          { required: true, message: "请输入用户名" },
          { max: 50, message: "最大50个字符" }
        ]
      },
      editForm: {
        component: {
          disabled: !userStore.isSuperUser
        },
        submit: userStore.isSuperUser
      },
      column: {
        sorter: true
      }
    },
    password: {
      title: "密码",
      type: "text",
      key: "password",
      column: {
        show: false
      },
      form: {
        rules: [{ min: 5, max: 18, message: "密码必须为5-18位之间" }],
        component: {
          showPassword: true
        },
        helper: "填写则修改密码"
      }
    },
    nickname: {
      title: "昵称",
      type: "text",
      // search: { show: true }, // 开启查询
      form: {
        rules: [{ max: 50, message: "最大50个字符" }]
      },
      column: {
        sorter: true
      }
    },
    roles: {
      title: "角色",
      colorAuto: true,
      type: "dict-select",
      valueBuilder(context) {
        // @ts-ignore
        context.row.roles = context.row.roles.map((item) => item.id)
      },
      dict: dict({
        getData: async () => {
          const { items } = await roleModel.list({ size: 100 })
          return items
        },
        value: "id",
        label: "title"
      }),
      form: {
        component: {
          multiple: true
        }
      },
      search: {
        show: true
      }
    },
    is_super: {
      title: "超级管理员",
      colorAuto: true,
      type: "dict-radio",
      dict: dict({
        data: [
          { value: true, label: "是" },
          { value: false, label: "否" }
        ]
      }),
      form: {
        labelWidth: "90",
        value: false,
      }
    }
  },
  editForm: {
    async afterSubmit({ form, row }) {
      // 当前用户为超级用户，并且准备修改用户名时
      if (form.username !== row.username && userStore.isSuperUser && userStore.username === row.username) {
        ElMessage.success("用户名修改成功，请重新登录")
        await userStore.logout()
        // location.reload()
        return false
      }
    }
  }
}
</script>
