<template>
  <m-table :crud-options="crudOptions" :model="logModel" ref="tableRef" />
</template>

<script lang="ts" setup>
import MTable from "@/components/Table/index.vue"
import { CrudOptions, dict } from "@fast-crud/fast-crud"
import { ref } from "vue"
import { logModel } from "@/api/system"

const tableRef = ref()
const crudOptions: CrudOptions = {
  toolbar: {
    show: false
  },
  columns: {
    username: {
      title: "用户",
      type: "text"
    },
    path: {
      title: "接口地址",
      type: "text"
    },
    method: {
      title: "请求方式",
      type: "text"
    },
    status_code: {
      title: "状态码",
      colorAuto: true,
      type: "dict-select",
      dict: dict({
        data: [
          { label: "200", value: 200 },
          { label: "403", value: 403 },
          { label: "404", value: 404 },
          { label: "421", value: 421 },
          { label: "422", value: 422 },
          { label: "500", value: 500 }
        ]
      }),
      search: {
        show: true
      }
    },
    response_time: {
      title: "响应时间",
      type: "text"
    },
    ip: {
      title: "ip",
      type: "text"
    },
    referer: {
      title: "referer",
      type: "text"
    },
    user_agent: {
      title: "user_agent",
      type: "text",
      column: {
        show: false
      }
    },
    summary: {
      title: "接口描述",
      type: "text"
    },
    tags: {
      title: "接口标签",
      type: "text",
      column: {
        order: 0
      }
    }
  }
}
</script>
