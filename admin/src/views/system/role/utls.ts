import { Ref } from "vue"

export function getCheckedValues(obj: Ref) {
  const menuSelected = obj.value.getCheckedKeys()
  let a = obj.value.getHalfCheckedKeys()
  return [...menuSelected, ...a].filter((item) => {
    return item && item !== -1
  })
}

export function filterNodeMenu(value: string, data: any) {
  if (!value) return true
  return data.title.includes(value)
}

export function filterNodeApi(value: string, data: any) {
  if (!value) return true
  return data.summary.includes(value)
}

export function buildApiTree(data: any[]) {
  const processedData = []
  const groupedData = {}
  debugger
  data.forEach((item) => {
    const tags = String(item["tags"])
    const pathParts = item["path"].split("/")
    const path = pathParts.slice(0, -1).join("/")
    const summary = tags.charAt(0).toUpperCase() + tags.slice(1)
    // const unique_id = item["method"].toLowerCase() + item["path"]

    if (!(path in groupedData)) {
      // groupedData[path] = { unique_id: path, path: path, summary: summary, children: [] }
      // @ts-ignore
      groupedData[path] = { id: -1, summary: summary, children: [] }
    }
    // @ts-ignore
    groupedData[path].children.push({
      id: item["id"],
      // path: item["path"],
      // method: item["method"],
      summary: item["summary"]
      // unique_id: unique_id
    })
  })
  processedData.push(...Object.values(groupedData))
  return processedData
}
