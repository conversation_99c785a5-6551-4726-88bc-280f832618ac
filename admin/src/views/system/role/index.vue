<template>
  <div>
    <m-table :crud-options="crudOptions" :model="roleModel" ref="tableRef" />
    <div>
      <m-permission v-model:visible="isShowPermission" :role_id="role_id" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { roleModel } from "@/api/system"
import MTable from "@/components/Table/index.vue"
import MPermission from "./permission.vue"
import { CrudOptions, dict } from "@fast-crud/fast-crud"
import { ref } from "vue"

const tableRef = ref()
const isShowPermission = ref(false)
const role_id = ref(-1)
const crudOptions: CrudOptions = {
  columns: {
    name: {
      title: "角色标识",
      type: "text",
      search: { show: true, component: { placeholder: "请输入角色标识" } }, // 开启查询
      form: {
        rules: [
          { required: true, message: "请输入角色标识" },
          { max: 50, message: "最大50个字符" }
        ]
      }
    },
    title: {
      title: "角色名称",
      type: "text",
      search: { show: true, component: { placeholder: "请输入角色名称" } }, // 开启查询
      form: {
        rules: [
          { required: true, message: "请输入角色名称" },
          { max: 50, message: "最大50个字符" }
        ]
      }
    },
    desc: {
      title: "角色描述",
      type: "text",
      search: { show: true, component: { placeholder: "请输入角色描述" } }, // 开启查询
      form: {
        rules: [
          { required: true, message: "请输入用户名" },
          { max: 50, message: "最大50个字符" }
        ]
      }
    }
  },
  rowHandle: {
    buttons: {
      view: {
        show: false
      },
      permission: {
        // text: "权限",
        show: true,
        icon: "fluent-mdl2:permissions",
        type: "warning",
        click(context) {
          isShowPermission.value = true
          role_id.value = context.row.id
        }
      }
    }
  }
}
</script>
