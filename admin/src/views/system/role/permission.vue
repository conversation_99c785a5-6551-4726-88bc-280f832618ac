<script setup lang="ts">
import { onMounted, Ref, ref, watch } from "vue"
import { apiModel, menuModel, roleModel } from "@/api/system"
import { buildApiTree, filterNodeApi, filterNodeMenu, getCheckedValues } from "./utls"
import { ElMessage } from "element-plus"
import { isMobile } from "@/utils/windows"

const props = defineProps({
  role_id: {
    type: Number,
    required: true
  }
})

const isShowPermission = defineModel("visible", { type: Boolean, default: false })

const activeName = ref("menu")
const filterText = ref("")

const menuRef = ref()
const menuValue: Ref<number[]> = ref([])
const menuData = ref()

const apiRef = ref()
const apiValue: Ref<number[]> = ref([])
const apiData = ref()

async function getApisAndMenus() {
  const [apiResponse, menuResponse] = await Promise.all([apiModel.getPermissionList(), menuModel.getMenuTree()])
  menuData.value = menuResponse
  apiData.value = [
    {
      children: buildApiTree(apiResponse),
      id: -1,
      summary: "根节点"
    }
  ]
}

watch([() => props.role_id, isShowPermission], async ([role_id]) => {
  if (!role_id || role_id === -1 || !isShowPermission.value) return
  if (!menuData.value || !apiData.value) {
    await getApisAndMenus()
  }
  const rolePermissions = await roleModel.getRolePermissions(role_id)
  menuRef.value.setCheckedKeys(
    rolePermissions.menus.map((menuItem) => {
      if (menuItem.menu_type === "catalog") return
      return menuItem.id
    }),
    true
  )
  apiRef.value.setCheckedKeys(rolePermissions.apis.map((apiItem) => apiItem.id))
})

function handleSubmit() {
  roleModel
    .updateRolePermissions({
      id: props.role_id,
      menu_ids: getCheckedValues(menuRef),
      api_ids: getCheckedValues(apiRef)
    })
    .then(() => {
      ElMessage.success("更新成功")
      isShowPermission.value = false
    })
}

watch(filterText, (val) => {
  menuRef.value!.filter(val)
  apiRef.value!.filter(val)
})
</script>

<template>
  <el-drawer
    :size="isMobile ? '70%' : '30%'"
    :show-close="false"
    class="permission-title"
    title="权限设置"
    v-model="isShowPermission"
    :center="true"
  >
    <template #header>
      <div>
        <h4>权限设置</h4>
        <div class="flex items-center gap-col-4">
          <el-input
            clearable
            v-model="filterText"
            size="large"
            class="max-w-2/3"
            placeholder="请输入需要搜索的权限名称"
            type="text"
          ></el-input>
          <el-button size="large" type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </div>
    </template>

    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane label="菜单权限" name="menu">
        <el-tree
          ref="menuRef"
          :default-checked-keys="menuValue"
          :props="{
            label: 'title'
          }"
          default-expand-all
          highlight-current
          node-key="id"
          :data="menuData"
          show-checkbox
          multiple
          :filter-node-method="filterNodeMenu"
        ></el-tree>
      </el-tab-pane>
      <el-tab-pane label="接口权限" name="api">
        <el-tree
          ref="apiRef"
          :default-checked-keys="apiValue"
          :props="{
            label: 'summary'
          }"
          default-expand-all
          highlight-current
          node-key="id"
          :data="apiData"
          show-checkbox
          multiple
          :filter-node-method="filterNodeApi"
        ></el-tree>
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>

<style scoped lang="scss">
.demo-tabs > .el-tabs__content {
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
  overflow-y: auto;
}

.permission-title .el-drawer__header {
  margin-bottom: 0;
}
</style>
