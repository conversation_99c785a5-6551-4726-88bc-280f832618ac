<template>
  <m-table :crud-options="crudOptions" :model="menuModel" ref="tableRef" />
</template>

<script lang="tsx" setup>
import { menuModel } from "@/api/system"
import MTable from "@/components/Table/index.vue"
import { compute, CrudOptions, dict } from "@fast-crud/fast-crud"
import { ref, shallowRef } from "vue"
import { vueComponents } from "@/router/routes"
import { toPathCamelCase } from "@/utils/url"
import { usePermissionStore } from "@/store"
import MenuTree from "./menu-tree.vue"
import { filterRoutes } from "@/router/utils"

const tableRef = ref()
const permissionStore = usePermissionStore()

const crudOptions: CrudOptions = {
  actionbar: {
    buttons: {
      deleteAll: {
        show: false
      },
      enableStatus: {
        show: false
      },
      disableStatus: {
        show: false
      }
    }
  },
  search: {
    show: false
  },
  request: {
    pageRequest: async () => {
      const data = await menuModel.getMenuTree()
      return {
        items: data,
        page: 1,
        pages: 1,
        size: 10,
        total: 10
      }
    }
  },
  columns: {
    menu_type: {
      title: "菜单类型",
      type: "dict-radio",
      dict: dict({
        data: [
          { label: "菜单", value: "menu" },
          { label: "目录", value: "catalog" }
        ]
      }),
      form: {
        value: "catalog"
      }
    },
    hidden: {
      title: "是否隐藏",
      type: "dict-switch",
      dict: dict({
        data: [
          { value: true, label: "是" },
          { value: false, label: "否" }
        ]
      }),
      form: {
        value: false
      },
      search: {
        show: true
      }
    },
    path: {
      title: "路由地址",
      type: "text",
      form: {
        component: {
          placeholder: "请输入路由地址"
        }
      }
    },
    name: {
      title: "name",
      type: "text",
      column: {
        show: false
      },
      form: {
        component: {
          placeholder: "请输入菜单标识（systemUser）"
        },
        value: compute(({ form }) => {
          form.name = toPathCamelCase(form.path)
        })
      }
    },
    title: {
      title: "菜单名称",
      type: "text",
      search: {
        show: true
      },
      form: {
        component: {
          placeholder: "请输入菜单名称"
        }
      }
    },
    icon: {
      title: "图标",
      type: "text",
      form: {
        component: {
          placeholder: "请输入菜单图标"
        },
        helper: {
          render: () => {
            return (
              <a href="https://icon-sets.iconify.design/" target="_blank">
                点我前往iconify查看图标
              </a>
            )
          }
        },
        suffixRender({ form }) {
          return <fs-icon icon={form.icon} />
        }
      },
      column: {
        cellRender(scope) {
          return <fs-icon icon={scope.row.icon} />
        },
        width: 100
      }
    },

    redirect: {
      title: "重定向",
      // type: "dict-select",
      form: {
        component: {
          name: "el-select",
          placeholder: "访问当前链接时重定向地址",
          filterable: true,
          "allow-create": false,
          slots: {
            default({ form }) {
              return permissionStore.routePaths.map((routePath) => {
                return form.path !== routePath ? <el-option key={routePath} label={routePath} value={routePath} /> : ""
              })
            }
          }
        }
      }
    },

    component: {
      title: "组件",
      colorAuto: true,
      type: "dict-select",
      form: {
        component: {
          placeholder: "请选择组件",
          filterable: true,
          disabled: compute(({ form }) => {
            const isCatalog = form.menu_type === "catalog"
            if (isCatalog) {
              form.component = "Layout"
            }
            return isCatalog
          })
        }
      },
      dict: dict({
        // @ts-ignore
        getData: () => {
          return vueComponents.map((item) => {
            return {
              label: item,
              value: item
            }
          })
        }
      })
    },

    parent_id: {
      title: "父级菜单",
      column: {
        show: false
      },
      form: {
        col: { span: 12 },
        rules: [{ required: true, message: "菜单为必选项" }],
        component: {
          name: shallowRef(MenuTree),
          vModel: "modelValue",
          data: compute(({ row }) => {
            return [
              {
                children: filterRoutes(permissionStore.menus, row.path),
                id: 0,
                title: "根节点",
                icon: "lucide:folder-root"
              }
            ]
          })
        }
      }
    },
    order: {
      title: "排序",
      type: "number"
    },
    always_show: {
      title: "always_show",
      type: "dict-switch",
      dict: dict({
        data: [
          { value: true, label: "是" },
          { value: false, label: "否" }
        ]
      }),
      form: {
        col: { span: 8 },
        // @ts-ignore
        show: compute(({ form }) => form.menu_type === "catalog"),
        // @ts-ignore
        submit: compute(({ form }) => form.menu_type === "catalog")
      },
      column: {
        show: false
      }
    },
    keepalive: {
      title: "keepalive",
      type: "dict-switch",
      dict: dict({
        data: [
          { value: true, label: "是" },
          { value: false, label: "否" }
        ]
      }),
      form: {
        value: true,
        col: { span: 8 },
        // @ts-ignore
         show: compute(({ form }) => form.menu_type === "menu"),
        // @ts-ignore
        submit: compute(({ form }) => form.menu_type === "menu")
      }
    }
  },
  form: {
    afterSubmit() {
      location.reload()
      return new Promise(() => true)
    }
  },
  settings: {
    plugins: {
      rowSelection: {
        enabled: false
      }
    }
  }
}
</script>
