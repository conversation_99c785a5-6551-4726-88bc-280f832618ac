<template>
  <el-tree-select
    check-strictly
    highlight-current
    v-model="modelValue"
    node-key="id"
    :props="{ label: 'title' }"
    :data="data"
  >
    <template #label="{ label, value }">
      <span>{{ label }}: </span>
      <span style="font-weight: bold">fdgsg{{ value }}</span>
    </template>
    <template #default="{ data }">
      <div class="flex items-center gap-col-1">
        <fs-icon v-if="data.icon" style="max-width: 14px" :icon="data.icon" />
        <span>{{ data.title }}</span>
      </div>
    </template>
  </el-tree-select>
</template>

<script lang="ts" setup>
import { FsIcon } from "@fast-crud/fast-crud"

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const modelValue = defineModel<number>({ required: true, type: Number })
</script>
