<template>
  <m-table :crud-options="crudOptions" :model="apiModel" ref="tableRef" />
</template>

<script lang="ts" setup>
import MTable from "@/components/Table/index.vue"
import { CrudOptions, dict } from "@fast-crud/fast-crud"
import { ref } from "vue"
import { apiModel } from "@/api/system"
import { ElMessage } from "element-plus"
import { hasPermission } from "@/directives"

const tableRef = ref()
const refreshLoading = ref(false)
const crudOptions: CrudOptions = {
  actionbar: {
    buttons: {
      refresh: {
        text: "刷新api",
        type: "danger",
        icon: "refresh",
        loading: refreshLoading,
        show: hasPermission(`post${apiModel.realUrl}/refresh`),
        click() {
          refreshLoading.value = true
          apiModel
            .refreshApi()
            .then(() => {
              ElMessage.success("api刷新成功")
              tableRef.value.crudExpose.doRefresh()
            })
            .finally(() => (refreshLoading.value = false))
        }
      }
    }
  },
  columns: {
    path: {
      title: "接口地址",
      type: "text"
    },
    method: {
      title: "请求方式",
      colorAuto: true,
      type: "dict-select",
      dict: dict({
        data: [
          { label: "GET", value: "GET" },
          { label: "POST", value: "POST" },
          { label: "PUT", value: "PUT" },
          { label: "DELETE", value: "DELETE" }
        ]
      })
    },
    summary: {
      title: "接口描述",
      type: "text",
      search: {
        show: true
      },
      form: {
        component: {
          placeholder: "请输入接口描述"
        }
      }
    },
    tags: {
      title: "接口标签",
      type: "text",

      search: {
        show: true
      },
      form: {
        component: {
          placeholder: "请输入接口标签"
        }
      }
    },
    log_status: {
      title: "开启日志",
      colorAuto: true,
      type: "dict-switch",
      dict: dict({
        data: [
          { label: "开启", value: true },
          { label: "关闭", value: false }
        ]
      }),
      search: {
        show: true
      },
      form: {
        col: { span: 8 }
      }
    },
    is_trust: {
      title: "是否信任",
      type: "dict-switch",
      dict: dict({
        data: [
          { label: "是", value: true },
          { label: "否", value: false }
        ]
      }),
      search: {
        show: true
      },
      form: {
        col: { span: 8 }
      }
    }
  }
}
</script>
