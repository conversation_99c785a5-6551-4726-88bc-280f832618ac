<template>
  <div class="system-test">
    <el-card class="test-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h2>系统功能测试</h2>
          <el-tag type="success">所有功能正常</el-tag>
        </div>
      </template>

      <!-- 图标测试 -->
      <div class="test-section">
        <h3>图标测试</h3>
        <div class="icon-test-grid">
          <div class="icon-item">
            <el-icon size="24"><Plus /></el-icon>
            <span>Plus</span>
          </div>
          <div class="icon-item">
            <el-icon size="24"><Delete /></el-icon>
            <span>Delete</span>
          </div>
          <div class="icon-item">
            <el-icon size="24"><Edit /></el-icon>
            <span>Edit</span>
          </div>
          <div class="icon-item">
            <el-icon size="24"><View /></el-icon>
            <span>View</span>
          </div>
          <div class="icon-item">
            <el-icon size="24"><Document /></el-icon>
            <span>Document</span>
          </div>
          <div class="icon-item">
            <el-icon size="24"><Folder /></el-icon>
            <span>Folder</span>
          </div>
          <div class="icon-item">
            <el-icon size="24"><Calendar /></el-icon>
            <span>Calendar</span>
          </div>
          <div class="icon-item">
            <el-icon size="24"><Reading /></el-icon>
            <span>Reading</span>
          </div>
          <div class="icon-item">
            <el-icon size="24"><Tools /></el-icon>
            <span>Tools</span>
          </div>
          <div class="icon-item">
            <el-icon size="24"><Upload /></el-icon>
            <span>Upload</span>
          </div>
        </div>
      </div>

      <!-- 组件测试 -->
      <div class="test-section">
        <h3>组件测试</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-button type="primary" @click="testMessage">测试消息</el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="success" @click="testDialog">测试对话框</el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="warning" @click="testNotification">测试通知</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- API测试 -->
      <div class="test-section">
        <h3>API连接测试</h3>
        <div class="api-status">
          <el-tag v-if="apiStatus === 'success'" type="success">
            <el-icon><CircleCheck /></el-icon>
            API连接正常
          </el-tag>
          <el-tag v-else-if="apiStatus === 'error'" type="danger">
            <el-icon><CircleClose /></el-icon>
            API连接失败
          </el-tag>
          <el-tag v-else type="info">
            <el-icon><Loading /></el-icon>
            检测中...
          </el-tag>
        </div>
        <el-button @click="testAPI" :loading="apiTesting">重新测试API</el-button>
      </div>

      <!-- 功能导航 -->
      <div class="test-section">
        <h3>功能导航</h3>
        <el-row :gutter="16">
          <el-col :span="6">
            <el-card class="nav-card" shadow="hover" @click="goToTiku">
              <div class="nav-content">
                <el-icon size="32"><Document /></el-icon>
                <h4>题库管理</h4>
                <p>管理题目和内容</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="nav-card" shadow="hover" @click="goToCategory">
              <div class="nav-content">
                <el-icon size="32"><Folder /></el-icon>
                <h4>分类管理</h4>
                <p>管理题目分类</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="nav-card" shadow="hover" @click="goToSemester">
              <div class="nav-content">
                <el-icon size="32"><Calendar /></el-icon>
                <h4>学期管理</h4>
                <p>管理学期信息</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="nav-card" shadow="hover" @click="goToDashboard">
              <div class="nav-content">
                <el-icon size="32"><DataLine /></el-icon>
                <h4>数据仪表板</h4>
                <p>查看统计数据</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  Plus,
  Delete,
  Edit,
  View,
  Document,
  Folder,
  Calendar,
  Reading,
  Tools,
  Upload,
  CircleCheck,
  CircleClose,
  Loading,
  DataLine
} from '@element-plus/icons-vue'

defineOptions({
  name: 'SystemTest'
})

const router = useRouter()
const apiStatus = ref('loading')
const apiTesting = ref(false)

onMounted(() => {
  testAPI()
})

// 测试消息
const testMessage = () => {
  ElMessage.success('消息组件工作正常！')
}

// 测试对话框
const testDialog = async () => {
  try {
    await ElMessageBox.confirm('这是一个测试对话框', '测试', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    ElMessage.success('对话框组件工作正常！')
  } catch {
    ElMessage.info('对话框已取消')
  }
}

// 测试通知
const testNotification = () => {
  ElNotification({
    title: '测试通知',
    message: '通知组件工作正常！',
    type: 'success',
    duration: 3000
  })
}

// 测试API连接
const testAPI = async () => {
  apiTesting.value = true
  apiStatus.value = 'loading'
  
  try {
    // 这里可以调用一个简单的API来测试连接
    const response = await fetch('/api/health', { method: 'GET' })
    if (response.ok) {
      apiStatus.value = 'success'
    } else {
      apiStatus.value = 'error'
    }
  } catch (error) {
    console.error('API测试失败:', error)
    apiStatus.value = 'error'
  } finally {
    apiTesting.value = false
  }
}

// 导航方法
const goToTiku = () => router.push('/exam/tiku')
const goToCategory = () => router.push('/exam/category')
const goToSemester = () => router.push('/exam/semester')
const goToDashboard = () => router.push('/exam/dashboard')
</script>

<style scoped>
.system-test {
  padding: 20px;
}

.test-card {
  border-radius: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
}

.test-section {
  margin-bottom: 32px;
}

.test-section h3 {
  color: #606266;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e4e7ed;
}

.icon-test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.icon-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.icon-item span {
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
}

.api-status {
  margin-bottom: 16px;
}

.nav-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.nav-content {
  text-align: center;
  padding: 16px;
}

.nav-content h4 {
  margin: 12px 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.nav-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .system-test {
    padding: 16px;
  }
  
  .icon-test-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 12px;
  }
  
  .nav-content {
    padding: 12px;
  }
  
  .nav-content h4 {
    font-size: 14px;
  }
  
  .nav-content p {
    font-size: 12px;
  }
}
</style>
