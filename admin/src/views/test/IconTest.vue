<template>
  <div class="icon-test">
    <h2>图标测试页面</h2>
    <p>测试所有使用的Element Plus图标是否正确加载</p>
    
    <div class="icon-grid">
      <!-- 基础图标 -->
      <div class="icon-item">
        <el-icon size="24"><Plus /></el-icon>
        <span>Plus</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><Delete /></el-icon>
        <span>Delete</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><Edit /></el-icon>
        <span>Edit</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><View /></el-icon>
        <span>View</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><Search /></el-icon>
        <span>Search</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><Refresh /></el-icon>
        <span>Refresh</span>
      </div>
      
      <!-- 文档和文件夹图标 -->
      <div class="icon-item">
        <el-icon size="24"><Document /></el-icon>
        <span>Document</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><DocumentAdd /></el-icon>
        <span>DocumentAdd</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><Folder /></el-icon>
        <span>Folder</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><FolderOpened /></el-icon>
        <span>FolderOpened</span>
      </div>
      
      <!-- 时间和日历图标 -->
      <div class="icon-item">
        <el-icon size="24"><Calendar /></el-icon>
        <span>Calendar</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><Clock /></el-icon>
        <span>Clock</span>
      </div>
      
      <!-- 功能图标 -->
      <div class="icon-item">
        <el-icon size="24"><Reading /></el-icon>
        <span>Reading</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><Upload /></el-icon>
        <span>Upload</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><Download /></el-icon>
        <span>Download</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><Setting /></el-icon>
        <span>Setting</span>
      </div>
      
      <!-- 箭头图标 -->
      <div class="icon-item">
        <el-icon size="24"><ArrowUp /></el-icon>
        <span>ArrowUp</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><ArrowDown /></el-icon>
        <span>ArrowDown</span>
      </div>
      
      <!-- 图表图标 -->
      <div class="icon-item">
        <el-icon size="24"><PieChart /></el-icon>
        <span>PieChart</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><DataLine /></el-icon>
        <span>DataLine</span>
      </div>
      
      <!-- 其他图标 -->
      <div class="icon-item">
        <el-icon size="24"><Tools /></el-icon>
        <span>Tools</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><Lightning /></el-icon>
        <span>Lightning</span>
      </div>
      
      <div class="icon-item">
        <el-icon size="24"><RefreshLeft /></el-icon>
        <span>RefreshLeft</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Plus,
  Delete,
  Edit,
  View,
  Search,
  Refresh,
  Document,
  DocumentAdd,
  Folder,
  FolderOpened,
  Calendar,
  Clock,
  Reading,
  Upload,
  Download,
  Setting,
  ArrowUp,
  ArrowDown,
  PieChart,
  DataLine,
  Tools,
  Lightning,
  RefreshLeft
} from '@element-plus/icons-vue'

defineOptions({
  name: 'IconTest'
})
</script>

<style scoped>
.icon-test {
  padding: 20px;
}

.icon-test h2 {
  color: #303133;
  margin-bottom: 8px;
}

.icon-test p {
  color: #606266;
  margin-bottom: 30px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 20px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.icon-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.icon-item span {
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
  text-align: center;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .icon-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 16px;
  }
  
  .icon-item {
    padding: 12px;
  }
}
</style>
