<template>
  <div class="pinyin-editor">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <el-button-group>
        <el-button size="small" @click="addPinyin" :disabled="!hasSelection">
          <el-icon><Reading /></el-icon>
          添加拼音
        </el-button>
        <el-button size="small" @click="removePinyin" :disabled="!hasSelection">
          <el-icon><Delete /></el-icon>
          移除拼音
        </el-button>
        <el-button size="small" @click="clearAllPinyin">
          <el-icon><RefreshLeft /></el-icon>
          清除所有拼音
        </el-button>
      </el-button-group>
      
      <el-button-group style="margin-left: 12px;">
        <el-button size="small" @click="previewMode = !previewMode">
          <el-icon><View /></el-icon>
          {{ previewMode ? '编辑模式' : '预览模式' }}
        </el-button>
      </el-button-group>
    </div>

    <!-- 编辑器容器 -->
    <div class="editor-container" v-show="!previewMode">
      <div ref="editorContainer" class="editor-content"></div>
    </div>

    <!-- 预览容器 -->
    <div class="preview-container" v-show="previewMode">
      <div class="preview-content" v-html="modelValue"></div>
    </div>

    <!-- 拼音输入对话框 -->
    <el-dialog v-model="pinyinDialogVisible" title="编辑拼音" width="400px">
      <div class="pinyin-input-container">
        <div class="selected-text">
          <label>选中文字：</label>
          <span class="text-highlight">{{ selectedText }}</span>
        </div>
        <div class="pinyin-input">
          <label>拼音注音：</label>
          <el-input
            v-model="pinyinInput"
            placeholder="请输入拼音，多个字用空格分隔"
            @keyup.enter="confirmPinyin"
          />
          <div class="pinyin-hint">
            <el-button size="small" type="primary" @click="autoGeneratePinyin">
              自动生成拼音
            </el-button>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="pinyinDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmPinyin">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Reading, Delete, RefreshLeft, View } from '@element-plus/icons-vue'
import { pinyin } from 'pinyin-pro'

defineOptions({
  name: 'PinyinEditor'
})

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  }
})

const emit = defineEmits(['update:modelValue'])

// 编辑器相关
const editorContainer = ref<HTMLElement>()
const hasSelection = ref(false)
const selectedText = ref('')
const previewMode = ref(false)

// 拼音对话框相关
const pinyinDialogVisible = ref(false)
const pinyinInput = ref('')

// 编辑器实例
let editorInstance: HTMLElement | null = null

onMounted(() => {
  initEditor()
})

onBeforeUnmount(() => {
  cleanup()
})

// 监听内容变化
watch(() => props.modelValue, (newValue) => {
  if (editorInstance && editorInstance.innerHTML !== newValue) {
    editorInstance.innerHTML = newValue || ''
  }
})

// 初始化编辑器
const initEditor = () => {
  if (!editorContainer.value) return

  // 创建可编辑div
  editorInstance = document.createElement('div')
  editorInstance.contentEditable = 'true'
  editorInstance.className = 'editor-instance'
  editorInstance.innerHTML = props.modelValue || ''
  editorInstance.style.cssText = `
    min-height: 300px;
    padding: 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    outline: none;
    line-height: 1.6;
    font-size: 14px;
  `

  // 添加事件监听
  editorInstance.addEventListener('input', handleInput)
  editorInstance.addEventListener('mouseup', handleSelection)
  editorInstance.addEventListener('keyup', handleSelection)

  editorContainer.value.appendChild(editorInstance)
}

// 处理输入事件
const handleInput = () => {
  if (editorInstance) {
    emit('update:modelValue', editorInstance.innerHTML)
  }
}

// 处理选择事件
const handleSelection = () => {
  const selection = window.getSelection()
  if (selection && selection.toString().trim()) {
    selectedText.value = selection.toString().trim()
    hasSelection.value = true
  } else {
    selectedText.value = ''
    hasSelection.value = false
  }
}

// 添加拼音
const addPinyin = () => {
  if (!selectedText.value) {
    ElMessage.warning('请先选择要添加拼音的文字')
    return
  }

  if (!/[\u4e00-\u9fa5]/.test(selectedText.value)) {
    ElMessage.warning('请选择包含中文的文字')
    return
  }

  // 自动生成拼音
  autoGeneratePinyin()
  pinyinDialogVisible.value = true
}

// 自动生成拼音
const autoGeneratePinyin = () => {
  if (!selectedText.value) return

  try {
    const pinyinResult = pinyin(selectedText.value, { 
      type: 'array', 
      toneType: 'symbol' 
    })
    pinyinInput.value = pinyinResult.join(' ')
  } catch (error) {
    console.error('生成拼音失败:', error)
    ElMessage.error('生成拼音失败')
  }
}

// 确认拼音
const confirmPinyin = () => {
  if (!pinyinInput.value.trim()) {
    ElMessage.warning('请输入拼音')
    return
  }

  const selection = window.getSelection()
  if (!selection || !selection.rangeCount) {
    ElMessage.warning('请重新选择文字')
    pinyinDialogVisible.value = false
    return
  }

  try {
    const range = selection.getRangeAt(0)
    const selectedContent = range.toString()
    
    // 生成拼音HTML
    const pinyinArray = pinyinInput.value.trim().split(/\s+/)
    let rubyHTML = ''
    
    for (let i = 0; i < selectedContent.length; i++) {
      const char = selectedContent[i]
      const pinyinChar = pinyinArray[i] || ''
      
      if (/[\u4e00-\u9fa5]/.test(char)) {
        rubyHTML += `<ruby>${char}<rt>${pinyinChar}</rt></ruby>`
      } else {
        rubyHTML += char
      }
    }

    // 替换选中内容
    const rubyElement = document.createElement('span')
    rubyElement.innerHTML = rubyHTML
    range.deleteContents()
    range.insertNode(rubyElement)

    // 清除选择
    selection.removeAllRanges()
    
    // 触发输入事件
    handleInput()
    
    ElMessage.success('拼音添加成功')
  } catch (error) {
    console.error('添加拼音失败:', error)
    ElMessage.error('添加拼音失败')
  }

  pinyinDialogVisible.value = false
  pinyinInput.value = ''
  hasSelection.value = false
  selectedText.value = ''
}

// 移除拼音
const removePinyin = () => {
  if (!selectedText.value) {
    ElMessage.warning('请先选择要移除拼音的文字')
    return
  }

  const selection = window.getSelection()
  if (!selection || !selection.rangeCount) return

  try {
    const range = selection.getRangeAt(0)
    const container = range.commonAncestorContainer

    // 查找包含的ruby元素
    let rubyElements: Element[] = []
    if (container.nodeType === Node.ELEMENT_NODE) {
      rubyElements = Array.from((container as Element).querySelectorAll('ruby'))
    } else if (container.parentElement) {
      rubyElements = Array.from(container.parentElement.querySelectorAll('ruby'))
    }

    // 移除ruby标签，保留文字
    rubyElements.forEach(ruby => {
      const textContent = ruby.textContent || ''
      const textNode = document.createTextNode(textContent)
      ruby.parentNode?.replaceChild(textNode, ruby)
    })

    // 触发输入事件
    handleInput()
    
    ElMessage.success('拼音移除成功')
  } catch (error) {
    console.error('移除拼音失败:', error)
    ElMessage.error('移除拼音失败')
  }

  hasSelection.value = false
  selectedText.value = ''
}

// 清除所有拼音
const clearAllPinyin = () => {
  if (!editorInstance) return

  try {
    const rubyElements = editorInstance.querySelectorAll('ruby')
    rubyElements.forEach(ruby => {
      const textContent = ruby.textContent || ''
      const textNode = document.createTextNode(textContent)
      ruby.parentNode?.replaceChild(textNode, ruby)
    })

    handleInput()
    ElMessage.success('所有拼音已清除')
  } catch (error) {
    console.error('清除拼音失败:', error)
    ElMessage.error('清除拼音失败')
  }
}

// 清理资源
const cleanup = () => {
  if (editorInstance) {
    editorInstance.removeEventListener('input', handleInput)
    editorInstance.removeEventListener('mouseup', handleSelection)
    editorInstance.removeEventListener('keyup', handleSelection)
  }
}

// 暴露方法给父组件
defineExpose({
  getContent: () => editorInstance?.innerHTML || '',
  setContent: (content: string) => {
    if (editorInstance) {
      editorInstance.innerHTML = content
      handleInput()
    }
  },
  focus: () => editorInstance?.focus()
})
</script>

<style scoped>
.pinyin-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
}

.editor-container,
.preview-container {
  min-height: 300px;
}

.preview-content {
  padding: 12px;
  line-height: 1.6;
  font-size: 14px;
}

.pinyin-input-container {
  padding: 16px 0;
}

.selected-text,
.pinyin-input {
  margin-bottom: 16px;
}

.selected-text label,
.pinyin-input label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

.text-highlight {
  background-color: #e6f7ff;
  padding: 4px 8px;
  border-radius: 4px;
  color: #1890ff;
  font-weight: 500;
}

.pinyin-hint {
  margin-top: 8px;
}

/* 拼音样式 */
:deep(ruby) {
  ruby-align: center;
}

:deep(rt) {
  font-size: 0.7em;
  text-align: center;
  color: #666;
}

/* 编辑器实例样式 */
:deep(.editor-instance:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}
</style>
