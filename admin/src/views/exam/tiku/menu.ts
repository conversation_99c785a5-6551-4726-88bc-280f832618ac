import { pinyin } from "pinyin-pro"
import { IDomEditor } from "@wangeditor-next/editor"

export class MyPinyinMenu {
  private title: string;
  private tag: string;
  constructor() {
    this.title = "拼音"
    this.tag = "button"
  }

  getValue() {
    return ""
  }

  isActive() {
    return false
  }

  isDisabled() {
    return false
  }

  exec(editor: IDomEditor) {
    const a = editor.get
    const selection = editor.getSelectionText()
    if (!selection) {
      alert("请先选择要添加拼音的文字")
      return
    }

    // 获取选中文字的拼音并生成带注音的HTML
    const annotatedText = addPinyinAnnotation(selection)
  debugger
    // 替换选中内容
    editor.deleteFragment()
    // debugger
    // editor.dangerouslyInsertHtml(annotatedText)
    editor
  }
}

// 自动添加拼音注音的函数
const addPinyinAnnotation = (text: string) => {
  const pinyinResult = pinyin(text, { type: "array", toneType: "symbol" })
  let result = ""

  // 为每个汉字添加拼音注音
  for (let i = 0; i < text.length; i++) {
    result += `<ruby>${text[i]}<rt>${pinyinResult[i]}</rt></ruby>`
  }

  return result
}
