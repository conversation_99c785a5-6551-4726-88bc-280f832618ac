<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from "vue"
import "@wangeditor/editor/dist/css/style.css"
import { Editor, Toolbar } from "@wangeditor-next/editor-for-vue"
import { Boot } from "@wangeditor/editor"

import { IDomEditor, IEditorConfig, IToolbarConfig } from "@wangeditor/editor"
import { MyPinyinMenu } from "./menu"
import { useUi } from "@fast-crud/fast-crud"

defineOptions({
  name: "MEditor"
})

// 编辑器实例
const editorRef = ref<IDomEditor | null>(null)

// 编辑器内容
// const valueHtml = defineModel()

// 工具栏配置
const toolbarConfig = ref<Partial<IToolbarConfig>>({
  excludeKeys: [],
  insertKeys: {
    index: 0,
    keys: ["add-pinyin"]
  }
})

// 编辑器配置
const editorConfig = ref<Partial<IEditorConfig>>({
  placeholder: "请输入内容..."
})

// 组件销毁时销毁编辑器实例
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor) editor.destroy()
})

// 定义 props 和 emit
const props = defineProps({
  modelValue: {
    type: String,
    default: ""
  }
})

const emit = defineEmits(["update:modelValue"])
const { ui } = useUi()
let formItemContext = ui.formItem.injectFormItemContext()
// 编辑器内容变化时触发
const handleChange = (editor: IDomEditor) => {
  formItemContext.onChange()
}

// 编辑器创建完成时的回调
const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor
  if (!editor.getAllMenuKeys()?.includes("add-pinyin")) {
    //判断如果已经插入进去，不在二次插入
    Boot.registerMenu({
      key: "add-pinyin", // menu key ，唯一。注册之后，需通过 toolbarKeys 配置到工具栏
      factory() {
        return new MyPinyinMenu()
      }
    })
  }
}
defineExpose({
  editorRef
})
</script>

<template>
  <div class="wangeditor-container">
    <Toolbar :editor="editorRef" :defaultConfig="toolbarConfig" style="border-bottom: 1px solid #ccc" />
    <Editor
      :modelValue="props.modelValue"
      :defaultConfig="editorConfig"
      style="height: 500px"
      @onCreated="handleCreated"
      @onChange="handleChange"
    />
  </div>
</template>

<style scoped>
.wangeditor-container {
  border: 1px solid #ccc;
  z-index: 500;
}

:deep(ruby) {
  ruby-align: center;
}

:deep(rt) {
  font-size: 0.7em;
  text-align: center;
}
</style>
