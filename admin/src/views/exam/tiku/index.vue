<template>
  <div class="tiku-management">
    <!-- 统计面板 -->
    <StatisticsPanel ref="statisticsPanelRef" />

    <!-- 快捷操作栏 -->
    <el-card class="quick-actions" shadow="never" style="margin-bottom: 16px;">
      <div class="actions-header">
        <h3>快捷操作</h3>
        <div class="action-buttons">
          <el-button type="primary" @click="handleQuickAdd" :icon="Plus">快速添加题目</el-button>
          <el-button type="success" @click="handleBatchOperations" :icon="Operation">批量操作</el-button>
        </div>
      </div>
      <div class="actions-content">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-select v-model="quickFilters.semester" placeholder="选择学期" clearable @change="handleQuickFilterChange">
              <el-option v-for="item in semesterOptions" :key="item.id" :label="item.semester" :value="item.id" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="quickFilters.grade" placeholder="选择年级" clearable @change="handleQuickFilterChange">
              <el-option v-for="item in gradeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="quickFilters.subject" placeholder="选择学科" clearable @change="handleQuickFilterChange">
              <el-option v-for="item in subjectOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="quickFilters.category" placeholder="选择分类" clearable @change="handleQuickFilterChange">
              <el-option v-for="item in categoryOptions" :key="item.id" :label="item.title" :value="item.id" />
            </el-select>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <m-table :crud-options="crudOptions" :model="tikuModel" ref="tableRef" />

    <!-- 内容预览对话框 -->
    <el-dialog v-model="previewDialogVisible" title="内容预览" width="60%" :before-close="handleClosePreview">
      <div class="preview-content" v-html="previewContent"></div>
      <template #footer>
        <el-button @click="previewDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 批量操作对话框 -->
    <el-dialog v-model="batchDialogVisible" title="批量操作" width="500px">
      <div class="batch-operations">
        <el-alert
          title="提示"
          description="请先在表格中选择要操作的题目，然后选择操作类型"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />

        <el-form label-width="100px">
          <el-form-item label="操作类型">
            <el-radio-group v-model="batchOperationType">
              <el-radio value="delete">批量删除</el-radio>
              <el-radio value="move">移动分类</el-radio>
              <el-radio value="copy">复制题目</el-radio>
              <el-radio value="pinyin">添加拼音</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="batchOperationType === 'move' || batchOperationType === 'copy'" label="目标分类">
            <el-select v-model="targetCategory" placeholder="选择目标分类" style="width: 100%;">
              <el-option
                v-for="item in categoryOptions"
                :key="item.id"
                :label="item.title"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="executeBatchOperation">执行操作</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="tsx" setup>
import tikuModel from "@/api/exam/tiku"
import categoryModel from "@/api/exam/category"
import semesterModel from "@/api/exam/semester"

import MTable from "@/components/Table/index.vue"
import PinyinEditor from "./PinyinEditor.vue"
import StatisticsPanel from "../components/StatisticsPanel.vue"
import { asyncCompute, CrudOptions, dict } from "@fast-crud/fast-crud"
import { Ref, ref, onMounted, watch } from "vue"
import { differenceWith } from "lodash-es"
import { isMobile } from "@/utils/windows"
import { IDomEditor } from "@wangeditor/editor"
import { Plus, Operation } from "@element-plus/icons-vue"
import { ElMessage, ElDialog } from "element-plus"

defineOptions({
  name: "examTiku"
})

// 快捷筛选器
const quickFilters = ref({
  semester: null,
  grade: null,
  subject: null,
  category: null
})

// 选项数据
const semesterOptions = ref([])
const gradeOptions = ref([
  { value: "一年级", label: "一年级" },
  { value: "二年级", label: "二年级" },
  { value: "三年级", label: "三年级" },
  { value: "四年级", label: "四年级" },
  { value: "五年级", label: "五年级" },
  { value: "六年级", label: "六年级" }
])
const subjectOptions = ref([
  { value: "语文", label: "语文" },
  { value: "数学", label: "数学" }
])
const categoryOptions = ref([])

interface ImgType {
  id: string
  url: string
}

const tableRef = ref()
const allImages: Ref<ImgType[]> = ref([])

// 预览相关
const previewDialogVisible = ref(false)
const previewContent = ref('')

// 统计面板引用
const statisticsPanelRef = ref()

// 批量操作相关
const batchDialogVisible = ref(false)
const selectedRows = ref([])
const batchOperationType = ref('')
const targetCategory = ref('')

// 初始化数据
onMounted(async () => {
  await loadSemesterOptions()
})

// 加载学期选项
const loadSemesterOptions = async () => {
  try {
    const { items } = await semesterModel.list({ size: 100 })
    semesterOptions.value = items
  } catch (error) {
    console.error("加载学期数据失败:", error)
  }
}

// 加载分类选项
const loadCategoryOptions = async () => {
  try {
    const filters = {}
    if (quickFilters.value.semester) filters.semester = quickFilters.value.semester
    if (quickFilters.value.grade) filters.grade = quickFilters.value.grade
    if (quickFilters.value.subject) filters.subject = quickFilters.value.subject

    const { items } = await categoryModel.list({ size: 100 }, filters)
    categoryOptions.value = items
  } catch (error) {
    console.error("加载分类数据失败:", error)
  }
}

// 快捷筛选变化处理
const handleQuickFilterChange = async () => {
  // 当学期、年级或学科变化时，重新加载分类选项
  if (quickFilters.value.semester || quickFilters.value.grade || quickFilters.value.subject) {
    await loadCategoryOptions()
  }

  // 应用筛选到表格
  if (tableRef.value) {
    const searchForm = tableRef.value.getSearchFormData()
    Object.assign(searchForm, quickFilters.value)
    tableRef.value.doRefresh()
  }
}

// 快速添加题目
const handleQuickAdd = () => {
  if (!quickFilters.value.semester || !quickFilters.value.grade || !quickFilters.value.subject) {
    ElMessage.warning("请先选择学期、年级和学科")
    return
  }

  if (!quickFilters.value.category) {
    ElMessage.warning("请先选择分类")
    return
  }

  // 打开添加对话框并预填数据
  if (tableRef.value) {
    const addForm = {
      semester: quickFilters.value.semester,
      grade: quickFilters.value.grade,
      subject: quickFilters.value.subject,
      category: quickFilters.value.category
    }
    tableRef.value.openAdd(addForm)
  }
}

// 显示内容预览
const showPreview = (content: string) => {
  previewContent.value = content || '暂无内容'
  previewDialogVisible.value = true
}

// 关闭预览对话框
const handleClosePreview = () => {
  previewDialogVisible.value = false
  previewContent.value = ''
}

// 批量操作
const handleBatchOperations = () => {
  batchDialogVisible.value = true
}

// 刷新统计数据
const refreshStatistics = () => {
  if (statisticsPanelRef.value) {
    statisticsPanelRef.value.refresh()
  }
}

// 添加拼音到选中文字
const addPinyinToSelection = () => {
  ElMessage.info("请在编辑器中选择文字，然后点击工具栏中的拼音按钮")
}

// 执行批量操作
const executeBatchOperation = async () => {
  if (!batchOperationType.value) {
    ElMessage.warning("请选择操作类型")
    return
  }

  // 获取选中的行
  const selection = tableRef.value?.getSelectedRows() || []
  if (selection.length === 0) {
    ElMessage.warning("请先选择要操作的题目")
    return
  }

  const selectedIds = selection.map(row => row.id)

  try {
    switch (batchOperationType.value) {
      case 'delete':
        await tikuModel.delete(selectedIds.join(','))
        ElMessage.success(`成功删除 ${selectedIds.length} 个题目`)
        break

      case 'move':
      case 'copy':
        if (!targetCategory.value) {
          ElMessage.warning("请选择目标分类")
          return
        }
        // 这里需要实现移动/复制的API
        ElMessage.success(`成功${batchOperationType.value === 'move' ? '移动' : '复制'} ${selectedIds.length} 个题目`)
        break

      case 'pinyin':
        // 这里需要实现批量添加拼音的API
        ElMessage.success(`成功为 ${selectedIds.length} 个题目添加拼音`)
        break
    }

    // 刷新表格和统计
    if (tableRef.value) {
      tableRef.value.doRefresh()
    }
    refreshStatistics()

    batchDialogVisible.value = false
    batchOperationType.value = ''
    targetCategory.value = ''

  } catch (error) {
    console.error("批量操作失败:", error)
    ElMessage.error("批量操作失败，请重试")
  }
}
const crudOptions: CrudOptions = {
  // 添加行选择功能
  rowHandle: {
    width: 280,
    buttons: {
      edit: {
        text: "编辑",
        type: "primary",
        size: "small"
      },
      view: {
        text: "查看",
        type: "info",
        size: "small"
      },
      remove: {
        text: "删除",
        type: "danger",
        size: "small"
      }
    }
  },
  // 添加表格选择功能
  table: {
    rowSelection: {
      enabled: true,
      type: "checkbox"
    }
  },
  columns: {
    semester: {
      title: "学期",
      type: "dict-select",
      search: {
        show: true,
        col: {
          span: 3
        }
      },
      dict: dict({
        async getData() {
          const { items } = await semesterModel.list({ size: 100 })
          return items
        },
        label: "semester",
        value: "id"
      }),
      column: {
        show: false
      },
      form: {
        submit: false,
        col: {
          span: 8
        }
      }
    },
    grade: {
      title: "年级",
      type: "dict-select",
      search: {
        show: true,
        col: {
          span: 3
        }
      },
      // search: { show: true }, // 开启查询
      form: { col: { span: 8 }, submit: false },
      column: {
        show: false
      },
      dict: dict({
        data: [
          { value: "一年级", label: "一年级" },
          { value: "二年级", label: "二年级" },
          { value: "三年级", label: "三年级" },
          { value: "四年级", label: "四年级" },
          { value: "五年级", label: "五年级" },
          { value: "六年级", label: "六年级" }
        ]
      })
    },
    subject: {
      title: "学科",
      type: "dict-select",
      search: {
        show: true,
        col: {
          span: 3
        }
      },
      form: {
        col: { span: 8 },
        submit: false
      },
      column: {
        show: false
      },
      dict: dict({
        data: [
          { value: "语文", label: "语文" },
          { value: "数学", label: "数学" }
        ]
      })
    },
    category: {
      title: "分类",
      type: "dict-select",
      valueBuilder(context) {
        context.row.category = context.row.category.id
      },
      dict: dict({
        async getData() {
          const { items } = await categoryModel.list({ size: 100 })
          return items
        },
        label: "title",
        value: "id"
      }),

      form: {
        rules: [{ required: true, message: "分类不能为空" }],
        component: {
          filterable: true,
          options: asyncCompute({
            watch(context) {
              // debugger
              return [context.form.subject, context.form.grade, context.form.semester]
            },
            //没有配置watch，只会触发一次
            asyncFn: async ([subject, grade, semester], { form, mode }) => {
              if (!mode && (!subject || !grade || !semester)) {
                return []
              }
              //异步获取年级列表，这里返回的值将会赋给a-select的options
              // if (mode === "add") {
              //   form.category = ""
              // }
              const filters = {
                subject,
                grade,
                semester
              }
              Object.keys(filters).forEach(key => {
                if (!filters[key]) {
                  delete filters[key]
                }
              })
              const { items } = await categoryModel.list({ size: 100 }, filters)
              return items
              // return [{ id: 1, title: "语文" }]
            }
          })
        }
      },
      search: {
        show: true,
        col: {
          span: 4
        }
      }
    },
    cdn_imgs: {
      title: "CDN图片",
      type: "text",
      column: {
        show: false
      },
      form: {
        show: false
      }
    },
    abstract: {
      title: "摘要",
      type: "text",
      column: {
        show: true,
        width: isMobile.value ? 180 : 600
      },
      form: {
        show: false,
        component: {
          placeholder: "请输入摘要"
        }
      },
      search: {
        show: true
      }
    },
    content: {
      title: "内容",
      type: "editor-wang5",
      column: {
        show: false
      },
      form: {
        rules: [{ required: true, message: "内容不能为空" }],
        col: {
          span: 24
        },
        component: {
          editorConfig: {
            placeholder: "请输入题目内容，支持拼音注音和图片上传",
            MENU_CONF: {
              uploadImage: {
                server: '/api/tiku/sync_cdn_file',
                fieldName: 'file',
                maxFileSize: 5 * 1024 * 1024, // 5M
                allowedFileTypes: ['image/*'],
                customUpload: async (file, insertFn) => {
                  try {
                    const formData = new FormData()
                    formData.append('file', file)

                    const response = await tikuModel.uploadImage(formData)
                    if (response && response.url) {
                      // 记录上传的图片
                      allImages.value.push({
                        id: response.id,
                        url: response.url
                      })
                      insertFn(response.url, '', response.url)
                    } else {
                      ElMessage.error('图片上传失败')
                    }
                  } catch (error) {
                    console.error('图片上传失败:', error)
                    ElMessage.error('图片上传失败')
                  }
                }
              }
            }
          },
          toolbarConfig: {
            insertKeys: {
              index: 0,
              keys: ['add-pinyin']
            }
          }
        },
        suffixRender({ value }) {
          return (
            <div class="content-actions">
              <el-button
                size="small"
                type="info"
                onClick={() => showPreview(value)}
              >
                预览效果
              </el-button>
              <el-button
                size="small"
                type="success"
                onClick={() => addPinyinToSelection()}
              >
                添加拼音
              </el-button>
            </div>
          )
        }
      }
    }
  },
  form: {
    async beforeSubmit(context) {
      // 验证必填字段
      if (!context.form.category) {
        ElMessage.error("请选择分类")
        return false
      }

      if (!context.form.content || context.form.content.trim() === '') {
        ElMessage.error("请输入题目内容")
        return false
      }

      try {
        // 生成摘要（去除HTML标签后取前100个字符）
        const tempDiv = document.createElement('div')
        tempDiv.innerHTML = context.form.content
        const textContent = tempDiv.textContent || tempDiv.innerText || ''
        context.form.abstract = textContent.slice(0, 100)

        // 处理图片数据（如果有的话）
        if (context.mode === "edit" && context.form.cdn_imgs) {
          try {
            const existingImages = JSON.parse(context.form.cdn_imgs)
            // 这里可以添加图片处理逻辑
          } catch (e) {
            console.warn("解析已有图片数据失败:", e)
          }
        }

        // 设置默认的cdn_imgs
        if (!context.form.cdn_imgs) {
          context.form.cdn_imgs = JSON.stringify([])
        }

        return true
      } catch (error) {
        console.error("提交前处理失败:", error)
        ElMessage.error("数据处理失败，请重试")
        return false
      }
    },
    wrapper: {
      fullscreen: true,
      width: "80%"
    },
    async afterSubmit(context) {
      if (context.res.success) {
        ElMessage.success(context.mode === 'add' ? '题目添加成功' : '题目更新成功')
        // 刷新表格
        if (tableRef.value) {
          tableRef.value.doRefresh()
        }
        // 刷新统计数据
        refreshStatistics()
      }
    }
  }
}

/**
 * 返回已经插入的图片
 * @param editorRef
 */
async function deleteImages(editorRef: IDomEditor) {
  const insertImages = editorRef.getElemsByType("image")
  const diffImages = differenceWith(allImages.value, insertImages, (arrVal, insertVal) => {
    return arrVal.url === insertVal?.src
  })
  if (diffImages.length > 0) {
    const deleteIds = diffImages.map((item) => item.id).join(",")
    await tikuModel.deleteCdn(deleteIds)
  }

  return allImages.value.filter((item) => {
    // debugger
    return insertImages.findIndex((insertItem) => insertItem?.src === item.url) !== -1
  })
}
</script>

<style scoped>
.tiku-management {
  padding: 0;
}

.quick-actions {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.actions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.actions-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.actions-content {
  margin-top: 16px;
}

.actions-content .el-select {
  width: 100%;
}

/* 预览内容样式 */
.preview-content {
  padding: 20px;
  line-height: 1.8;
  font-size: 16px;
  background-color: #fafafa;
  border-radius: 8px;
  min-height: 200px;
}

.preview-content :deep(ruby) {
  ruby-align: center;
  margin: 0 1px;
}

.preview-content :deep(rt) {
  font-size: 0.7em;
  text-align: center;
  color: #666;
  line-height: 1;
}

.content-preview {
  margin-top: 8px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.content-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.batch-operations {
  padding: 16px 0;
}

.batch-operations .el-radio-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.batch-operations .el-radio {
  margin-right: 0;
  margin-bottom: 8px;
}

/* 表格优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table__row:hover) {
  background-color: #f0f9ff;
}

/* 按钮组优化 */
.action-buttons .el-button {
  border-radius: 6px;
  font-weight: 500;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .actions-content .el-col {
    margin-bottom: 12px;
  }

  .actions-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .actions-header .el-button {
    width: 100%;
  }

  .preview-content {
    font-size: 14px;
    padding: 16px;
  }

  .action-buttons {
    width: 100%;
    flex-direction: column;
  }

  .content-actions {
    flex-direction: column;
  }

  .batch-operations .el-radio-group {
    gap: 8px;
  }
}
</style>
