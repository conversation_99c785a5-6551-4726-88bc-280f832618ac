<template>
  <div class="tiku-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>题库管理</h1>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleAdd" :icon="Plus">添加题目</el-button>
        <el-button type="success" @click="handleBatchOperations" :icon="Tools">批量操作</el-button>
      </div>
    </div>

    <!-- 筛选栏 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="filters" inline class="filter-form">
        <el-form-item label="学期">
          <el-select v-model="filters.semester" placeholder="选择学期" clearable @change="handleSearch" style="width: 160px;">
            <el-option v-for="item in semesterOptions" :key="item.id" :label="item.semester" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="年级">
          <el-select v-model="filters.grade" placeholder="选择年级" clearable @change="handleSearch" style="width: 120px;">
            <el-option v-for="item in gradeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="学科">
          <el-select v-model="filters.subject" placeholder="选择学科" clearable @change="handleSearch" style="width: 120px;">
            <el-option v-for="item in subjectOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="filters.category" placeholder="选择分类" clearable @change="handleSearch" style="width: 160px;">
            <el-option v-for="item in categoryOptions" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词">
          <el-input v-model="filters.keyword" placeholder="搜索题目内容" clearable @change="handleSearch" style="width: 200px;" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="table-header">
          <span>题目列表</span>
          <div class="table-actions">
            <el-button size="small" @click="handleRefresh" :icon="Refresh">刷新</el-button>
            <el-button size="small" @click="handleExport" :icon="Download">导出</el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f8f9fa', color: '#606266', fontWeight: '600' }"
        :row-style="{ height: '60px' }"
        :cell-style="{ padding: '12px 0' }"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="abstract" label="摘要" min-width="300" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="abstract-cell">
              {{ row.abstract || '暂无摘要' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="category_title" label="分类" width="180" show-overflow-tooltip />
        <el-table-column prop="grade" label="年级" width="100" align="center" />
        <el-table-column prop="subject" label="学科" width="100" align="center" />
        <el-table-column label="拼音" width="100" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.has_pinyin" type="success" size="small">
              <el-icon><Reading /></el-icon>
              有拼音
            </el-tag>
            <el-tag v-else type="info" size="small">无拼音</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="160" align="center">
          <template #default="{ row }">
            <div class="time-cell">
              {{ formatTime(row.create_time) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button size="small" type="primary" @click="handleView(row)" :icon="View">查看</el-button>
              <el-button size="small" type="warning" @click="handleEdit(row)" :icon="Edit">编辑</el-button>
              <el-button size="small" type="danger" @click="handleDelete(row)" :icon="Delete">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'add' ? '添加题目' : '编辑题目'"
      width="80%"
      :before-close="handleDialogClose"
    >
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="100px">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="学期" prop="semester">
              <el-select v-model="formData.semester" placeholder="选择学期" style="width: 100%;" @change="handleFormChange">
                <el-option v-for="item in semesterOptions" :key="item.id" :label="item.semester" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="年级" prop="grade">
              <el-select v-model="formData.grade" placeholder="选择年级" style="width: 100%;" @change="handleFormChange">
                <el-option v-for="item in gradeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="学科" prop="subject">
              <el-select v-model="formData.subject" placeholder="选择学科" style="width: 100%;" @change="handleFormChange">
                <el-option v-for="item in subjectOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="分类" prop="category">
              <el-select v-model="formData.category" placeholder="选择分类" style="width: 100%;">
                <el-option v-for="item in formCategoryOptions" :key="item.id" :label="item.title" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="题目内容" prop="content">
              <RichEditor
                v-model="formData.content"
                height="400px"
                placeholder="请输入题目内容，支持拼音注音和图片上传..."
                @change="handleContentChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ dialogMode === 'add' ? '添加' : '更新' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 查看对话框 -->
    <el-dialog v-model="viewDialogVisible" title="查看题目" width="60%">
      <div class="view-content" v-html="viewContent"></div>
      <template #footer>
        <el-button @click="viewDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 批量操作对话框 -->
    <el-dialog v-model="batchDialogVisible" title="批量操作" width="500px">
      <div class="batch-operations">
        <el-alert
          title="提示"
          :description="`已选择 ${selectedRows.length} 个题目`"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />

        <el-form label-width="100px">
          <el-form-item label="操作类型">
            <el-radio-group v-model="batchOperationType">
              <el-radio value="delete">批量删除</el-radio>
              <el-radio value="move">移动分类</el-radio>
              <el-radio value="copy">复制题目</el-radio>
              <el-radio value="pinyin">添加拼音</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="batchOperationType === 'move' || batchOperationType === 'copy'" label="目标分类">
            <el-select v-model="targetCategory" placeholder="选择目标分类" style="width: 100%;">
              <el-option
                v-for="item in categoryOptions"
                :key="item.id"
                :label="item.title"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="executeBatchOperation" :loading="batchProcessing">执行操作</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Tools,
  Search,
  Refresh,
  Download,
  View,
  Edit,
  Delete,
  Document,
  Reading,
  Folder,
  Calendar,
  Upload
} from '@element-plus/icons-vue'
import tikuModel from '@/api/exam/tiku'
import categoryModel from '@/api/exam/category'
import semesterModel from '@/api/exam/semester'
import RichEditor from '@/components/RichEditor/index.vue'
import { pinyin } from 'pinyin-pro'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

defineOptions({
  name: "examTiku"
})

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const batchProcessing = ref(false)

// 表格数据
const tableData = ref([])
const selectedRows = ref([])

// 分页数据
const pagination = ref({
  page: 1,
  size: 20,
  total: 0
})

// 筛选数据
const filters = ref({
  semester: null,
  grade: null,
  subject: null,
  category: null,
  keyword: ''
})

// 统计数据
const statistics = ref({
  total: 0,
  pinyin: 0,
  categories: 0,
  today: 0
})

// 对话框状态
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const batchDialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit'

// 表单数据
const formData = ref({
  id: null,
  semester: null,
  grade: null,
  subject: null,
  category: null,
  content: '',
  abstract: ''
})

const formRef = ref()
const editorRef = ref()

// 选项数据
const semesterOptions = ref([])
const categoryOptions = ref([])
const formCategoryOptions = ref([])
const gradeOptions = ref([
  { value: "一年级", label: "一年级" },
  { value: "二年级", label: "二年级" },
  { value: "三年级", label: "三年级" },
  { value: "四年级", label: "四年级" },
  { value: "五年级", label: "五年级" },
  { value: "六年级", label: "六年级" }
])
const subjectOptions = ref([
  { value: "语文", label: "语文" },
  { value: "数学", label: "数学" }
])

// 批量操作
const batchOperationType = ref('')
const targetCategory = ref('')

// 查看内容
const viewContent = ref('')

// 表单验证规则
const formRules = {
  semester: [{ required: true, message: '请选择学期', trigger: 'change' }],
  grade: [{ required: true, message: '请选择年级', trigger: 'change' }],
  subject: [{ required: true, message: '请选择学科', trigger: 'change' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  content: [{ required: true, message: '请输入题目内容', trigger: 'blur' }]
}

// 上传配置
const uploadUrl = computed(() => '/api/tiku/sync_cdn_file')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${localStorage.getItem('access_token') || ''}`
}))

// 初始化
onMounted(() => {
  loadSemesterOptions()
  loadCategoryOptions()
  loadTableData()
  loadStatistics()
})

// 加载学期选项
const loadSemesterOptions = async () => {
  try {
    const { items } = await semesterModel.list({ size: 100 })
    semesterOptions.value = items
  } catch (error) {
    console.error('加载学期数据失败:', error)
  }
}

// 加载分类选项
const loadCategoryOptions = async () => {
  try {
    const { items } = await categoryModel.list({ size: 100 })
    categoryOptions.value = items
  } catch (error) {
    console.error('加载分类数据失败:', error)
  }
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.page,
      size: pagination.value.size,
      ...filters.value
    }

    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === null || params[key] === '') {
        delete params[key]
      }
    })

    const response = await tikuModel.list(params)
    tableData.value = response.items || []
    pagination.value.total = response.total || 0

    // 处理数据，添加额外字段
    tableData.value = tableData.value.map(item => ({
      ...item,
      has_pinyin: item.content?.includes('<ruby>') || false,
      category_title: item.category?.title || '未分类'
    }))

  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const stats = await tikuModel.getStatistics()
    statistics.value = {
      total: stats.tikus || 0,
      pinyin: stats.pinyinTikus || 0,
      categories: stats.categories || 0,
      today: stats.todayTikus || 0
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.value.page = 1
  loadTableData()
}

// 重置筛选
const handleReset = () => {
  filters.value = {
    semester: null,
    grade: null,
    subject: null,
    category: null,
    keyword: ''
  }
  handleSearch()
}

// 刷新
const handleRefresh = () => {
  loadTableData()
  loadStatistics()
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1
  loadTableData()
}

const handleCurrentChange = (page: number) => {
  pagination.value.page = page
  loadTableData()
}

// 表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 时间格式化
const formatTime = (time: string) => {
  if (!time) return '未知'
  try {
    return formatDistanceToNow(new Date(time), {
      addSuffix: true,
      locale: zhCN
    })
  } catch (error) {
    return '时间格式错误'
  }
}

// CRUD操作
const handleAdd = () => {
  dialogMode.value = 'add'
  formData.value = {
    id: null,
    semester: filters.value.semester,
    grade: filters.value.grade,
    subject: filters.value.subject,
    category: filters.value.category,
    content: '',
    abstract: ''
  }
  loadFormCategoryOptions()
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  dialogMode.value = 'edit'
  formData.value = {
    id: row.id,
    semester: row.semester,
    grade: row.grade,
    subject: row.subject,
    category: row.category?.id || row.category,
    content: row.content || '',
    abstract: row.abstract || ''
  }
  loadFormCategoryOptions()
  dialogVisible.value = true
}

const handleView = (row: any) => {
  viewContent.value = row.content || '暂无内容'
  viewDialogVisible.value = true
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除题目"${row.abstract || '未命名'}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await tikuModel.delete(row.id)
    ElMessage.success('删除成功')
    loadTableData()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 表单相关
const loadFormCategoryOptions = async () => {
  try {
    const params = {}
    if (formData.value.semester) params.semester = formData.value.semester
    if (formData.value.grade) params.grade = formData.value.grade
    if (formData.value.subject) params.subject = formData.value.subject

    const { items } = await categoryModel.list({ size: 100 }, params)
    formCategoryOptions.value = items
  } catch (error) {
    console.error('加载表单分类选项失败:', error)
  }
}

const handleFormChange = () => {
  // 当学期、年级、学科变化时重新加载分类选项
  formData.value.category = null
  loadFormCategoryOptions()
}

const handleDialogClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 生成摘要
    if (formData.value.content) {
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = formData.value.content
      formData.value.abstract = (tempDiv.textContent || tempDiv.innerText || '').slice(0, 100)
    }

    const submitData = {
      ...formData.value,
      cdn_imgs: JSON.stringify([]) // 暂时设为空数组
    }

    if (dialogMode.value === 'add') {
      await tikuModel.create(submitData)
      ElMessage.success('添加成功')
    } else {
      await tikuModel.update(submitData.id, submitData)
      ElMessage.success('更新成功')
    }

    handleDialogClose()
    loadTableData()
    loadStatistics()

  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 编辑器相关
const handleContentChange = (content: string) => {
  formData.value.content = content
}

// 批量操作
const handleBatchOperations = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要操作的题目')
    return
  }
  batchDialogVisible.value = true
}

const executeBatchOperation = async () => {
  if (!batchOperationType.value) {
    ElMessage.warning('请选择操作类型')
    return
  }

  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要操作的题目')
    return
  }

  try {
    batchProcessing.value = true
    const selectedIds = selectedRows.value.map(row => row.id)

    switch (batchOperationType.value) {
      case 'delete':
        await ElMessageBox.confirm(
          `确定要删除选中的 ${selectedIds.length} 个题目吗？`,
          '确认批量删除',
          { type: 'warning' }
        )

        for (const id of selectedIds) {
          await tikuModel.delete(id)
        }
        ElMessage.success(`成功删除 ${selectedIds.length} 个题目`)
        break

      case 'move':
      case 'copy':
        if (!targetCategory.value) {
          ElMessage.warning('请选择目标分类')
          return
        }

        for (const row of selectedRows.value) {
          const data = { ...row, category: targetCategory.value }
          if (batchOperationType.value === 'copy') {
            delete data.id
            await tikuModel.create(data)
          } else {
            await tikuModel.update(row.id, data)
          }
        }

        ElMessage.success(`成功${batchOperationType.value === 'move' ? '移动' : '复制'} ${selectedIds.length} 个题目`)
        break

      case 'pinyin':
        // 为选中的题目自动添加拼音
        for (const row of selectedRows.value) {
          if (row.content && !row.content.includes('<ruby>')) {
            // 简单的拼音添加逻辑
            let content = row.content
            const chineseRegex = /[\u4e00-\u9fa5]/g
            content = content.replace(chineseRegex, (match) => {
              try {
                const pinyinResult = pinyin(match, { type: 'array', toneType: 'symbol' })
                return `<ruby>${match}<rt>${pinyinResult[0] || ''}</rt></ruby>`
              } catch {
                return match
              }
            })

            await tikuModel.update(row.id, { ...row, content })
          }
        }
        ElMessage.success(`成功为 ${selectedIds.length} 个题目添加拼音`)
        break
    }

    batchDialogVisible.value = false
    batchOperationType.value = ''
    targetCategory.value = ''
    loadTableData()
    loadStatistics()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量操作失败:', error)
      ElMessage.error('批量操作失败')
    }
  } finally {
    batchProcessing.value = false
  }
}

// 导出功能
const handleExport = async () => {
  try {
    ElMessage.info('导出功能开发中...')
    // 这里可以实现导出逻辑
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}
</script>

<style scoped>
.tiku-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  position: relative;
  border-radius: 12px;
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.stat-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 32px;
  color: #409eff;
  opacity: 0.3;
}

/* 筛选卡片 */
.filter-card {
  margin-bottom: 24px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-card :deep(.el-card__body) {
  padding: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.filter-form .el-form-item {
  margin-bottom: 0;
}

.filter-form .el-select {
  min-width: 120px;
}

/* 表格卡片 */
.table-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header span {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #ebeef5;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table__row:hover) {
  background-color: #f0f9ff;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

.abstract-cell {
  line-height: 1.4;
  max-height: 3.6em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.time-cell {
  font-size: 12px;
  color: #909399;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: nowrap;
}

.action-buttons .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* 编辑器样式 */
.editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.editor-content {
  min-height: 300px;
  padding: 16px;
  outline: none;
  line-height: 1.6;
  font-size: 14px;
}

.editor-content:focus {
  background-color: #fafafa;
}

.editor-content[contenteditable]:empty::before {
  content: attr(placeholder);
  color: #c0c4cc;
}

/* 拼音样式 */
.view-content :deep(ruby) {
  ruby-align: center;
  margin: 0 1px;
}

.view-content :deep(rt) {
  font-size: 0.7em;
  text-align: center;
  color: #666;
  line-height: 1;
}

/* 批量操作 */
.batch-operations {
  padding: 16px 0;
}

.batch-operations .el-radio-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.batch-operations .el-radio {
  margin-right: 0;
  margin-bottom: 8px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .tiku-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    flex-direction: column;
  }

  .stats-row .el-col {
    margin-bottom: 16px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .editor-toolbar {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
