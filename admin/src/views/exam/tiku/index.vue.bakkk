<template>
  <m-table :crud-options="crudOptions" :model="tikuModel" ref="tableRef" />
</template>

<script lang="tsx" setup>
import tikuModel from "@/api/exam/tiku"
import categoryModel from "@/api/exam/category"
import semesterModel from "@/api/exam/semester"

import MTable from "@/components/Table/index.vue"
import { asyncCompute, CrudOptions, dict } from "@fast-crud/fast-crud"
import { Ref, ref } from "vue"
import { Editor } from "@wangeditor/editor-for-vue"
import { differenceWith } from "lodash-es"
import { isMobile } from "@/utils/windows"
import MPhoneView from "@/views/exam/components/phone-view.vue"
import MEditor from "./MEditor.vue";

interface ImgType {
  id: string
  url: string
}

const tableRef = ref()
const allImages: Ref<ImgType[]> = ref([])
const crudOptions: CrudOptions = {
  columns: {
    semester: {
      title: "学期",
      type: "dict-select",
      search: {
        show: true,
        col: {
          span: 3
        }
      },
      dict: dict({
        async getData() {
          const { items } = await semesterModel.list({ size: 100 }, { status: true })
          return items
        },
        label: "semester",
        value: "id"
      }),
      column: {
        show: false
      },
      form: {
        submit: false,
        col: {
          span: 8
        }
      }
    },
    grade: {
      title: "年级",
      type: "dict-select",
      search: {
        show: true,
        col: {
          span: 3
        }
      },
      // search: { show: true }, // 开启查询
      form: { col: { span: 8 }, submit: false },
      column: {
        show: false
      },
      dict: dict({
        data: [
          { value: "一年级", label: "一年级" },
          { value: "二年级", label: "二年级" },
          { value: "三年级", label: "三年级" },
          { value: "四年级", label: "四年级" },
          { value: "五年级", label: "五年级" },
          { value: "六年级", label: "六年级" }
        ]
      })
    },
    subject: {
      title: "学科",
      type: "dict-select",
      search: {
        show: true,
        col: {
          span: 3
        }
      },
      form: {
        col: { span: 8 },
        submit: false
      },
      column: {
        show: false
      },
      dict: dict({
        data: [
          { value: "语文", label: "语文" },
          { value: "数学", label: "数学" }
        ]
      })
    },
    category: {
      title: "分类",
      type: "dict-select",
      valueBuilder(context) {
        context.row.category = context.row.category.id
      },
      dict: dict({
        async getData() {
          const { items } = await categoryModel.list({ size: 100 })
          return items
        },
        label: "title",
        value: "id"
      }),

      form: {
        rules: [{ required: true, message: "分类不能为空" }],
        component: {
          filterable: true,
          options: asyncCompute({
            watch(context) {
              // debugger
              return [context.form.subject, context.form.grade, context.form.semester]
            },
            //没有配置watch，只会触发一次
            asyncFn: async ([subject, grade, semester], { form, mode }) => {
              // debugger
              if (!mode && !subject || !grade || !semester) {
                return []
              }
              //异步获取年级列表，这里返回的值将会赋给a-select的options
              // if (mode === "add") {
              //   form.category = ""
              // }
              const { items } = await categoryModel.list({ size: 100 }, { subject, grade, semester })
              return items
              // return [{ id: 1, title: "语文" }]
            }
          })
        }
      },
      search: {
        show: true,
        col: {
          span: 4
        }
      }
    },
    cdn_imgs: {
      title: "CDN图片",
      type: "text",
      column: {
        show: false
      },
      form: {
        show: false
      }
    },
    abstract: {
      title: "摘要",
      type: "text",
      column: {
        show: true,
        width: isMobile.value ? 180 : 600
      },
      form: {
        show: false,
        component: {
          placeholder: "请输入摘要"
        }
      },
      search: {
        show: true
      }
    },
    content: {
      title: "内容",
      column: {
        show: false,
      },
      form: {
        rules: [{ required: true, message: "内容不能为空" }],
        col:{
          span: 24
        },
        component: {
          name:MEditor,
          VModel:'modelValue'
        }
        // suffixRender({ value, getComponentRef }) {
        //   return <MPhoneView content={value} />
        // }
      }
    }
  },
  form: {
    async beforeSubmit(context) {
      const contentRef = await context.getComponentRef("content")
            // debugger

      // console.log(editorRef.getText())
      // 默认只取前100个字符
      context.form.abstract = contentRef.editorRef.getText()?.slice(0, 100)
      // debugger
      if (context.mode === "edit") {
        allImages.value.push(...JSON.parse(context.form.cdn_imgs))
      }
      const insertedImages = await deleteImages(contentRef.editorRef)
      context.form.cdn_imgs = JSON.stringify(insertedImages)
      allImages.value = []
      return true
    },
    wrapper: {
      fullscreen: false
    }
  }
}

/**
 * 返回已经插入的图片
 * @param editorRef
 */
async function deleteImages(editorRef: Editor) {
  const insertImages = editorRef.getElemsByType("image")
  const diffImages = differenceWith(allImages.value, insertImages, (arrVal, insertVal) => {
    return arrVal.url === insertVal?.src
  })
  if (diffImages.length > 0) {
    const deleteIds = diffImages.map((item) => item.id).join(",")
    await tikuModel.deleteCdn(deleteIds)
  }

  return allImages.value.filter((item) => {
    // debugger
    return insertImages.findIndex((insertItem) => insertItem?.src === item.url) !== -1
  })
}
</script>
