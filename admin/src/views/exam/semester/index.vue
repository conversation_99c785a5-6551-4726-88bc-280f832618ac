<template>
  <m-table :crud-options="crudOptions" :model="semesterModel" />
</template>

<script lang="tsx" setup>
import semesterModel from "@/api/exam/semester"
import MTable from "@/components/Table/index.vue"
import { compute, CrudOptions, dict } from "@fast-crud/fast-crud"

defineOptions({
  name: "examSemester"
})
const crudOptions: CrudOptions = {
  columns: {
    id: {
      title: "id",
      form: { show: false }, // 表单配置
      column: {
        width: 70,
        sorter: true
      }
    },
    year: {
      title: "年份",
      // type: "date",
      valueBuilder({ form }) {
        form.year = new Date(form.year, 1, 1)
      },
      form: {
        component: {
          name: "el-date-picker",
          type: "year",
          defaultValue: new Date(),
          // format: 'yyyy',
          // valueFormat: 'yyyy',
          placeholder: "选择年份"
        },
        col: {
          span: 12
        },
        rules: [{ required: true, message: "学科必填" }]
        // value: new Date().getFullYear()
      },
      column: {
        cellRender({ value }) {
          return value.getFullYear()
        }
      }
    },
    season: {
      title: "季节",
      type: "dict-select",
      dict: dict({
        data: [
          { value: "春期", label: "春期" },
          { value: "秋期", label: "秋期" }
        ]
      }),
      form: {
        col: {
          span: 12
        },
        rules: [{ required: true, message: "学科必填" }]
      }
    },
    semester: {
      title: "学期",
      type: "text",
      form: {
        show: false
      },
      search: {
        show: true,
        component: {
          placeholder: "请输入要查询的学期"
        }
      }
    },
    is_default: {
      title: "是否默认",
      type: "dict-switch",
      colorAuto: true,
      dict: dict({
        data: [

          { value: true, label: "是" },
            { value: false, label: "否" },
        ]
      }),
      form: {
        col: {
          span: 12
        },
        value: false,
        // rules: [{ required: true, message: "是否默认必填" }]
      }
    },
  },
  form: {
    async beforeSubmit(context) {
      if (typeof context.form.year === "object") {
        context.form.year = context.form.year.getFullYear()
      }
      // debugger
      // if (context.form.year?.getFullYear() !== undefined) {
      //   context.form.year = context.form.year.getFullYear()
      // }
      context.form.semester = `${context.form.year}年${context.form.season}`
      return true
    }
  }
}
</script>
