<template>
  <div class="semester-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>学期管理</h1>
        <p>管理学期信息，设置默认学期</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleAdd" :icon="Plus">添加学期</el-button>
        <el-button type="success" @click="handleQuickCreate" :icon="Calendar">快速创建当前学期</el-button>
      </div>
    </div>

    <!-- 默认学期设置 -->
    <el-card class="default-semester-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>默认学期设置</span>
          <el-tag v-if="defaultSemester" type="success" size="large">
            当前默认：{{ defaultSemester.semester }}
          </el-tag>
          <el-tag v-else type="warning" size="large">
            未设置默认学期
          </el-tag>
        </div>
      </template>
      <p>默认学期将在创建题目和分类时自动选中，建议设置为当前正在使用的学期。</p>
    </el-card>

    <!-- 筛选栏 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="filters" inline>
        <el-form-item label="学期名称">
          <el-input v-model="filters.semester" placeholder="搜索学期名称" clearable @change="handleSearch" style="width: 200px;" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="handleSearch">
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="table-header">
          <span>学期列表</span>
          <div class="table-actions">
            <el-button size="small" @click="handleRefresh" :icon="Refresh">刷新</el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="tableData" 
        v-loading="loading"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="semester" label="学期名称" min-width="200" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              @change="handleStatusChange(row)"
              active-text="启用"
              inactive-text="禁用"
            />
          </template>
        </el-table-column>
        <el-table-column label="是否默认" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.is_default" type="success" size="small">
              <el-icon><Star /></el-icon>
              默认学期
            </el-tag>
            <el-button v-else size="small" type="primary" text @click="handleSetDefault(row)">
              设为默认
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="分类数量" width="120">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.category_count || 0 }} 个</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="题目数量" width="120">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.tiku_count || 0 }} 个</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="warning" @click="handleEdit(row)" :icon="Edit">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)" :icon="Delete" :disabled="row.is_default">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogMode === 'add' ? '添加学期' : '编辑学期'" 
      width="400px"
    >
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="80px">
        <el-form-item label="学期名称" prop="semester">
          <el-input v-model="formData.semester" placeholder="请输入学期名称，例如：2024年春季学期" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="formData.status"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        <el-form-item label="设为默认" prop="is_default">
          <el-switch
            v-model="formData.is_default"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ dialogMode === 'add' ? '添加' : '更新' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Calendar, 
  Search, 
  Refresh, 
  Edit, 
  Delete,
  Star
} from '@element-plus/icons-vue'
import semesterModel from '@/api/exam/semester'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

defineOptions({
  name: "examSemester"
})

// 响应式数据
const loading = ref(false)
const submitting = ref(false)

// 表格数据
const tableData = ref([])

// 分页数据
const pagination = ref({
  page: 1,
  size: 20,
  total: 0
})

// 筛选数据
const filters = ref({
  semester: '',
  status: null
})

// 对话框状态
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit'

// 表单数据
const formData = ref({
  id: null,
  semester: '',
  status: true,
  is_default: false
})

const formRef = ref()

// 默认学期
const defaultSemester = computed(() => {
  return tableData.value.find(item => item.is_default)
})

// 表单验证规则
const formRules = {
  semester: [{ required: true, message: '请输入学期名称', trigger: 'blur' }]
}

// 初始化
onMounted(() => {
  loadTableData()
})

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.page,
      size: pagination.value.size,
      ...filters.value
    }
    
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === null || params[key] === '') {
        delete params[key]
      }
    })
    
    const response = await semesterModel.list(params)
    tableData.value = response.items || []
    pagination.value.total = response.total || 0
    
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.value.page = 1
  loadTableData()
}

// 重置筛选
const handleReset = () => {
  filters.value = {
    semester: '',
    status: null
  }
  handleSearch()
}

// 刷新
const handleRefresh = () => {
  loadTableData()
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1
  loadTableData()
}

const handleCurrentChange = (page: number) => {
  pagination.value.page = page
  loadTableData()
}

// 时间格式化
const formatTime = (time: string) => {
  if (!time) return '未知'
  try {
    return formatDistanceToNow(new Date(time), {
      addSuffix: true,
      locale: zhCN
    })
  } catch (error) {
    return '时间格式错误'
  }
}

// CRUD操作
const handleAdd = () => {
  dialogMode.value = 'add'
  formData.value = {
    id: null,
    semester: '',
    status: true,
    is_default: false
  }
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  dialogMode.value = 'edit'
  formData.value = {
    id: row.id,
    semester: row.semester,
    status: row.status,
    is_default: row.is_default
  }
  dialogVisible.value = true
}

const handleDelete = async (row: any) => {
  if (row.is_default) {
    ElMessage.warning('默认学期不能删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除学期"${row.semester}"吗？删除后相关的分类和题目也会受到影响。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await semesterModel.delete(row.id)
    ElMessage.success('删除成功')
    loadTableData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    if (dialogMode.value === 'add') {
      await semesterModel.create(formData.value)
      ElMessage.success('添加成功')
    } else {
      await semesterModel.update(formData.value.id, formData.value)
      ElMessage.success('更新成功')
    }

    handleDialogClose()
    loadTableData()

  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 状态变化
const handleStatusChange = async (row: any) => {
  try {
    await semesterModel.update(row.id, { status: row.status })
    ElMessage.success('状态更新成功')
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
    // 恢复原状态
    row.status = !row.status
  }
}

// 设置默认学期
const handleSetDefault = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要将"${row.semester}"设置为默认学期吗？`,
      '确认设置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    await semesterModel.update(row.id, { is_default: true })
    ElMessage.success('默认学期设置成功')
    loadTableData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('设置默认学期失败:', error)
      ElMessage.error('设置默认学期失败')
    }
  }
}

// 快速创建当前学期
const handleQuickCreate = async () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1

  // 判断春季还是秋季学期
  const season = month >= 2 && month <= 7 ? '春季' : '秋季'
  const semesterName = `${year}年${season}学期`

  try {
    await ElMessageBox.confirm(
      `确定要创建"${semesterName}"吗？`,
      '快速创建学期',
      {
        confirmButtonText: '创建',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    await semesterModel.create({
      semester: semesterName,
      status: true,
      is_default: true
    })

    ElMessage.success('学期创建成功')
    loadTableData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('创建学期失败:', error)
      ElMessage.error('创建学期失败')
    }
  }
}
</script>

<style scoped>
.semester-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 默认学期卡片 */
.default-semester-card {
  margin-bottom: 24px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 筛选卡片 */
.filter-card {
  margin-bottom: 24px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-card :deep(.el-card__body) {
  padding: 20px;
}

/* 表格卡片 */
.table-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header span {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table__row:hover) {
  background-color: #f0f9ff;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .semester-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    flex-direction: column;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
