<script setup lang="ts">
import { ref } from "vue"

defineOptions({
  name: "MPhoneView"
})
defineProps({
  content: {
    require: false,
    type: String,
    default: () => ""
  }
})
const width = ref(340)
</script>

<template>
  <div :style="{ width: `${width}px`, margin: 0, padding: 0 }">
    <div class="flex px-2">
      <label class="w-13 font-700">宽度：</label>
      <el-slider class="px-4" v-model="width" :max="500" :min="300"></el-slider>
    </div>
    <div class="phone-content h-[463px] overflow-auto" v-html="content"></div>
  </div>
</template>

<style scoped lang="scss">
.phone-content {
  & > p {
    height: v-bind(width) + "px";
  }
}
</style>
