<template>
  <div class="statistics-panel">
    <el-card class="statistics-card" shadow="never">
      <div class="card-header">
        <h3>数据统计</h3>
        <el-button size="small" @click="refreshStatistics" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <el-row :gutter="16" class="statistics-content">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon semester">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.semesters || 0 }}</div>
              <div class="stat-label">学期总数</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon category">
              <el-icon><Folder /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.categories || 0 }}</div>
              <div class="stat-label">分类总数</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon tiku">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.tikus || 0 }}</div>
              <div class="stat-label">题目总数</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon pinyin">
              <el-icon><Reading /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.pinyinTikus || 0 }}</div>
              <div class="stat-label">拼音题目</div>
            </div>
          </div>
        </el-col>
      </el-row>
      
      <!-- 详细统计 -->
      <div class="detailed-stats" v-if="showDetails">
        <el-divider>详细统计</el-divider>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="detail-section">
              <h4>按学科分布</h4>
              <div class="subject-stats">
                <div 
                  v-for="(count, subject) in statistics.bySubject" 
                  :key="subject"
                  class="subject-item"
                >
                  <span class="subject-name">{{ subject }}</span>
                  <span class="subject-count">{{ count }}</span>
                </div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="detail-section">
              <h4>按年级分布</h4>
              <div class="grade-stats">
                <div 
                  v-for="(count, grade) in statistics.byGrade" 
                  :key="grade"
                  class="grade-item"
                >
                  <span class="grade-name">{{ grade }}</span>
                  <span class="grade-count">{{ count }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="16" style="margin-top: 16px;">
          <el-col :span="24">
            <div class="detail-section">
              <h4>最近更新</h4>
              <div class="recent-updates">
                <div 
                  v-for="item in statistics.recentUpdates" 
                  :key="item.id"
                  class="update-item"
                >
                  <span class="update-title">{{ item.abstract || '无标题' }}</span>
                  <span class="update-time">{{ formatTime(item.update_time) }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <div class="toggle-details">
        <el-button 
          text 
          type="primary" 
          @click="showDetails = !showDetails"
        >
          {{ showDetails ? '收起详情' : '展开详情' }}
          <el-icon>
            <ArrowDown v-if="!showDetails" />
            <ArrowUp v-else />
          </el-icon>
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh, 
  Calendar, 
  Folder, 
  Document, 
  Reading,
  ArrowDown,
  ArrowUp
} from '@element-plus/icons-vue'
import tikuModel from '@/api/exam/tiku'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

defineOptions({
  name: 'StatisticsPanel'
})

const loading = ref(false)
const showDetails = ref(false)
const statistics = ref({
  semesters: 0,
  categories: 0,
  tikus: 0,
  pinyinTikus: 0,
  bySubject: {},
  byGrade: {},
  recentUpdates: []
})

onMounted(() => {
  loadStatistics()
})

const loadStatistics = async () => {
  loading.value = true
  try {
    const data = await tikuModel.getStatistics()
    statistics.value = data || statistics.value
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const refreshStatistics = () => {
  loadStatistics()
}

const formatTime = (time: string) => {
  if (!time) return '未知'
  try {
    return formatDistanceToNow(new Date(time), { 
      addSuffix: true, 
      locale: zhCN 
    })
  } catch (error) {
    return '时间格式错误'
  }
}

defineExpose({
  refresh: refreshStatistics
})
</script>

<style scoped>
.statistics-panel {
  margin-bottom: 16px;
}

.statistics-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.statistics-content {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stat-icon.semester {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.category {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.tiku {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.pinyin {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.detailed-stats {
  margin-top: 20px;
}

.detail-section {
  background-color: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 600;
}

.subject-stats,
.grade-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.subject-item,
.grade-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.subject-name,
.grade-name {
  color: #606266;
  font-size: 13px;
}

.subject-count,
.grade-count {
  color: #409eff;
  font-weight: 600;
  font-size: 14px;
}

.recent-updates {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.update-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.update-title {
  color: #606266;
  font-size: 13px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 12px;
}

.update-time {
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
}

.toggle-details {
  text-align: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .statistics-content .el-col {
    margin-bottom: 12px;
  }
  
  .stat-item {
    padding: 12px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .detailed-stats .el-col {
    margin-bottom: 16px;
  }
}
</style>
