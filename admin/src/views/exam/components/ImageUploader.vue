<template>
  <div class="image-uploader">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :data="uploadData"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :limit="limit"
      :accept="accept"
      :multiple="multiple"
      :drag="drag"
      :show-file-list="showFileList"
      list-type="picture-card"
      class="upload-demo"
    >
      <div class="upload-trigger">
        <el-icon class="upload-icon"><Plus /></el-icon>
        <div class="upload-text">点击上传图片</div>
      </div>
      
      <template #tip>
        <div class="upload-tip">
          <div>支持 jpg、png、gif 格式</div>
          <div>单个文件大小不超过 {{ maxSizeMB }}MB</div>
          <div v-if="limit > 1">最多上传 {{ limit }} 个文件</div>
        </div>
      </template>
    </el-upload>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="60%">
      <div class="preview-container">
        <img :src="previewUrl" alt="预览图片" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import tikuModel from '@/api/exam/tiku'

defineOptions({
  name: 'ImageUploader'
})

interface UploadFile {
  id?: string
  name: string
  url: string
  status?: string
}

const props = defineProps({
  modelValue: {
    type: Array as () => UploadFile[],
    default: () => []
  },
  limit: {
    type: Number,
    default: 5
  },
  maxSize: {
    type: Number,
    default: 5 * 1024 * 1024 // 5MB
  },
  accept: {
    type: String,
    default: 'image/*'
  },
  multiple: {
    type: Boolean,
    default: true
  },
  drag: {
    type: Boolean,
    default: false
  },
  showFileList: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const uploadRef = ref()
const fileList = ref<UploadFile[]>([])
const previewVisible = ref(false)
const previewUrl = ref('')

// 计算属性
const maxSizeMB = computed(() => Math.round(props.maxSize / 1024 / 1024))
const uploadUrl = computed(() => '/api/tiku/sync_cdn_file')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${localStorage.getItem('access_token') || ''}`
}))
const uploadData = computed(() => ({}))

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  fileList.value = newValue.map(item => ({
    ...item,
    uid: item.id || Date.now() + Math.random()
  }))
}, { immediate: true })

// 上传前检查
const beforeUpload = (file: File) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  // 检查文件大小
  if (file.size > props.maxSize) {
    ElMessage.error(`文件大小不能超过 ${maxSizeMB.value}MB!`)
    return false
  }

  // 检查数量限制
  if (fileList.value.length >= props.limit) {
    ElMessage.error(`最多只能上传 ${props.limit} 个文件!`)
    return false
  }

  return true
}

// 上传成功
const handleSuccess = (response: any, file: any) => {
  if (response && response.url) {
    const newFile: UploadFile = {
      id: response.id,
      name: file.name,
      url: response.url,
      status: 'success'
    }
    
    fileList.value.push(newFile)
    updateModelValue()
    
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败')
  }
}

// 上传失败
const handleError = (error: any) => {
  console.error('上传失败:', error)
  ElMessage.error('图片上传失败，请重试')
}

// 移除文件
const handleRemove = async (file: any) => {
  try {
    // 如果有ID，说明是已上传的文件，需要从CDN删除
    if (file.id) {
      await tikuModel.deleteCdn(file.id)
    }
    
    // 从文件列表中移除
    const index = fileList.value.findIndex(item => 
      item.id === file.id || item.url === file.url
    )
    if (index > -1) {
      fileList.value.splice(index, 1)
      updateModelValue()
    }
    
    ElMessage.success('图片删除成功')
  } catch (error) {
    console.error('删除图片失败:', error)
    ElMessage.error('删除图片失败')
  }
}

// 预览图片
const handlePreview = (file: any) => {
  previewUrl.value = file.url
  previewVisible.value = true
}

// 更新模型值
const updateModelValue = () => {
  const value = fileList.value.map(item => ({
    id: item.id,
    name: item.name,
    url: item.url
  }))
  emit('update:modelValue', value)
  emit('change', value)
}

// 清空文件列表
const clearFiles = () => {
  fileList.value = []
  updateModelValue()
}

// 手动触发上传
const submit = () => {
  uploadRef.value?.submit()
}

// 暴露方法
defineExpose({
  clearFiles,
  submit,
  getFileList: () => fileList.value
})
</script>

<style scoped>
.image-uploader {
  width: 100%;
}

.upload-demo {
  width: 100%;
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-trigger:hover {
  border-color: #409eff;
  color: #409eff;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

.upload-text {
  color: #8c939d;
  font-size: 14px;
}

.upload-tip {
  color: #606266;
  font-size: 12px;
  line-height: 1.5;
  margin-top: 8px;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.preview-image {
  max-width: 100%;
  max-height: 500px;
  object-fit: contain;
}

/* Element Plus 样式覆盖 */
:deep(.el-upload--picture-card) {
  width: 148px;
  height: 148px;
  border-radius: 6px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 148px;
  height: 148px;
  border-radius: 6px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item-thumbnail) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 移动端适配 */
@media (max-width: 768px) {
  :deep(.el-upload--picture-card) {
    width: 120px;
    height: 120px;
  }

  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 120px;
    height: 120px;
  }
  
  .upload-icon {
    font-size: 24px;
  }
  
  .upload-text {
    font-size: 12px;
  }
}
</style>
