<template>
  <div class="category-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>分类管理</h1>
        <p>管理题目分类，支持快速创建和批量操作</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleAdd" :icon="Plus">添加分类</el-button>
        <el-button type="success" @click="handleBatchCreate" :icon="FolderOpened">批量创建</el-button>
      </div>
    </div>

    <!-- 筛选栏 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="filters" inline>
        <el-form-item label="学期">
          <el-select v-model="filters.semester" placeholder="选择学期" clearable @change="handleSearch">
            <el-option v-for="item in semesterOptions" :key="item.id" :label="item.semester" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="年级">
          <el-select v-model="filters.grade" placeholder="选择年级" clearable @change="handleSearch">
            <el-option v-for="item in gradeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="学科">
          <el-select v-model="filters.subject" placeholder="选择学科" clearable @change="handleSearch">
            <el-option v-for="item in subjectOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类名称">
          <el-input v-model="filters.title" placeholder="搜索分类名称" clearable @change="handleSearch" style="width: 200px;" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="table-header">
          <span>分类列表</span>
          <div class="table-actions">
            <el-button size="small" @click="handleRefresh" :icon="Refresh">刷新</el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="tableData" 
        v-loading="loading"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="分类名称" min-width="200" />
        <el-table-column prop="semester_title" label="学期" width="150" />
        <el-table-column prop="grade" label="年级" width="100" />
        <el-table-column prop="subject" label="学科" width="100" />
        <el-table-column label="题目数量" width="120">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.tiku_count || 0 }} 个</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="warning" @click="handleEdit(row)" :icon="Edit">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)" :icon="Delete">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogMode === 'add' ? '添加分类' : '编辑分类'" 
      width="500px"
    >
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="80px">
        <el-form-item label="学期" prop="semester">
          <el-select v-model="formData.semester" placeholder="选择学期" style="width: 100%;">
            <el-option v-for="item in semesterOptions" :key="item.id" :label="item.semester" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="年级" prop="grade">
          <el-select v-model="formData.grade" placeholder="选择年级" style="width: 100%;">
            <el-option v-for="item in gradeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="学科" prop="subject">
          <el-select v-model="formData.subject" placeholder="选择学科" style="width: 100%;">
            <el-option v-for="item in subjectOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类名称" prop="category">
          <el-input v-model="formData.category" placeholder="请输入分类名称" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ dialogMode === 'add' ? '添加' : '更新' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量创建对话框 -->
    <el-dialog v-model="batchDialogVisible" title="批量创建分类" width="600px">
      <el-form :model="batchForm" :rules="batchRules" ref="batchFormRef" label-width="80px">
        <el-form-item label="学期" prop="semester">
          <el-select v-model="batchForm.semester" placeholder="选择学期" style="width: 100%;">
            <el-option v-for="item in semesterOptions" :key="item.id" :label="item.semester" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="年级" prop="grade">
          <el-select v-model="batchForm.grade" placeholder="选择年级" style="width: 100%;">
            <el-option v-for="item in gradeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="学科" prop="subject">
          <el-select v-model="batchForm.subject" placeholder="选择学科" style="width: 100%;">
            <el-option v-for="item in subjectOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类名称" prop="categories">
          <el-input 
            v-model="batchForm.categories" 
            type="textarea" 
            :rows="4"
            placeholder="请输入分类名称，多个分类用逗号分隔，例如：拼音练习,词语理解,句子分析"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBatchSubmit" :loading="batchSubmitting">批量创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  FolderOpened, 
  Search, 
  Refresh, 
  Edit, 
  Delete 
} from '@element-plus/icons-vue'
import categoryModel from '@/api/exam/category'
import semesterModel from '@/api/exam/semester'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

defineOptions({
  name: "examCategory"
})

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const batchSubmitting = ref(false)

// 表格数据
const tableData = ref([])

// 分页数据
const pagination = ref({
  page: 1,
  size: 20,
  total: 0
})

// 筛选数据
const filters = ref({
  semester: null,
  grade: null,
  subject: null,
  title: ''
})

// 对话框状态
const dialogVisible = ref(false)
const batchDialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit'

// 表单数据
const formData = ref({
  id: null,
  semester: null,
  grade: null,
  subject: null,
  category: ''
})

const batchForm = ref({
  semester: null,
  grade: null,
  subject: null,
  categories: ''
})

const formRef = ref()
const batchFormRef = ref()

// 选项数据
const semesterOptions = ref([])
const gradeOptions = ref([
  { value: "一年级", label: "一年级" },
  { value: "二年级", label: "二年级" },
  { value: "三年级", label: "三年级" },
  { value: "四年级", label: "四年级" },
  { value: "五年级", label: "五年级" },
  { value: "六年级", label: "六年级" }
])
const subjectOptions = ref([
  { value: "语文", label: "语文" },
  { value: "数学", label: "数学" }
])

// 表单验证规则
const formRules = {
  semester: [{ required: true, message: '请选择学期', trigger: 'change' }],
  grade: [{ required: true, message: '请选择年级', trigger: 'change' }],
  subject: [{ required: true, message: '请选择学科', trigger: 'change' }],
  category: [{ required: true, message: '请输入分类名称', trigger: 'blur' }]
}

const batchRules = {
  semester: [{ required: true, message: '请选择学期', trigger: 'change' }],
  grade: [{ required: true, message: '请选择年级', trigger: 'change' }],
  subject: [{ required: true, message: '请选择学科', trigger: 'change' }],
  categories: [{ required: true, message: '请输入分类名称', trigger: 'blur' }]
}

// 初始化
onMounted(() => {
  loadSemesterOptions()
  loadTableData()
})

// 加载学期选项
const loadSemesterOptions = async () => {
  try {
    const { items } = await semesterModel.list({ size: 100 })
    semesterOptions.value = items
  } catch (error) {
    console.error('加载学期数据失败:', error)
  }
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.page,
      size: pagination.value.size,
      ...filters.value
    }

    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === null || params[key] === '') {
        delete params[key]
      }
    })

    const response = await categoryModel.list(params)
    tableData.value = response.items || []
    pagination.value.total = response.total || 0

    // 处理数据，添加额外字段
    tableData.value = tableData.value.map(item => ({
      ...item,
      semester_title: item.semester?.semester || '未知学期',
      tiku_count: item.tiku_count || 0
    }))

  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.value.page = 1
  loadTableData()
}

// 重置筛选
const handleReset = () => {
  filters.value = {
    semester: null,
    grade: null,
    subject: null,
    title: ''
  }
  handleSearch()
}

// 刷新
const handleRefresh = () => {
  loadTableData()
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1
  loadTableData()
}

const handleCurrentChange = (page: number) => {
  pagination.value.page = page
  loadTableData()
}

// 时间格式化
const formatTime = (time: string) => {
  if (!time) return '未知'
  try {
    return formatDistanceToNow(new Date(time), {
      addSuffix: true,
      locale: zhCN
    })
  } catch (error) {
    return '时间格式错误'
  }
}

// CRUD操作
const handleAdd = () => {
  dialogMode.value = 'add'
  formData.value = {
    id: null,
    semester: filters.value.semester,
    grade: filters.value.grade,
    subject: filters.value.subject,
    category: ''
  }
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  dialogMode.value = 'edit'
  formData.value = {
    id: row.id,
    semester: row.semester?.id || row.semester,
    grade: row.grade,
    subject: row.subject,
    category: row.category || ''
  }
  dialogVisible.value = true
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类"${row.title || '未命名'}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await categoryModel.delete(row.id)
    ElMessage.success('删除成功')
    loadTableData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 生成标题
    const semesterData = semesterOptions.value.find(item => item.id === formData.value.semester)
    if (!semesterData) {
      ElMessage.error('学期数据异常')
      return
    }

    const submitData = {
      ...formData.value,
      title: `${semesterData.semester}-${formData.value.grade}-${formData.value.subject}-${formData.value.category}`
    }

    if (dialogMode.value === 'add') {
      await categoryModel.create(submitData)
      ElMessage.success('添加成功')
    } else {
      await categoryModel.update(submitData.id, submitData)
      ElMessage.success('更新成功')
    }

    handleDialogClose()
    loadTableData()

  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 批量创建
const handleBatchCreate = () => {
  batchForm.value = {
    semester: filters.value.semester,
    grade: filters.value.grade,
    subject: filters.value.subject,
    categories: ''
  }
  batchDialogVisible.value = true
}

const handleBatchSubmit = async () => {
  if (!batchFormRef.value) return

  try {
    const valid = await batchFormRef.value.validate()
    if (!valid) return

    const categories = batchForm.value.categories.split(',').map(cat => cat.trim()).filter(cat => cat)
    if (categories.length === 0) {
      ElMessage.warning('请输入有效的分类名称')
      return
    }

    batchSubmitting.value = true

    const semesterData = semesterOptions.value.find(item => item.id === batchForm.value.semester)
    if (!semesterData) {
      ElMessage.error('学期数据异常')
      return
    }

    const promises = categories.map(categoryName => {
      const data = {
        semester: batchForm.value.semester,
        grade: batchForm.value.grade,
        subject: batchForm.value.subject,
        category: categoryName,
        title: `${semesterData.semester}-${batchForm.value.grade}-${batchForm.value.subject}-${categoryName}`
      }
      return categoryModel.create(data)
    })

    await Promise.all(promises)
    ElMessage.success(`成功创建 ${categories.length} 个分类`)

    batchDialogVisible.value = false
    loadTableData()

  } catch (error) {
    console.error('批量创建失败:', error)
    ElMessage.error('批量创建失败')
  } finally {
    batchSubmitting.value = false
  }
}
</script>

<style scoped>
.category-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 筛选卡片 */
.filter-card {
  margin-bottom: 24px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-card :deep(.el-card__body) {
  padding: 20px;
}

/* 表格卡片 */
.table-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header span {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table__row:hover) {
  background-color: #f0f9ff;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .category-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    flex-direction: column;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
