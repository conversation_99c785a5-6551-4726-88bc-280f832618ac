<template>
  <m-table :crud-options="crudOptions" :model="categoryModel" />
</template>

<script lang="tsx" setup>
import categoryModel from "@/api/exam/category"
import semesterModel from "@/api/exam/semester"

import MTable from "@/components/Table/index.vue"
import { CrudOptions, dict } from "@fast-crud/fast-crud"
import { ElMessage } from "element-plus"
defineOptions({
  name: "examCategory"
})
const crudOptions: CrudOptions = {
  columns: {
    semester: {
      title: "学期",
      type: "dict-select",
      valueBuilder(context) {
        context.row.semester = context.row.semester?.id
      },
      search: { show: true }, // 开启查询
      form: {
        col: { span: 12 },
        rules: [{ required: true, message: "学期必填" }]
      },
      column: {
        sorter: true
      },
      dict: dict({
        async getData() {
          const { items } = await semesterModel.list({ size: 100 })
          return items
        },
        immediate: false,
        value: "id",
        label: "semester"
      })
    },
    grade: {
      title: "年级",
      type: "dict-select",
      // search: { show: true }, // 开启查询
      form: { col: { span: 12 }, rules: [{ required: true, message: "年级必填" }] },
      column: {
        sorter: true
      },
      search: {
        show: true
      },
      dict: dict({
        data: [
          { value: "一年级", label: "一年级" },
          { value: "二年级", label: "二年级" },
          { value: "三年级", label: "三年级" },
          { value: "四年级", label: "四年级" },
          { value: "五年级", label: "五年级" },
          { value: "六年级", label: "六年级" }
        ]
      })
    },
    subject: {
      title: "学科",
      type: "dict-select",
      // search: { show: true }, // 开启查询
      form: { col: { span: 12 }, rules: [{ required: true, message: "学科必填" }] },
      column: {
        sorter: true
      },
      search: {
        show: true
      },
      dict: dict({
        data: [
          { value: "语文", label: "语文" },
          { value: "数学", label: "数学" }
        ]
      })
    },
    category: {
      title: "分类",
      type: "text",
      form: {
        col: { span: 12 },
        rules: [{ required: true, message: "分类必填" }],
        component: { placeholder: "例如：识字大闯关" }
      },
      search: {
        show: true
      }
    },
    title: {
      title: "标题",
      type: "text",
      form: {
        show: false
      },
      column: {
        show: false
      }
    }
  },
  form: {
    async beforeSubmit({ form, mode, getComponentRef }) {
      const semesterRef = getComponentRef("semester")
      const { semester, grade, subject, category } = form
      const semesterData = semesterRef.getDictData().find((item) => item.id === semester)
      if (!semesterData) {
        ElMessage.error("学期未选择")
        return false
      }
      form.title = `${semesterData.semester}-${grade}-${subject}-${category}`
      return true
    }
  }
}
</script>
