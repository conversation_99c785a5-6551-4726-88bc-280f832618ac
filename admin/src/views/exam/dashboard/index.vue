<template>
  <div class="exam-dashboard">
    <!-- 页面标题 -->
    <div class="dashboard-header">
      <h1>题库管理仪表板</h1>
      <p>全面管理您的题库、分类和学期数据</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card semester-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.semesters || 0 }}</div>
              <div class="stat-label">学期总数</div>
            </div>
          </div>
          <div class="stat-action">
            <el-button type="primary" size="small" @click="goToSemester">管理学期</el-button>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card category-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Folder /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.categories || 0 }}</div>
              <div class="stat-label">分类总数</div>
            </div>
          </div>
          <div class="stat-action">
            <el-button type="success" size="small" @click="goToCategory">管理分类</el-button>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card tiku-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.tikus || 0 }}</div>
              <div class="stat-label">题目总数</div>
            </div>
          </div>
          <div class="stat-action">
            <el-button type="warning" size="small" @click="goToTiku">管理题库</el-button>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card pinyin-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Reading /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.pinyinTikus || 0 }}</div>
              <div class="stat-label">拼音题目</div>
            </div>
          </div>
          <div class="stat-action">
            <el-button type="info" size="small" @click="goToPinyin">拼音管理</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <el-row :gutter="20" class="quick-actions-row">
      <el-col :xs="24" :md="12">
        <el-card class="quick-actions-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h3>快捷操作</h3>
              <el-icon><Lightning /></el-icon>
            </div>
          </template>
          
          <div class="quick-actions-grid">
            <div class="action-item" @click="quickCreateSemester">
              <el-icon class="action-icon"><Plus /></el-icon>
              <span>创建学期</span>
            </div>
            <div class="action-item" @click="quickCreateCategory">
              <el-icon class="action-icon"><FolderAdd /></el-icon>
              <span>创建分类</span>
            </div>
            <div class="action-item" @click="quickCreateTiku">
              <el-icon class="action-icon"><DocumentAdd /></el-icon>
              <span>添加题目</span>
            </div>
            <div class="action-item" @click="batchImport">
              <el-icon class="action-icon"><Upload /></el-icon>
              <span>批量导入</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :md="12">
        <el-card class="recent-activity-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h3>最近活动</h3>
              <el-icon><Clock /></el-icon>
            </div>
          </template>
          
          <div class="recent-activities">
            <div 
              v-for="activity in recentActivities" 
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ formatTime(activity.time) }}</div>
              </div>
            </div>
            
            <div v-if="recentActivities.length === 0" class="no-activities">
              <el-empty description="暂无最近活动" :image-size="80" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据分布图表 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :md="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h3>学科分布</h3>
              <el-icon><PieChart /></el-icon>
            </div>
          </template>
          
          <div class="subject-distribution">
            <div 
              v-for="(count, subject) in statistics.bySubject" 
              :key="subject"
              class="subject-item"
            >
              <div class="subject-bar">
                <div class="subject-name">{{ subject }}</div>
                <div class="subject-progress">
                  <el-progress 
                    :percentage="getPercentage(count, statistics.tikus)" 
                    :color="getSubjectColor(subject)"
                    :show-text="false"
                  />
                </div>
                <div class="subject-count">{{ count }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :md="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h3>年级分布</h3>
              <el-icon><BarChart /></el-icon>
            </div>
          </template>
          
          <div class="grade-distribution">
            <div 
              v-for="(count, grade) in statistics.byGrade" 
              :key="grade"
              class="grade-item"
            >
              <div class="grade-bar">
                <div class="grade-name">{{ grade }}</div>
                <div class="grade-progress">
                  <el-progress 
                    :percentage="getPercentage(count, statistics.tikus)" 
                    :color="getGradeColor(grade)"
                    :show-text="false"
                  />
                </div>
                <div class="grade-count">{{ count }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Calendar,
  Folder,
  Document,
  Reading,
  Lightning,
  Plus,
  FolderAdd,
  DocumentAdd,
  Upload,
  Clock,
  PieChart,
  BarChart
} from '@element-plus/icons-vue'
import tikuModel from '@/api/exam/tiku'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

defineOptions({
  name: 'ExamDashboard'
})

const router = useRouter()

const statistics = ref({
  semesters: 0,
  categories: 0,
  tikus: 0,
  pinyinTikus: 0,
  bySubject: {},
  byGrade: {}
})

const recentActivities = ref([])

onMounted(() => {
  loadStatistics()
  loadRecentActivities()
})

// 加载统计数据
const loadStatistics = async () => {
  try {
    const data = await tikuModel.getStatistics()
    statistics.value = data || statistics.value
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 加载最近活动
const loadRecentActivities = async () => {
  try {
    // 这里可以调用获取最近活动的API
    recentActivities.value = []
  } catch (error) {
    console.error('加载最近活动失败:', error)
  }
}

// 导航方法
const goToSemester = () => router.push('/exam/semester')
const goToCategory = () => router.push('/exam/category')
const goToTiku = () => router.push('/exam/tiku')
const goToPinyin = () => router.push('/exam/tiku?pinyin=true')

// 快捷操作
const quickCreateSemester = () => {
  ElMessage.info('跳转到学期管理页面创建新学期')
  goToSemester()
}

const quickCreateCategory = () => {
  ElMessage.info('跳转到分类管理页面创建新分类')
  goToCategory()
}

const quickCreateTiku = () => {
  ElMessage.info('跳转到题库管理页面添加新题目')
  goToTiku()
}

const batchImport = () => {
  ElMessage.info('批量导入功能开发中...')
}

// 工具方法
const formatTime = (time: string) => {
  if (!time) return '未知'
  try {
    return formatDistanceToNow(new Date(time), { 
      addSuffix: true, 
      locale: zhCN 
    })
  } catch (error) {
    return '时间格式错误'
  }
}

const getPercentage = (value: number, total: number) => {
  return total > 0 ? Math.round((value / total) * 100) : 0
}

const getSubjectColor = (subject: string) => {
  const colors = {
    '语文': '#f56c6c',
    '数学': '#409eff',
    '英语': '#67c23a',
    '科学': '#e6a23c',
    '其他': '#909399'
  }
  return colors[subject] || colors['其他']
}

const getGradeColor = (grade: string) => {
  const colors = {
    '一年级': '#ff6b6b',
    '二年级': '#4ecdc4',
    '三年级': '#45b7d1',
    '四年级': '#96ceb4',
    '五年级': '#feca57',
    '六年级': '#ff9ff3'
  }
  return colors[grade] || '#909399'
}
</script>

<style scoped>
.exam-dashboard {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
}

.dashboard-header h1 {
  color: #303133;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.dashboard-header p {
  color: #606266;
  font-size: 16px;
  margin: 0;
}

.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 16px;
}

.semester-card .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.category-card .stat-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.tiku-card .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.pinyin-card .stat-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  color: #909399;
  font-size: 14px;
  margin-top: 4px;
}

.stat-action {
  text-align: center;
}

.quick-actions-row,
.charts-row {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  color: #409eff;
}

.action-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.recent-activities {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f0f9ff;
  color: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.activity-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #909399;
}

.subject-distribution,
.grade-distribution {
  padding: 16px 0;
}

.subject-item,
.grade-item {
  margin-bottom: 16px;
}

.subject-bar,
.grade-bar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.subject-name,
.grade-name {
  width: 60px;
  font-size: 14px;
  color: #606266;
  text-align: right;
}

.subject-progress,
.grade-progress {
  flex: 1;
}

.subject-count,
.grade-count {
  width: 40px;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  text-align: center;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .exam-dashboard {
    padding: 16px;
  }
  
  .dashboard-header h1 {
    font-size: 24px;
  }
  
  .dashboard-header p {
    font-size: 14px;
  }
  
  .stats-row .el-col {
    margin-bottom: 16px;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
}
</style>
