<template>
  <div class="pinyin-editor">
    <div v-for="(item, index) in selectedTexts" :key="index" class="text-with-pinyin">
      <span class="text">{{ item.text }}</span>
      <span class="pinyin">{{ item.pinyin }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineExpose } from 'vue';

const selectedTexts = ref<{ text: string; pinyin: string }[]>([]);

function addPinyin(text: string, pinyin: string) {
  selectedTexts.value.push({ text, pinyin });
}

function handleTextSelection(text: string) {
  const pinyin = getPinyin(text); // 假设有一个方法可以获取拼音
  addPinyin(text, pinyin);
}

function getPinyin(text: string): string {
  // 这里可以实现拼音生成的逻辑
  return '拼音'; // 返回示例拼音
}

// 暴露方法
defineExpose({ handleTextSelection });
</script>

<style scoped>
.pinyin-editor {
  margin-top: 10px;
}
.text-with-pinyin {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.text {
  font-weight: bold;
}
.pinyin {
  font-size: 0.8em;
  color: gray;
}
</style> 