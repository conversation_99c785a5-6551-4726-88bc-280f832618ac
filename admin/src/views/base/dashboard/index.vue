<script lang="ts" setup>
import { ref } from 'vue';
import { EditorContent, useEditor } from '@tiptap/vue-3';
import StarterKit from '@tiptap/starter-kit';
import PinyinEditor from './PinyinEditor.vue';

const editor = useEditor({
  extensions: [StarterKit],
  content: '<p>在这里输入文本...</p>',
  onSelectionUpdate({ editor }) {
    const selectedText = editor.getText(); // 获取整个文档的文本
    const { from, to } = editor.state.selection;
    const text = selectedText.slice(from, to); // 获取选定的文本
    if (text) {
      // 调用 PinyinEditor 的方法
      pinyinEditorRef.value.handleTextSelection(text);
    }
  },
});

const pinyinEditorRef = ref();
</script>

<template>
  <div class="home">
    <EditorContent :editor="editor" />
    <PinyinEditor ref="pinyinEditorRef" />
  </div>
</template>
<style lang="scss" scoped>
.home {
  padding: 20px;

  //& > p {
  //  margin: 20px auto;
  //  //margin-bottom: 20px;
  //  text-align: center;
  //  font-weight: 600;
  //  color: #440e0e;
  //}

  .svg-icon {
    width: 100%;
    height: 80%;
  }
}
</style>
