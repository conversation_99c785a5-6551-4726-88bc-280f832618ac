// * 后端路由相关函数
// 根据后端传来数据构建出前端路由
import { shallowRef } from "vue"
import { RouteRecordRaw } from "vue-router"
import Layout from "@/layouts/index.vue"
import { MenuTree } from "@/api/system/menu/types"
import { vueModules } from "@/router/routes"

export function buildRoutes(routes: MenuTree[]): RouteRecordRaw[] {
  // @ts-ignore
  return routes.map((e) => ({
    name: e.name,
    path: e.path, // 处理目录是一级菜单的情况
    component: shallowRef(Layout), // ? 不使用 shallowRef 控制台会有 warning
    redirect: e.redirect,
    meta: {
      title: e.title,
      icon: e.icon,
      svgIcon: e.svg_icon,
      rank: e.order,
      keepAlive: e.keepalive,
      hidden: e.hidden,
      affix: e.affix,
      breadcrumb: e.breadcrumb,
      alwaysShow: e.always_show
    },
    children: e.children.map((e_child) => ({
      name: e_child.name,
      path: e_child.path, // 父路径 + 当前菜单路径
      // ! 读取动态加载的路由模块
      component: vueModules[`/src/views${e_child.component}/index.vue`],
      // component: () => import(`@/views${e_child.component}/index.vue`),
      isHidden: e_child.is_hidden,
      meta: {
        title: e_child.title,
        icon: e_child.icon,
        svgIcon: e_child.svg_icon,
        rank: e_child.order,
        keepAlive: e_child.keepalive,
        hidden: e_child.hidden,
        affix: e_child.affix,
        breadcrumb: e_child.breadcrumb,
        alwaysShow: e_child.always_show
      }
    }))
  }))
}
