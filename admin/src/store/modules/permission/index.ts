import { defineStore } from "pinia"
import { type RouteRecordRaw } from "vue-router"
import { constantRoutes } from "@/router/routes"
import { buildRoutes } from "./utils"
import { userModel } from "@/api/system"
import { getRoutePaths } from "@/router/utils"
import { MenuTree } from "@/api/system/menu/types"

export const usePermissionStore = defineStore("permission", {
  state() {
    return {
      accessRoutes: new Array<RouteRecordRaw>(),
      accessApis: new Array<string>(),
      accessMenus: new Array<MenuTree>()
    }
  },
  getters: {
    routes(state): RouteRecordRaw[] {
      return constantRoutes.concat(state.accessRoutes.sort((a, b) => {
        const rankA = a.meta?.rank || 0;
        const rankB = b.meta?.rank || 0;
        return rankA - rankB;
      }))
    },
    routePaths(state): string[] {
      return getRoutePaths(state.accessRoutes)
    },
    menus(state): MenuTree[] {
      // 当前用户所拥有的菜单权限
      return state.accessMenus.filter((menu) => menu.name && !menu.is_hidden)
      // return this.routes.filter((route) => route.name && !route.isHidden)
    },
    apis(state) {
      return state.accessApis
    }
  },
  actions: {
    async generateRoutes() {
      const res = await userModel.getUserMenu() // 调用接口获取后端传来的菜单路由
      this.accessMenus = res
      this.accessRoutes = buildRoutes(res) // 处理成前端路由格式
      return this.accessRoutes
    },
    async getAccessApis() {
      this.accessApis = await userModel.getUserApi()
      return this.accessApis
    },
    resetPermission() {
      this.$reset()
    }
  }
})
