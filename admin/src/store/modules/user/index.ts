import { defineStore } from "pinia"
import { addD<PERSON><PERSON>out<PERSON>, resetRouter, router } from "@/router"
import { UserSchemaLogin } from "@/api/system/login/types"
import { getUserInfoApi, loginApi } from "@/api/system"
import { removeToken, setToken } from "@/utils/cache/cookies"
import { useSettingsStore } from "@/store/modules/settings"
import { useTagsViewStore } from "@/store/modules/tags-view"
import { UserList } from "@/api/system/user/types"
import { usePermissionStore } from "@/store/modules/permission"

export const useUserStore = defineStore("user", {
  state() {
    return {
      userInfo: {} as UserList,
      token: ""
    }
  },
  getters: {
    userId(state) {
      return state.userInfo.id
    },
    username(state) {
      return state.userInfo.username
    },
    nickname(state) {
      return state.userInfo.nickname
    },
    roles(state) {
      return state.userInfo?.roles || []
    },
    isSuperUser(state) {
      return state.userInfo?.is_super || false
    },
    isActive(state) {
      return state.userInfo.status || false
    }
  },
  actions: {
    async getUserInfo() {
      try {
        this.userInfo = await getUserInfoApi()
        return this.userInfo
      } catch (error) {
        return error
      }
    },
    setUserInfo(userInfo = {}) {
      this.userInfo = { ...this.userInfo, ...userInfo }
    },
    async login(data: UserSchemaLogin) {
      const tokenResp = await loginApi(data)
      setToken(tokenResp)
      this.token = tokenResp.access_token
      await addDynamicRoutes()
    },
    async logout() {
      removeToken()
      usePermissionStore().resetPermission()
      await resetRouter()
      this.$reset()
      this._resetTagsView()
      await router.push("/login")
    },
    /** 重置 Visited Views 和 Cached Views */
    _resetTagsView() {
      if (!useSettingsStore().cacheTagsView) {
        const tagsViewStore = useTagsViewStore()
        tagsViewStore.delAllVisitedViews()
        tagsViewStore.delAllCachedViews()
      }
    }
  }
  // persist: {
  //   paths: ["userInfo", "token"]
  // }
})
