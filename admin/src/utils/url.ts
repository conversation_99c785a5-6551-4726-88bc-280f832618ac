export function generatePath(url: string) {
  if (!url) {
    return ""
  }
  if (url.startsWith("http")) {
    return new URL(url).pathname.replace(/\/$/, "")
  }
  return url.replace(/\/$/, "")
}

export function joinUrlPath(basePath: string, pathFragment: string): string {
  /**
   * 拼接 URL 的 path 部分，确保 URL 合规。
   *
   * @param basePath - 基础路径。
   * @param pathFragment - 需要添加的路径片段。
   * @returns 拼接后的完整路径。
   */
  // 移除 basePath 和 pathFragment 开头和结尾的斜杠
  const cleanBasePath = basePath.replace(/^\//, "").replace(/\/$/, "")
  const cleanPathFragment = pathFragment.replace(/^\//, "").replace(/\/$/, "")

  // 拼接路径并确保合规
  return cleanBasePath && cleanPathFragment
    ? `/${cleanBasePath}/${cleanPathFragment}`
    : `/${cleanBasePath || cleanPathFragment}`
}

export function toPathCamelCase(path: string) {
  if (!path) {
    return ""
  }
  // 移除路径首尾的斜杠
  const segments: string[] = path.replace(/^\//, "").replace(/\/*$/, "").split("/")

  // 转换为小驼峰写法
  const camelCaseSegments = segments.map((segment, index) => {
    if (index === 0) {
      // 第一个单词保持小写
      return segment.toLowerCase()
    }
    // 其他单词首字母大写
    return segment.charAt(0).toUpperCase() + segment.slice(1).toLowerCase()
  })

  // 拼接成最终的小驼峰写法字符串
  return camelCaseSegments.join("")
}
