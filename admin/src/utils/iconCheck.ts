/**
 * 图标检查工具
 * 用于验证项目中使用的所有Element Plus图标是否存在
 */

import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 项目中使用的所有图标名称
export const usedIcons = [
  // 基础操作图标
  'Plus',
  'Delete', 
  'Edit',
  'View',
  'Search',
  'Refresh',
  'RefreshLeft',
  
  // 文档和文件夹图标
  'Document',
  'DocumentAdd',
  'Folder',
  'FolderOpened',
  
  // 时间和日历图标
  'Calendar',
  'Clock',
  
  // 功能图标
  'Reading',
  'Upload',
  'Download',
  'Setting',
  'Tools',
  
  // 箭头图标
  'ArrowUp',
  'ArrowDown',
  
  // 图表图标
  'PieChart',
  'Histogram',
  
  // 其他图标
  'Lightning'
]

// 检查图标是否存在
export function checkIconExists(iconName: string): boolean {
  return iconName in ElementPlusIconsVue
}

// 检查所有使用的图标
export function checkAllUsedIcons(): { valid: string[], invalid: string[] } {
  const valid: string[] = []
  const invalid: string[] = []
  
  usedIcons.forEach(iconName => {
    if (checkIconExists(iconName)) {
      valid.push(iconName)
    } else {
      invalid.push(iconName)
    }
  })
  
  return { valid, invalid }
}

// 获取所有可用的图标名称
export function getAllAvailableIcons(): string[] {
  return Object.keys(ElementPlusIconsVue)
}

// 查找相似的图标名称
export function findSimilarIcons(iconName: string): string[] {
  const availableIcons = getAllAvailableIcons()
  const lowerIconName = iconName.toLowerCase()
  
  return availableIcons.filter(icon => 
    icon.toLowerCase().includes(lowerIconName) ||
    lowerIconName.includes(icon.toLowerCase())
  )
}

// 推荐替代图标
export const iconReplacements: Record<string, string> = {
  'Operation': 'Tools',
  'Clear': 'RefreshLeft',
  'FolderAdd': 'FolderOpened',
  'BarChart': 'Histogram'
}

// 获取推荐的替代图标
export function getRecommendedIcon(iconName: string): string {
  return iconReplacements[iconName] || iconName
}

// 控制台输出图标检查结果
export function logIconCheckResults(): void {
  const { valid, invalid } = checkAllUsedIcons()
  
  console.group('📊 图标检查结果')
  console.log(`✅ 有效图标 (${valid.length}):`, valid)
  
  if (invalid.length > 0) {
    console.log(`❌ 无效图标 (${invalid.length}):`, invalid)
    
    invalid.forEach(iconName => {
      const similar = findSimilarIcons(iconName)
      const recommended = getRecommendedIcon(iconName)
      
      console.group(`🔍 ${iconName}`)
      if (recommended !== iconName) {
        console.log(`💡 推荐替代: ${recommended}`)
      }
      if (similar.length > 0) {
        console.log(`🔗 相似图标:`, similar.slice(0, 5))
      }
      console.groupEnd()
    })
  }
  
  console.groupEnd()
}

// 开发环境下自动检查
if (import.meta.env.DEV) {
  logIconCheckResults()
}
