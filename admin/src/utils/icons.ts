/**
 * 图标统一管理
 * 确保所有使用的图标都是Element Plus中存在的
 */

// 从Element Plus导入所有需要的图标
export {
  // 基础操作图标
  Plus,
  Delete,
  Edit,
  View,
  Search,
  Refresh,
  Close,
  
  // 文档和文件夹图标
  Document,
  DocumentAdd,
  Folder,
  FolderOpened,
  
  // 时间和日历图标
  Calendar,
  Clock,
  
  // 功能图标
  Reading,
  Upload,
  Download,
  Setting,
  Tools,
  
  // 箭头图标
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  
  // 图表图标 - 使用存在的图标
  PieChart,
  DataLine, // 使用DataLine替代Histogram
  
  // 其他图标
  Lightning,
  RefreshLeft,
  
  // 展开折叠图标
  Expand,
  Fold
} from '@element-plus/icons-vue'

// 图标别名映射 - 用于向后兼容
export const IconAliases = {
  // 如果某些图标不存在，提供替代方案
  'Operation': Tools,
  'Clear': RefreshLeft,
  'FolderAdd': FolderOpened,
  'BarChart': DataLine,
  'Histogram': DataLine
} as const

// 获取图标组件
export function getIcon(iconName: string) {
  // 首先尝试从别名映射中获取
  if (iconName in IconAliases) {
    return IconAliases[iconName as keyof typeof IconAliases]
  }
  
  // 如果没有找到，返回默认图标
  return Document
}

// 检查图标是否存在的工具函数
export function hasIcon(iconName: string): boolean {
  try {
    // 动态导入检查
    return iconName in IconAliases || 
           iconName === 'Plus' || 
           iconName === 'Delete' ||
           iconName === 'Edit' ||
           iconName === 'View' ||
           iconName === 'Search' ||
           iconName === 'Refresh' ||
           iconName === 'Close' ||
           iconName === 'Document' ||
           iconName === 'DocumentAdd' ||
           iconName === 'Folder' ||
           iconName === 'FolderOpened' ||
           iconName === 'Calendar' ||
           iconName === 'Clock' ||
           iconName === 'Reading' ||
           iconName === 'Upload' ||
           iconName === 'Download' ||
           iconName === 'Setting' ||
           iconName === 'Tools' ||
           iconName === 'ArrowUp' ||
           iconName === 'ArrowDown' ||
           iconName === 'ArrowLeft' ||
           iconName === 'ArrowRight' ||
           iconName === 'PieChart' ||
           iconName === 'DataLine' ||
           iconName === 'Lightning' ||
           iconName === 'RefreshLeft' ||
           iconName === 'Expand' ||
           iconName === 'Fold'
  } catch {
    return false
  }
}

// 常用图标组合
export const CommonIcons = {
  // 操作图标
  actions: {
    add: Plus,
    edit: Edit,
    delete: Delete,
    view: View,
    search: Search,
    refresh: Refresh,
    close: Close
  },
  
  // 文件图标
  files: {
    document: Document,
    folder: Folder,
    folderOpen: FolderOpened,
    upload: Upload,
    download: Download
  },
  
  // 导航图标
  navigation: {
    up: ArrowUp,
    down: ArrowDown,
    left: ArrowLeft,
    right: ArrowRight,
    expand: Expand,
    fold: Fold
  },
  
  // 功能图标
  features: {
    calendar: Calendar,
    clock: Clock,
    reading: Reading,
    setting: Setting,
    tools: Tools,
    lightning: Lightning
  }
} as const
