/** 统一处理 Cookie */

import <PERSON><PERSON><PERSON><PERSON> from "@/constants/cache-key"
import Cookies from "js-cookie"
import { Token } from "@/api/system/login/types"

export const getToken = () => {
  return Cookies.get(CacheKey.TOKEN)
}
export const setToken = (token: Token) => {
  // debugger
  Cookies.set(CacheKey.TOKEN, token.access_token, {
    expires: new Date(token.expires)
  })
}
export const removeToken = () => {
  Cookies.remove(CacheKey.TOKEN)
}
