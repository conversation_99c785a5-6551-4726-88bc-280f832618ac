/**
 * Page_UserList_
 */
export interface PageUserList {
  /**
   * Items
   */
  items: UserList[]
  /**
   * Page
   */
  page: number | null
  /**
   * Pages
   */
  pages?: number | null
  /**
   * Size
   */
  size: number | null
  /**
   * Total
   */
  total: number | null
  [property: string]: any
}

/**
 * UserList
 */
export interface UserList {
  /**
   * Create Time，创建时间
   */
  create_time?: null
  /**
   * Id，主键
   */
  id?: number
  /**
   * Is Super，是否管理员
   */
  is_super?: boolean | null
  /**
   * Last Login，最后登录时间
   */
  last_login?: null
  /**
   * Nickname
   */
  nickname?: null
  /**
   * Roles
   */
  roles?: RoleSchemaRelation[]
  /**
   * Status，状态:True=启用,False=禁用
   */
  status?: boolean | null
  /**
   * Update Time，更新时间
   */
  update_time?: null
  /**
   * Username，用户名
   */
  username?: string
  [property: string]: any
}

/**
 * RoleSchemaRelation
 */
export interface RoleSchemaRelation {
  /**
   * Id，主键
   */
  id?: number
  /**
   * Name，角色名称
   */
  name?: string
  [property: string]: any
}

/**
 * UserSchemaFilters
 */
export interface UserSchemaFilters {
  /**
   * Status，状态:True=启用,False=禁用
   */
  status?: boolean | null
  /**
   * Username，用户名
   */
  username?: string
  [property: string]: any
}
