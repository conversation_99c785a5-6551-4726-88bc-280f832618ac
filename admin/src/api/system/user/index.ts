import Crud, { QueryParams } from "@/api/crud"
import { UserSchemaFilters, PageUserList } from "./types"
import { MenuTree } from "@/api/system/menu/types"
import { request } from "@/utils/service"

class User extends Crud {
  /**
   * 获取列表
   * @param queryParams
   * @param filters
   */
  async list(queryParams: QueryParams = {}, filters: UserSchemaFilters = {}): Promise<PageUserList> {
    return await super.list(queryParams, filters)
  }

  async getUserMenu(): Promise<MenuTree[]> {
    return await request({
      url: `${this.baseUrl}/usermenu`,
      method: "get"
    })
  }

  async getUserApi(): Promise<string[]> {
    return await request({
      url: `${this.baseUrl}/userapi`,
      method: "get"
    })
  }
}

export default new User("/system/user")
