import Crud, { QueryParams } from "@/api/crud"
import { PageApiSchemaRead, ApiSchemaFilters, ApiSchemaRead } from "./types"
import { request } from "@/utils/service"

class Api extends Crud {
  /**
   * 获取列表
   * @param queryParams
   * @param filters
   */
  async list(queryParams: QueryParams = {}, filters: ApiSchemaFilters = {}): Promise<PageApiSchemaRead> {
    return await super.list(queryParams, filters)
  }

  async refreshApi() {
    return await request({
      method: "post",
      url: `${this.baseUrl}/refresh`
    })
  }

  async getPermissionList(): Promise<ApiSchemaRead[]> {
    return await request({
      method: "post",
      url: `${this.baseUrl}/permission_list`
    })
  }
}

export default new Api("/system/api")
