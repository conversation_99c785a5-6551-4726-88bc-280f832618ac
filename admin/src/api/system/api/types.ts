/**
 * Page_ApiSchemaRead_
 */
export interface PageApiSchemaRead {
  /**
   * Items
   */
  items: ApiSchemaRead[]
  /**
   * Page
   */
  page: number | null
  /**
   * Pages
   */
  pages?: number | null
  /**
   * Size
   */
  size: number | null
  /**
   * Total
   */
  total: number | null
  [property: string]: any
}

/**
 * ApiSchemaRead
 */
export interface ApiSchemaRead {
  /**
   * Create Time，创建时间
   */
  create_time?: null
  /**
   * Id，主键
   */
  id?: number
  /**
   * Method，请求方法
   */
  method?: string
  /**
   * Path，API路径
   */
  path?: string
  /**
   * Status，状态:True=启用,False=禁用
   */
  status?: boolean | null
  /**
   * Summary，请求简介
   */
  summary?: string
  /**
   * Tags，API标签
   */
  tags?: string
  /**
   * Update Time，更新时间
   */
  update_time?: null
  [property: string]: any
}
/**
 * ApiSchemaFilters
 */
export interface ApiSchemaFilters {
  /**
   * Create Time，创建时间
   */
  create_time?: null
  /**
   * Method，请求方法
   */
  method?: string
  /**
   * Path，API路径
   */
  path?: string
  /**
   * Status，状态:True=启用,False=禁用
   */
  status?: boolean | null
  /**
   * Summary，请求简介
   */
  summary?: string
  /**
   * Tags，API标签
   */
  tags?: string
  /**
   * Update Time，更新时间
   */
  update_time?: null
  [property: string]: any
}
