/**
 * Page_AuditLogSchemaRead_
 */
export interface PageAuditLogSchemaRead {
  /**
   * Items
   */
  items: AuditLogSchemaRead[]
  /**
   * Page
   */
  page: number | null
  /**
   * Pages
   */
  pages?: number | null
  /**
   * Size
   */
  size: number | null
  /**
   * Total
   */
  total: number | null
  [property: string]: any
}

/**
 * AuditLogSchemaRead
 */
export interface AuditLogSchemaRead {
  /**
   * Create Time，创建时间
   */
  create_time?: null
  /**
   * Id，主键
   */
  id?: number
  /**
   * Ip，请求IP
   */
  ip?: null | string
  /**
   * Method，请求方法
   */
  method?: null | string
  /**
   * Path，请求路径
   */
  path?: null | string
  /**
   * Referer，请求来源
   */
  referer?: null | string
  /**
   * Response Time，响应时间(单位ms)
   */
  response_time?: number | null
  /**
   * Status，状态码
   */
  status?: number | null
  /**
   * Summary，请求描述
   */
  summary?: null | string
  /**
   * Tags，功能模块
   */
  tags?: null | string
  /**
   * Update Time，更新时间
   */
  update_time?: null
  /**
   * User Agent，请求UA
   */
  user_agent?: null | string
  /**
   * User Id，用户ID
   */
  user_id?: null
  /**
   * Username，用户名称
   */
  username?: null
  [property: string]: any
}

/**
 * AuditLogSchemaFilters
 */
export interface AuditLogSchemaFilters {
  /**
   * Ip，请求IP
   */
  ip?: null | string
  /**
   * Path，请求路径
   */
  path?: null | string
  /**
   * Status，状态码
   */
  status?: number | null
  /**
   * Tags，功能模块
   */
  tags?: null | string
  /**
   * Username，用户名称
   */
  username?: null
  [property: string]: any
}
