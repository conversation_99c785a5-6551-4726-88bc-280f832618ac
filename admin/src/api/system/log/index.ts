import Crud, { QueryParams } from "@/api/crud"
import { PageAuditLogSchemaRead, AuditLogSchemaFilters } from "./types"

class Log extends Crud {
  /**
   * 获取列表
   * @param queryParams
   * @param filters
   */
  async list(queryParams: QueryParams = {}, filters: AuditLogSchemaFilters = {}): Promise<PageAuditLogSchemaRead> {
    return await super.list(queryParams, filters)
  }
}

export default new Log("/system/log")
