/**
 * Page_MenuSchemaRead_
 */
export interface PageMenuSchemaRead {
  /**
   * Items
   */
  items: MenuSchemaRead[]
  /**
   * Page
   */
  page: number | null
  /**
   * Pages
   */
  pages?: number | null
  /**
   * Size
   */
  size: number | null
  /**
   * Total
   */
  total: number | null

  [property: string]: any
}

/**
 * MenuSchemaRead
 */
/**
 * MenuSchemaRead
 */
export interface MenuSchemaRead {
  /**
   * Affix，默认 false，如果设置为 true，它则会固定在 tags-view 中
   */
  affix?: boolean | null
  /**
   * Alwaysshow，默认 false，如果设置为 true，它则会固定在 tags-view 中
   */
  always_show?: boolean | null
  /**
   * Breadcrumb，默认 true，如果设置为 false，则不会在面包屑中显示
   */
  breadcrumb?: boolean | null
  /**
   * Component，组件
   */
  component?: string
  /**
   * Create Time，创建时间
   */
  create_time?: null
  /**
   * Hidden，是否隐藏
   */
  hidden?: boolean | null
  /**
   * Icon，菜单图标
   */
  icon?: null
  /**
   * Id，主键
   */
  id?: number
  /**
   * Keepalive，存活
   */
  keepalive?: boolean | null
  /**
   * Menu Type，菜单类型
   */
  menu_type: string
  /**
   * Name，路由名称
   */
  name?: string
  /**
   * Order，排序
   */
  order?: number | null
  /**
   * Parent Id，父菜单ID
   */
  parent_id?: number | null
  /**
   * Path，菜单路径
   */
  path?: string
  /**
   * Redirect，重定向
   */
  redirect?: null
  /**
   * Status，状态:True=启用,False=禁用
   */
  status?: boolean | null
  /**
   * Svgicon，设置该路由的图标，记得将 svg 导入 @/icons/svg
   */
  svg_icon?: null
  /**
   * Title，菜单名称
   */
  title?: string
  /**
   * Update Time，更新时间
   */
  update_time?: null
  [property: string]: any
}

export interface MenuTree extends MenuSchemaRead {
  children: MenuTree[]
}
