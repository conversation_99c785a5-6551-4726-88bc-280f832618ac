import Crud, { QueryParams } from "@/api/crud"
import { request } from "@/utils/service"
import { MenuTree, PageMenuSchemaRead } from "./types"

class Menu extends Crud {
  /**
   * 获取列表
   * @param queryParams
   * @param filters
   */
  async list(queryParams: QueryParams = {}, filters = {}): Promise<PageMenuSchemaRead> {
    return await super.list(queryParams, filters)
  }
  async getMenuTree(): Promise<MenuTree[]> {
    return await request({
      url: "/system/menu/tree",
      method: "get"
    })
  }
}

export default new Menu("/system/menu")
