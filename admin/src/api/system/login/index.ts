import { request } from "@/utils/service"
import { UserSchemaLogin, Token, ChangePasswordSchema } from "./types"
import { UserList } from "@/api/system/user/types"

/** 登录并返回 Token */
export async function loginApi(data: UserSchemaLogin): Promise<Token> {
  return request({
    url: "system/login/login",
    method: "post",
    data
  })
}

// 获取个人信息
export async function getUserInfoApi(): Promise<UserList> {
  return request({
    url: "system/login/me",
    method: "get"
  })
}

export async function changePassword(data: ChangePasswordSchema) {
  return request({
    url: "system/login/update_password",
    method: "post",
    data
  })
}
