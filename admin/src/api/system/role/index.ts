import Crud, { QueryParams } from "@/api/crud"
import { RoleSchemaFilters, PageRoleList, RoleUpdateMenusApis, RolePermissionSchema } from "./types"
import { request } from "@/utils/service"

class Role extends Crud {
  /**
   * 获取列表
   * @param queryParams
   * @param filters
   */
  async list(queryParams: QueryParams = {}, filters: RoleSchemaFilters = {}): Promise<PageRoleList> {
    return await super.list(queryParams, filters)
  }

  async getRolePermissions(role_id: number): Promise<RolePermissionSchema> {
    return await request({
      method: "get",
      url: `/system/role/authorized`,
      params: {
        role_id
      }
    })
  }

  async updateRolePermissions(data: RoleUpdateMenusApis): Promise<any> {
    return await request({
      method: "put",
      url: `${this.baseUrl}/authorized`,
      data
    })
  }
}

export default new Role("/system/role")
