import { UserList } from "@/api/system/user/types"
import { ApiSchemaRead } from "@/api/system/api/types"
import { MenuSchemaRead } from "@/api/system/menu/types"

/**
 * Page_RoleList_
 */
export interface PageRoleList {
  /**
   * Items
   */
  items: RoleList[]
  /**
   * Page
   */
  page: number | null
  /**
   * Pages
   */
  pages?: number | null
  /**
   * Size
   */
  size: number | null
  /**
   * Total
   */
  total: number | null

  [property: string]: any
}

/**
 * RoleList
 */
export interface RoleList {
  /**
   * Create Time，创建时间
   */
  create_time?: null
  /**
   * Desc，角色描述
   */
  desc?: null
  /**
   * Id，主键
   */
  id?: number
  /**
   * Name，角色名称
   */
  name?: string
  /**
   * Status，状态:True=启用,False=禁用
   */
  status?: boolean | null
  /**
   * Update Time，更新时间
   */
  update_time?: null

  [property: string]: any
}

/**
 * RoleUpdateMenusApis
 * 更新角色菜单和api
 */
export interface RoleUpdateMenusApis {
  /**
   * Api Ids
   */
  api_ids?: number[]
  /**
   * Id
   */
  id: number
  /**
   * Menu Ids
   */
  menu_ids?: number[]

  [property: string]: any
}

/**
 * RoleSchemaFilters
 */
export interface RoleSchemaFilters {
  /**
   * Status，状态:True=启用,False=禁用
   */
  status?: boolean | null

  [property: string]: any
}

export interface RolePermissionSchema extends RoleList {
  users: UserList[]
  apis: ApiSchemaRead[]
  menus: MenuSchemaRead[]
}
