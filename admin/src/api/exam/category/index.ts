import Crud, { QueryParams } from "@/api/crud"
import { CategorySchemaFilters, PageCategorySchemaRead } from "./types"

class Category extends Crud {
  /**
   * 获取列表
   * @param queryParams
   * @param filters
   */
  async list(queryParams: QueryParams = {}, filters: CategorySchemaFilters = {}): Promise<PageCategorySchemaRead> {
    return await super.list(queryParams, filters)
  }
}

export default new Category("/category")
