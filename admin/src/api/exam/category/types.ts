/**
 * Page_CategorySchemaRead_
 */
export interface PageCategorySchemaRead {
  /**
   * Items
   */
  items: CategorySchemaRead[]
  /**
   * Page
   */
  page: number | null
  /**
   * Pages
   */
  pages?: number | null
  /**
   * Size
   */
  size: number | null
  /**
   * Total
   */
  total: number | null

  [property: string]: any
}

/**
 * CategorySchemaRead
 */
export interface CategorySchemaRead {
  /**
   * Category，题目分类
   */
  category?: string
  /**
   * Create Time，创建时间
   */
  create_time?: null
  /**
   * Grade，年级：一年级
   */
  grade?: string
  /**
   * Id，主键
   */
  id?: number
  /**
   * 学期
   */
  semester: Semester
  /**
   * Status，状态:True=启用,False=禁用
   */
  status?: boolean | null
  /**
   * Subject，学科：语文
   */
  subject?: string
  /**
   * Update Time，更新时间
   */
  update_time?: null
}

/**
 * 学期
 *
 * SemesterSchemaRelation
 */
export interface Semester {
  /**
   * Id，主键
   */
  id?: number
  /**
   * Semester，2024春期
   */
  semester?: string

  [property: string]: any
}

/**
 * CategorySchemaFilters
 */
export interface CategorySchemaFilters {
  /**
   * Category，题目分类
   */
  category?: string
  /**
   * Grade，年级：一年级
   */
  grade?: string
  /**
   * Status，状态:True=启用,False=禁用
   */
  status?: boolean | null
  /**
   * Subject，学科：语文
   */
  subject?: string
  /**
   * 学期
   */
  semester?: number
}
