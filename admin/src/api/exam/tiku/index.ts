import Crud, { QueryParams } from "@/api/crud"
import { PageTikuSchemaRead, TikuSchemaFilters } from "./types"
import { request } from "@/utils/service"

class Tiku extends Crud {
  /**
   * 获取列表
   * @param queryParams
   * @param filters
   */
  // @ts-ignore
  async list(queryParams: QueryParams = {}, filters: TikuSchemaFilters = {}): Promise<PageTikuSchemaRead> {
    return await super.list(queryParams, filters)
  }

  async deleteCdn(ids: string) {
    return await request({
      method: "post",
      url: `${this.baseUrl}/cdn_delete`,
      params: {
        ids
      }
    })
  }

  async uploadImage(formData: FormData) {
    return await request({
      method: "post",
      url: `${this.baseUrl}/sync_cdn_file`,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  async getStatistics() {
    return await request({
      method: "get",
      url: `${this.baseUrl}/statistics`
    })
  }
}

export default new Tiku("/tiku")
