import Crud, { QueryParams } from "@/api/crud"
import { PageTikuSchemaRead, TikuSchemaFilters } from "./types"
import { request } from "@/utils/service"

class Tiku extends Crud {
  /**
   * 获取列表
   * @param queryParams
   * @param filters
   */
  // @ts-ignore
  async list(queryParams: QueryParams = {}, filters: TikuSchemaFilters = {}): Promise<PageTikuSchemaRead> {
    return await super.list(queryParams, filters)
  }

  async deleteCdn(ids: string) {
    return await request({
      method: "post",
      url: `${this.baseUrl}/cdn_delete`,
      params: {
        ids
      }
    })
  }
}

export default new Tiku("/tiku")
