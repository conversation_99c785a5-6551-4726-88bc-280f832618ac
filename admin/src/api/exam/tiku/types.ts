/**
 * Page_TikuSchemaRead_
 */
export interface PageTikuSchemaRead {
  /**
   * Items
   */
  items: TikuSchemaRead[]
  /**
   * Page
   */
  page: number | null
  /**
   * Pages
   */
  pages?: number | null
  /**
   * Size
   */
  size: number | null
  /**
   * Total
   */
  total: number | null
  [property: string]: any
}
/**
 * TikuSchemaRead
 */
export interface TikuSchemaRead {
  /**
   * Created，创建时间
   */
  create_time?: Date | null
  /**
   * Id
   */
  id: number
  /**
   * 用户状态
   */
  status?: boolean
  /**
   * Updated，更新时间
   */
  update_time?: Date | null
  semester?: string
  grade?: string
  subject?: string
  category?: string
  content?: string
}

export interface TikuSchemaFilters {
  semester?: string
  grade?: string
  subject?: string
  category?: string
  status: boolean
}
