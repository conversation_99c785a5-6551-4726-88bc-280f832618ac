import Crud, { QueryParams } from "@/api/crud"
import { PageSemesterSchemaList, SemesterSchemaFilters } from "./types"

class Semester extends Crud {
  /**
   * 获取列表
   * @param queryParams
   * @param filters
   */
  async list(queryParams: QueryParams = {}, filters: SemesterSchemaFilters = {}): Promise<PageSemesterSchemaList> {
    return await super.list(queryParams, filters)
  }
}

export default new Semester("/semester")
