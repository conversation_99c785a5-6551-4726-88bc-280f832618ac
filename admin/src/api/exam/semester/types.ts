/**
 * Page_SemesterSchemaList_
 */
export interface PageSemesterSchemaList {
  /**
   * Items
   */
  items: SemesterSchemaRead[]
  /**
   * Page
   */
  page: number | null
  /**
   * Pages
   */
  pages?: number | null
  /**
   * Size
   */
  size: number | null
  /**
   * Total
   */
  total: number | null
  [property: string]: any
}
/**
 * SemesterSchemaRead
 */
export interface SemesterSchemaRead {
  /**
   * Created，创建时间
   */
  create_time?: Date | null
  /**
   * Id
   */
  id: number
  /**
   * 用户状态
   */
  status?: boolean
  /**
   * Updated，更新时间
   */
  update_time?: Date | null
  semester?: string
  year?: string
  season?: string
}

export interface SemesterSchemaFilters {
  semester?: string
  season?: string
  year?: string
  status?: boolean
}
