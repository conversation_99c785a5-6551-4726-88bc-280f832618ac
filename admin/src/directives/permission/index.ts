import { App } from "vue"
import { usePermissionStore, useUserStore } from "@/store"

/**
 * 检测当前api是否有权限
 * @param permission get/api/v1/user/list
 */
export function hasPermission(permission: string) {
  const userStore = useUserStore()
  const userPermissionStore = usePermissionStore()
  const accessApis = userPermissionStore.apis
  if (userStore.isSuperUser) {
    return true
  }
  return accessApis.includes(permission)
}

/** 权限指令，和权限判断函数 checkPermission 功能类似 */
export function setupPermissionDirective(app: App) {
  function updateElVisible(el: Element, permission: string) {
    if (!permission) {
      throw new Error(`need roles: like v-permission="get/api/v1/user/list"`)
    }
    if (!hasPermission(permission)) {
      el.parentElement?.removeChild(el)
    }
  }

  const permissionDirective = {
    mounted(el: Element, binding: any) {
      updateElVisible(el, binding.value)
    },
    beforeUpdate(el: Element, binding: any) {
      updateElVisible(el, binding.value)
    }
  }

  app.directive("permission", permissionDirective)
}
